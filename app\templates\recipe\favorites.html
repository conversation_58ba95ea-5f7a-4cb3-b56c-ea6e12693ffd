{% extends 'base.html' %}

{% block title %}我的收藏食谱{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .recipe-card {
        height: 100%;
        transition: transform 0.3s;
    }
    .recipe-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .recipe-image {
        height: 180px;
        object-fit: cover;
    }
    .recipe-image-placeholder {
        height: 180px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
    }
    .favorite-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 1.5rem;
        color: #dc3545;
        cursor: pointer;
        z-index: 10;
    }
    .favorite-date {
        font-size: 0.8rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">我的收藏食谱</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('recipe.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-list"></i> 所有食谱
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if favorite_recipes %}
                    <div class="row">
                        {% for item in favorite_recipes %}
                        <div class="col-md-3 mb-4">
                            <div class="card recipe-card">
                                <div class="favorite-icon remove-favorite" data-recipe-id="{{ item.recipe.id }}">
                                    <i class="fas fa-heart"></i>
                                </div>
                                {% if item.recipe.main_image %}
                                <img src="{{ url_for('static', filename=item.recipe.main_image) }}" alt="{{ item.recipe.name }}" class="card-img-top recipe-image">
                                {% else %}
                                <div class="recipe-image-placeholder">
                                    <i class="fas fa-utensils fa-3x text-muted"></i>
                                </div>
                                {% endif %}
                                <div class="card-body">
                                    <h5 class="card-title">{{ item.recipe.name }}</h5>
                                    <p class="card-text">
                                        <span class="badge badge-primary">{{ item.recipe.category }}</span>
                                        {% if item.recipe.meal_type %}
                                        <span class="badge badge-info">{{ item.recipe.meal_type }}</span>
                                        {% endif %}
                                    </p>
                                    <p class="favorite-date">收藏于: {{ item.favorite_date|format_datetime('%Y-%m-%d') }}</p>
                                </div>
                                <div class="card-footer">
                                    <a href="{{ url_for('recipe.view', id=item.recipe.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> 查看详情
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <div class="d-flex justify-content-center mt-4">
                        <ul class="pagination">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('recipe_favorite.index', page=pagination.prev_num) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                            </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num == pagination.page %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('recipe_favorite.index', page=page_num) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('recipe_favorite.index', page=pagination.next_num) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-heart-broken fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">您还没有收藏任何食谱</h4>
                        <p class="text-muted">浏览食谱列表，点击心形图标收藏您喜欢的食谱</p>
                        <a href="{{ url_for('recipe.index') }}" class="btn btn-primary mt-3">
                            <i class="fas fa-search"></i> 浏览食谱
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 取消收藏
        $('.remove-favorite').click(function() {
            var recipeId = $(this).data('recipe-id');
            var card = $(this).closest('.col-md-3');
            
            if (confirm('确定要取消收藏这个食谱吗？')) {
                $.ajax({
                    url: '/api/toggle-favorite/' + recipeId,
                    type: 'POST',
                    success: function(response) {
                        if (response.status === 'success') {
                            // 动画效果移除卡片
                            card.fadeOut(300, function() {
                                $(this).remove();
                                
                                // 检查是否还有收藏的食谱
                                if ($('.recipe-card').length === 0) {
                                    location.reload(); // 刷新页面显示空状态
                                }
                            });
                        }
                    }
                });
            }
        });
    });
</script>
{% endblock %}
