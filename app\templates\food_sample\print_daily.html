<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每日留样记录打印</title>
    <style nonce="{{ csp_nonce }}">
        body {
            font-family: SimSun, "宋体", "Microsoft YaHei", "微软雅黑", sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 14pt;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .title {
            font-size: 24pt;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 18pt;
            margin-bottom: 20px;
        }
        .info {
            margin-bottom: 20px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #f2f2f2;
        }
        .meal-type {
            font-weight: bold;
            font-size: 16pt;
            margin: 20px 0 10px 0;
        }
        .image-container {
            text-align: center;
            margin: 10px 0;
        }
        .image-container img {
            max-width: 100px;
            max-height: 100px;
            border: 1px solid #ddd;
        }
        .footer {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
        }
        .signature {
            margin-top: 50px;
        }
        @media print {
            body {
                padding: 0;
            }
            @page {
                size: A4;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">{{ project_name|default('初中毕业生学生去向管理系统') }}</div>
            <div class="subtitle">每日食品留样记录汇总表</div>
        </div>

        <div class="info">
            <div class="info-item"><strong>日期：</strong>{{ meal_date }}</div>
            {% if area %}
            <div class="info-item"><strong>区域：</strong>{{ area.name }}</div>
            {% endif %}
        </div>

        {% set meal_types = ['早餐', '午餐', '晚餐'] %}
        {% for meal_type in meal_types %}
            {% set meal_samples = food_samples|selectattr('meal_type', 'equalto', meal_type)|list %}
            {% if meal_samples %}
            <div class="meal-type">{{ meal_type }}</div>
            <table>
                <thead>
                    <tr>
                        <th style="width: 5%">序号</th>
                        <th class="w-15">留样编号</th>
                        <th class="w-25">食谱名称</th>
                        <th class="w-15">留样数量</th>
                        <th class="w-15">留样时间</th>
                        <th class="w-15">操作员</th>
                        <th style="width: 10%">图片</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sample in meal_samples %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ sample.sample_number }}</td>
                        <td>{{ sample.recipe.name }}</td>
                        <td>{{ sample.sample_quantity }} {{ sample.sample_unit }}</td>
                        <td>{{  sample.start_time|format_datetime('%H:%M')  }}</td>
                        <td>{{ sample.operator.real_name or sample.operator.username }}</td>
                        <td>
                            {% if sample.sample_image %}
                            <div class="image-container">
                                <img src="{{ url_for('static', filename=sample.sample_image, _external=True) }}" alt="留样图片">
                            </div>
                            {% else %}
                            无图片
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        {% endfor %}

        <div class="footer">
            <div>
                <div><strong>留样负责人：</strong>________________</div>
                <div class="signature">签名：________________</div>
            </div>
            <div>
                <div><strong>食堂负责人：</strong>________________</div>
                <div class="signature">签名：________________</div>
            </div>
            <div>
                <div><strong>日期：</strong>{{ meal_date }}</div>
            </div>
        </div>
    </div>

    <script nonce="{{ csp_nonce }}">
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
