{% extends 'base.html' %}

{% block title %}食材分类管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">食材分类管理</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('ingredient_category.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 添加分类
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">分类树</h3>
                                </div>
                                <div class="card-body">
                                    <div id="category-tree"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">分类列表</h3>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>名称</th>
                                                <th>父分类</th>
                                                <th>描述</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for category in categories %}
                                            <tr>
                                                <td>{{ category.id }}</td>
                                                <td>{{ category.name }}</td>
                                                <td>
                                                    {% if category.parent %}
                                                        {{ category.parent.name }}
                                                    {% else %}
                                                        -
                                                    {% endif %}
                                                </td>
                                                <td>{{ category.description or '-' }}</td>
                                                <td>
                                                    <a href="{{ url_for('ingredient_category.edit', id=category.id) }}" class="btn btn-info btn-sm">
                                                        <i class="fas fa-edit"></i> 编辑
                                                    </a>
                                                    <button class="btn btn-danger btn-sm delete-btn" data-id="{{ category.id }}">
                                                        <i class="fas fa-trash"></i> 删除
                                                    </button>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                确定要删除这个食材分类吗？此操作不可逆。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jstree/jstree.min.js') }}"></script>
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/jstree/themes/default/style.min.css') }}" />
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 初始化分类树
        var categoryTree = {{  category_tree|safe  }};

        function transformData(data) {
            return data.map(function(item) {
                var node = {
                    id: item.id.toString(),
                    text: item.name,
                    state: { opened: true }
                };

                if (item.children && item.children.length > 0) {
                    node.children = transformData(item.children);
                }

                return node;
            });
        }

        $('#category-tree').jstree({
            'core': {
                'data': transformData(categoryTree)
            }
        });

        // 删除功能
        var deleteId = null;

        $('.delete-btn').click(function() {
            deleteId = $(this).data('id');
            $('#deleteModal').modal('show');
        });

        $('#confirmDelete').click(function() {
            if (deleteId) {
                $.ajax({
                    url: '{{ url_for("ingredient_category.delete", id=0) }}'.replace('0', deleteId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#deleteModal').modal('hide');
                    },
                    error: function() {
                        toastr.error('删除失败，请稍后重试！');
                        $('#deleteModal').modal('hide');
                    }
                });
            }
        });
    });
</script>
{% endblock %}
