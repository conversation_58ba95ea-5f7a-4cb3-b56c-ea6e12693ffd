{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('supplier_school.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                        <a href="{{ url_for('supplier.index') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-list"></i> 供应商列表
                        </a>
                    </div>
                </div>
                {% if title == '添加供应商-学校关系' %}
                <div class="card-body bg-light">
                    <div class="alert alert-success mb-0">
                        <i class="fas fa-handshake"></i>
                        <strong>建立合作关系：</strong>请选择供应商和学校，填写合作信息以建立正式的供应关系。
                    </div>
                </div>
                {% endif %}
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.supplier_id.label }}
                                    {{ form.supplier_id(class="form-control") }}
                                    {% for error in form.supplier_id.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.area_id.label }}
                                    {{ form.area_id(class="form-control") }}
                                    {% for error in form.area_id.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.contract_number.label }}
                                    <div class="input-group">
                                        {{ form.contract_number(class="form-control", id="contract_number") }}
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-outline-secondary" id="generateContractBtn">
                                                <i class="fas fa-sync-alt"></i> 重新生成
                                            </button>
                                        </div>
                                    </div>
                                    <small class="text-muted">系统自动生成，可手动修改</small>
                                    {% for error in form.contract_number.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.start_date.label }}
                                    {{ form.start_date(class="form-control datepicker") }}
                                    {% for error in form.start_date.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.end_date.label }}
                                    {{ form.end_date(class="form-control datepicker") }}
                                    <small class="text-muted">留空表示长期合作</small>
                                    {% for error in form.end_date.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.status.label }}
                                    {{ form.status(class="form-control") }}
                                    {% for error in form.status.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            {{ form.notes.label }}
                            {{ form.notes(class="form-control", rows=3) }}
                            {% for error in form.notes.errors %}
                            <small class="text-danger">{{ error }}</small>
                            {% endfor %}
                        </div>
                        <div class="form-group">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('supplier_school.index') }}" class="btn btn-secondary">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 日期选择器
        $('.datepicker').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            todayHighlight: true,
            language: 'zh-CN'
        });

        // 如果URL中有supplier_id参数，自动选择对应的供应商
        var urlParams = new URLSearchParams(window.location.search);
        var supplierIdParam = urlParams.get('supplier_id');
        if (supplierIdParam) {
            $('#supplier_id').val(supplierIdParam);
        }

        // 如果学校下拉框为空（值为0），则自动选择第一个有效选项
        if ($('#area_id').val() == 0) {
            // 获取第一个有效的选项值（跳过"请选择学校"选项）
            var firstOption = $('#area_id option:eq(1)').val();
            if (firstOption) {
                $('#area_id').val(firstOption);
                // 触发change事件，以便其他依赖于此选择的逻辑能够执行
                $('#area_id').trigger('change');
            }
        }

        // 生成合同编号函数
        function generateContractNumber() {
            // 获取当前日期
            var now = new Date();
            var year = now.getFullYear();
            var month = (now.getMonth() + 1).toString().padStart(2, '0');
            var day = now.getDate().toString().padStart(2, '0');

            // 生成随机数
            var randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

            // 组合合同编号：C + 年月日 + 4位随机数
            return 'C' + year + month + day + randomNum;
        }

        // 如果合同编号为空，自动生成一个
        if (!$('#contract_number').val()) {
            $('#contract_number').val(generateContractNumber());
        }

        // 重新生成按钮点击事件
        $('#generateContractBtn').click(function() {
            $('#contract_number').val(generateContractNumber());
        });

        // 供应商和学校选择变化时，可以考虑重新生成合同编号
        $('#supplier_id, #area_id').change(function() {
            // 只有当两者都已选择且合同编号为空或用户确认时才重新生成
            var supplierId = $('#supplier_id').val();
            var areaId = $('#area_id').val();

            if (supplierId > 0 && areaId > 0) {
                var currentContractNumber = $('#contract_number').val();
                if (!currentContractNumber || confirm('是否重新生成合同编号？')) {
                    $('#contract_number').val(generateContractNumber());
                }
            }
        });
    });
</script>
{% endblock %}
