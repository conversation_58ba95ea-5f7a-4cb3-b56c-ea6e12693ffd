{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="card-title mb-0">供应商产品管理</h3>
                            <small class="text-muted">管理供应商产品的审核、上架和下架操作</small>
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('product_batch.index') }}" class="btn btn-success btn-sm">
                                <i class="fas fa-layer-group"></i> 批次管理
                            </a>
                            <a href="{{ url_for('supplier_product.create') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 添加产品
                            </a>
                            <a href="{{ url_for('supplier.index') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left"></i> 返回供应商列表
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="supplier_id">供应商</label>
                                    <select class="form-control" id="supplier_id" name="supplier_id">
                                        <option value="">-- 所有供应商 --</option>
                                        {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}" {% if supplier_id == supplier.id %}selected{% endif %}>
                                            {{ supplier.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="ingredient_id">食材</label>
                                    <select class="form-control" id="ingredient_id" name="ingredient_id">
                                        <option value="">-- 所有食材 --</option>
                                        {% for ingredient in ingredients %}
                                        <option value="{{ ingredient.id }}" {% if ingredient_id == ingredient.id %}selected{% endif %}>
                                            {{ ingredient.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="status">状态</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="">-- 所有状态 --</option>
                                        <option value="1" {% if status == 1 %}selected{% endif %}>已上架</option>
                                        <option value="0" {% if status == 0 %}selected{% endif %}>未上架</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="keyword">关键词</label>
                                    <input type="text" class="form-control" id="keyword" name="keyword" value="{{ keyword }}" placeholder="产品名称/型号">
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-block">搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 产品列表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>产品名称</th>
                                    <th>供应商</th>
                                    <th>食材</th>
                                    <th>型号/规格</th>
                                    <th>单价</th>
                                    <th>审核状态</th>
                                    <th>上架状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in products %}
                                <tr>
                                    <td>{{ product.id }}</td>
                                    <td>{{ product.product_name or product.ingredient.name }}</td>
                                    <td>{{ product.supplier.name }}</td>
                                    <td>{{ product.ingredient.name }}</td>
                                    <td>
                                        {% if product.model_number %}
                                        <span class="badge badge-info">{{ product.model_number }}</span>
                                        {% endif %}
                                        {% if product.specification %}
                                        <span class="badge badge-secondary">{{ product.specification }}</span>
                                        {% endif %}
                                    </td>
                                    <td>¥{{ product.price }}</td>
                                    <td>
                                        {% if product.shelf_status == 0 %}
                                        <span class="badge badge-warning">待审核</span>
                                        {% elif product.shelf_status == 1 %}
                                        <span class="badge badge-success">已审核</span>
                                        {% else %}
                                        <span class="badge badge-danger">已拒绝</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if product.is_available == 1 %}
                                        <span class="badge badge-success">已上架</span>
                                        {% else %}
                                        <span class="badge badge-secondary">未上架</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('supplier_product.view', id=product.id) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('supplier_product.edit', id=product.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if product.shelf_status == 1 and product.is_available == 0 %}
                                            <button type="button" class="btn btn-sm btn-success shelf-btn" data-id="{{ product.id }}">
                                                <i class="fas fa-arrow-up"></i>
                                            </button>
                                            {% elif product.is_available == 1 %}
                                            <button type="button" class="btn btn-sm btn-warning unshelf-btn" data-id="{{ product.id }}">
                                                <i class="fas fa-arrow-down"></i>
                                            </button>
                                            {% endif %}
                                            {% if product.shelf_status == 0 %}
                                            <button type="button" class="btn btn-sm btn-success approve-btn" data-id="{{ product.id }}">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger reject-btn" data-id="{{ product.id }}">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% endif %}
                                            <button type="button" class="btn btn-sm btn-danger delete-btn" data-id="{{ product.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="9" class="text-center">暂无产品数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('supplier_product.index', page=pagination.prev_num, supplier_id=supplier_id, ingredient_id=ingredient_id, status=status, keyword=keyword) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                            </li>
                            {% endif %}

                            {% for page in pagination.iter_pages() %}
                                {% if page %}
                                    {% if page != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('supplier_product.index', page=page, supplier_id=supplier_id, ingredient_id=ingredient_id, status=status, keyword=keyword) }}">{{ page }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('supplier_product.index', page=pagination.next_num, supplier_id=supplier_id, ingredient_id=ingredient_id, status=status, keyword=keyword) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                确定要删除这个产品吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 拒绝原因模态框 -->
<div class="modal fade" id="rejectModal" tabindex="-1" role="dialog" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectModalLabel">拒绝原因</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="rejectForm">
                    <div class="form-group">
                        <label for="rejectReason">请输入拒绝原因</label>
                        <textarea class="form-control" id="rejectReason" rows="3" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmReject">确认拒绝</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 删除功能
        var deleteId = null;

        $('.delete-btn').click(function() {
            deleteId = $(this).data('id');
            $('#deleteModal').modal('show');
        });

        $('#confirmDelete').click(function() {
            if (deleteId) {
                $.ajax({
                    url: '{{ url_for("supplier_product.delete", id=0) }}'.replace('0', deleteId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#deleteModal').modal('hide');
                    },
                    error: function() {
                        toastr.error('删除失败，请稍后重试！');
                        $('#deleteModal').modal('hide');
                    }
                });
            }
        });

        // 上架功能
        $('.shelf-btn').click(function() {
            var productId = $(this).data('id');
            $.ajax({
                url: '{{ url_for("supplier_product.shelf_product", id=0) }}'.replace('0', productId),
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('上架失败，请稍后重试！');
                }
            });
        });

        // 下架功能
        $('.unshelf-btn').click(function() {
            var productId = $(this).data('id');
            $.ajax({
                url: '{{ url_for("supplier_product.unshelf_product", id=0) }}'.replace('0', productId),
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('下架失败，请稍后重试！');
                }
            });
        });

        // 审核通过功能
        $('.approve-btn').click(function() {
            var productId = $(this).data('id');
            $.ajax({
                url: '{{ url_for("supplier_product.approve_product", id=0) }}'.replace('0', productId),
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('审核失败，请稍后重试！');
                }
            });
        });

        // 拒绝功能
        var rejectId = null;

        $('.reject-btn').click(function() {
            rejectId = $(this).data('id');
            $('#rejectModal').modal('show');
        });

        $('#confirmReject').click(function() {
            if (rejectId) {
                var reason = $('#rejectReason').val();
                if (!reason) {
                    toastr.error('请输入拒绝原因！');
                    return;
                }

                $.ajax({
                    url: '{{ url_for("supplier_product.reject_product", id=0) }}'.replace('0', rejectId),
                    type: 'POST',
                    data: {
                        reason: reason
                    },
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#rejectModal').modal('hide');
                    },
                    error: function() {
                        toastr.error('拒绝失败，请稍后重试！');
                        $('#rejectModal').modal('hide');
                    }
                });
            }
        });
    });
</script>
{% endblock %}
