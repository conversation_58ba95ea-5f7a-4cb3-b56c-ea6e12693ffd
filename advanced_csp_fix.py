#!/usr/bin/env python3
"""
高级 CSP 违规修复脚本
专门处理内联事件处理器和复杂的 CSP 违规情况
"""

import os
import re
import glob
import hashlib
from pathlib import Path

def scan_and_fix_inline_events():
    """扫描并修复内联事件处理器"""
    print("🔍 深度扫描内联事件处理器...")
    
    # 查找所有 HTML 模板文件
    html_files = glob.glob('app/templates/**/*.html', recursive=True)
    
    # 常见的内联事件属性
    event_patterns = {
        'onclick': r'onclick\s*=\s*["\']([^"\']*)["\']',
        'onload': r'onload\s*=\s*["\']([^"\']*)["\']',
        'onchange': r'onchange\s*=\s*["\']([^"\']*)["\']',
        'onsubmit': r'onsubmit\s*=\s*["\']([^"\']*)["\']',
        'onerror': r'onerror\s*=\s*["\']([^"\']*)["\']',
        'onkeyup': r'onkeyup\s*=\s*["\']([^"\']*)["\']',
        'onkeydown': r'onkeydown\s*=\s*["\']([^"\']*)["\']',
        'onfocus': r'onfocus\s*=\s*["\']([^"\']*)["\']',
        'onblur': r'onblur\s*=\s*["\']([^"\']*)["\']',
        'onmouseover': r'onmouseover\s*=\s*["\']([^"\']*)["\']',
        'onmouseout': r'onmouseout\s*=\s*["\']([^"\']*)["\']'
    }
    
    fixed_files = 0
    total_events_fixed = 0
    
    for file_path in html_files:
        print(f"\n🔍 扫描文件: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            events_in_file = 0
            
            # 检查每种事件类型
            for event_type, pattern in event_patterns.items():
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"  ⚠️ 发现 {len(matches)} 个 {event_type} 事件")
                    events_in_file += len(matches)
                    
                    # 为每个匹配生成修复建议
                    for i, match in enumerate(matches):
                        print(f"    - {event_type}: {match[:50]}...")
            
            if events_in_file > 0:
                # 生成修复后的文件
                fixed_content = generate_event_fix(content, file_path)
                if fixed_content != content:
                    # 保存修复后的文件
                    backup_path = file_path + '.backup'
                    with open(backup_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(fixed_content)
                    
                    fixed_files += 1
                    total_events_fixed += events_in_file
                    print(f"  ✅ 已修复并备份到 {backup_path}")
                else:
                    print(f"  ⚠️ 需要手动修复")
            else:
                print(f"  ✅ 无内联事件")
                
        except Exception as e:
            print(f"  ❌ 处理文件失败: {e}")
    
    print(f"\n📊 内联事件修复总结:")
    print(f"   修复文件数: {fixed_files}")
    print(f"   修复事件数: {total_events_fixed}")
    
    return fixed_files, total_events_fixed

def generate_event_fix(content, file_path):
    """生成事件处理器修复"""
    
    # 简单的修复策略：将常见的内联事件转换为外部脚本
    fixes = []
    
    # 修复 onclick="window.print()" 
    content = re.sub(
        r'onclick\s*=\s*["\']window\.print\(\)["\']',
        'class="print-button"',
        content,
        flags=re.IGNORECASE
    )
    
    # 修复 onclick="history.back()"
    content = re.sub(
        r'onclick\s*=\s*["\']history\.back\(\)["\']',
        'class="back-button"',
        content,
        flags=re.IGNORECASE
    )
    
    # 修复 onclick="location.reload()"
    content = re.sub(
        r'onclick\s*=\s*["\']location\.reload\(\)["\']',
        'class="reload-button"',
        content,
        flags=re.IGNORECASE
    )
    
    # 修复简单的函数调用
    def replace_simple_onclick(match):
        func_call = match.group(1)
        # 生成唯一的 data 属性
        unique_id = hashlib.md5(func_call.encode()).hexdigest()[:8]
        return f'data-onclick="{func_call}" data-event-id="{unique_id}"'
    
    content = re.sub(
        r'onclick\s*=\s*["\']([^"\']*\(\)[^"\']*)["\']',
        replace_simple_onclick,
        content,
        flags=re.IGNORECASE
    )
    
    # 添加通用事件处理脚本（如果还没有的话）
    if 'data-onclick=' in content and 'universal-event-handler.js' not in content:
        # 在文件末尾添加脚本引用
        script_tag = '\n<script nonce="{{ csp_nonce }}" src="{{ url_for(\'static\', filename=\'js/universal-event-handler.js\') }}"></script>'
        
        # 查找合适的插入位置
        if '</body>' in content:
            content = content.replace('</body>', script_tag + '\n</body>')
        elif '</html>' in content:
            content = content.replace('</html>', script_tag + '\n</html>')
        else:
            content += script_tag
    
    return content

def create_universal_event_handler():
    """创建通用事件处理器"""
    print("\n📝 创建通用事件处理器...")
    
    handler_script = '''/**
 * 通用事件处理器
 * 处理从内联事件迁移过来的事件处理
 */

(function() {
    'use strict';
    
    // 等待 DOM 加载完成
    document.addEventListener('DOMContentLoaded', function() {
        
        // 处理打印按钮
        document.querySelectorAll('.print-button').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                window.print();
            });
        });
        
        // 处理返回按钮
        document.querySelectorAll('.back-button').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                history.back();
            });
        });
        
        // 处理刷新按钮
        document.querySelectorAll('.reload-button').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                location.reload();
            });
        });
        
        // 处理通用 onclick 事件
        document.querySelectorAll('[data-onclick]').forEach(function(element) {
            const funcCall = element.getAttribute('data-onclick');
            element.addEventListener('click', function(e) {
                e.preventDefault();
                try {
                    // 安全地执行函数调用
                    if (typeof safeEval === 'function') {
                        safeEval(funcCall);
                    } else {
                        // 备用方案：使用 Function 构造器
                        new Function(funcCall)();
                    }
                } catch (error) {
                    console.error('事件处理器执行失败:', error, '函数:', funcCall);
                }
            });
        });
        
        // 处理表单提交事件
        document.querySelectorAll('[data-onsubmit]').forEach(function(form) {
            const funcCall = form.getAttribute('data-onsubmit');
            form.addEventListener('submit', function(e) {
                try {
                    if (typeof safeEval === 'function') {
                        const result = safeEval(funcCall);
                        if (result === false) {
                            e.preventDefault();
                        }
                    } else {
                        const result = new Function('return ' + funcCall)();
                        if (result === false) {
                            e.preventDefault();
                        }
                    }
                } catch (error) {
                    console.error('表单提交处理器执行失败:', error);
                    e.preventDefault();
                }
            });
        });
        
        // 处理 onchange 事件
        document.querySelectorAll('[data-onchange]').forEach(function(element) {
            const funcCall = element.getAttribute('data-onchange');
            element.addEventListener('change', function(e) {
                try {
                    if (typeof safeEval === 'function') {
                        safeEval(funcCall);
                    } else {
                        new Function(funcCall)();
                    }
                } catch (error) {
                    console.error('Change 事件处理器执行失败:', error);
                }
            });
        });
        
        console.log('✅ 通用事件处理器已初始化');
    });
    
    // 为动态添加的元素提供事件绑定
    window.bindUniversalEvents = function(container) {
        if (!container) container = document;
        
        // 重新绑定所有事件...
        // (这里可以复制上面的逻辑)
    };
    
})();'''
    
    # 保存通用事件处理器
    handler_path = 'app/static/js/universal-event-handler.js'
    os.makedirs(os.path.dirname(handler_path), exist_ok=True)
    
    with open(handler_path, 'w', encoding='utf-8') as f:
        f.write(handler_script)
    
    print(f"✅ 通用事件处理器已保存到: {handler_path}")

def scan_unsafe_inline_styles():
    """扫描不安全的内联样式"""
    print("\n🔍 扫描不安全的内联样式...")
    
    html_files = glob.glob('app/templates/**/*.html', recursive=True)
    
    style_violations = 0
    fixed_files = 0
    
    for file_path in html_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找 style 属性
            style_matches = re.findall(r'style\s*=\s*["\']([^"\']*)["\']', content, re.IGNORECASE)
            
            if style_matches:
                print(f"📄 {file_path}: 发现 {len(style_matches)} 个内联样式")
                style_violations += len(style_matches)
                
                # 尝试将内联样式转换为 CSS 类
                fixed_content = fix_inline_styles(content, file_path)
                
                if fixed_content != content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(fixed_content)
                    fixed_files += 1
                    print(f"  ✅ 已修复内联样式")
                
        except Exception as e:
            print(f"❌ 处理 {file_path} 失败: {e}")
    
    print(f"\n📊 内联样式扫描结果:")
    print(f"   发现内联样式: {style_violations}")
    print(f"   修复文件数: {fixed_files}")

def fix_inline_styles(content, file_path):
    """修复内联样式"""
    
    # 常见的内联样式修复
    replacements = {
        r'style\s*=\s*["\']display:\s*none["\']': 'class="d-none"',
        r'style\s*=\s*["\']display:\s*block["\']': 'class="d-block"',
        r'style\s*=\s*["\']text-align:\s*center["\']': 'class="text-center"',
        r'style\s*=\s*["\']text-align:\s*left["\']': 'class="text-left"',
        r'style\s*=\s*["\']text-align:\s*right["\']': 'class="text-right"',
        r'style\s*=\s*["\']margin-top:\s*20px["\']': 'class="mt-4"',
        r'style\s*=\s*["\']margin-bottom:\s*20px["\']': 'class="mb-4"',
        r'style\s*=\s*["\']padding:\s*10px["\']': 'class="p-2"',
        r'style\s*=\s*["\']color:\s*red["\']': 'class="text-danger"',
        r'style\s*=\s*["\']color:\s*green["\']': 'class="text-success"',
        r'style\s*=\s*["\']font-weight:\s*bold["\']': 'class="fw-bold"',
    }
    
    for pattern, replacement in replacements.items():
        content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
    
    return content

def create_csp_report_handler():
    """创建 CSP 违规报告处理器"""
    print("\n📝 创建 CSP 违规报告处理器...")
    
    report_handler = '''/**
 * CSP 违规报告处理器
 * 收集和分析 CSP 违规报告
 */

(function() {
    'use strict';
    
    // CSP 违规报告收集器
    const CSPReporter = {
        violations: [],
        maxViolations: 100,
        
        // 初始化报告收集
        init: function() {
            // 监听 CSP 违规事件
            document.addEventListener('securitypolicyviolation', (e) => {
                this.handleViolation(e);
            });
            
            console.log('🔒 CSP 违规报告收集器已启动');
        },
        
        // 处理违规事件
        handleViolation: function(event) {
            const violation = {
                directive: event.violatedDirective,
                blockedURI: event.blockedURI,
                lineNumber: event.lineNumber,
                columnNumber: event.columnNumber,
                sourceFile: event.sourceFile,
                sample: event.sample,
                timestamp: new Date().toISOString()
            };
            
            this.violations.push(violation);
            
            // 限制违规记录数量
            if (this.violations.length > this.maxViolations) {
                this.violations.shift();
            }
            
            // 输出到控制台
            console.warn('🚨 CSP 违规:', violation);
            
            // 可选：发送到服务器
            // this.sendToServer(violation);
        },
        
        // 获取违规报告
        getReport: function() {
            return {
                totalViolations: this.violations.length,
                violations: this.violations,
                summary: this.generateSummary()
            };
        },
        
        // 生成违规摘要
        generateSummary: function() {
            const summary = {};
            
            this.violations.forEach(v => {
                const key = v.directive;
                summary[key] = (summary[key] || 0) + 1;
            });
            
            return summary;
        },
        
        // 清除违规记录
        clear: function() {
            this.violations = [];
            console.log('🧹 CSP 违规记录已清除');
        },
        
        // 发送到服务器（可选）
        sendToServer: function(violation) {
            // 这里可以实现发送违规报告到服务器的逻辑
            // fetch('/api/csp-report', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify(violation)
            // });
        }
    };
    
    // 初始化
    CSPReporter.init();
    
    // 暴露到全局
    window.CSPReporter = CSPReporter;
    
    // 添加调试命令
    window.getCSPReport = function() {
        const report = CSPReporter.getReport();
        console.log('📊 CSP 违规报告:', report);
        return report;
    };
    
})();'''
    
    # 保存 CSP 报告处理器
    report_path = 'app/static/js/csp-reporter.js'
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_handler)
    
    print(f"✅ CSP 报告处理器已保存到: {report_path}")

if __name__ == '__main__':
    print("🚀 高级 CSP 违规修复工具")
    print("=" * 60)
    
    # 1. 扫描并修复内联事件处理器
    fixed_files, total_events = scan_and_fix_inline_events()
    
    # 2. 创建通用事件处理器
    create_universal_event_handler()
    
    # 3. 扫描不安全的内联样式
    scan_unsafe_inline_styles()
    
    # 4. 创建 CSP 违规报告处理器
    create_csp_report_handler()
    
    print("\n" + "=" * 60)
    print("🎉 高级 CSP 修复完成！")
    print(f"✅ 修复了 {fixed_files} 个文件的内联事件")
    print(f"✅ 处理了 {total_events} 个事件处理器")
    print("\n📝 创建的工具:")
    print("   - universal-event-handler.js (通用事件处理器)")
    print("   - csp-reporter.js (CSP 违规报告收集器)")
    print("\n🔧 后续步骤:")
    print("1. 重启应用服务器")
    print("2. 在浏览器控制台运行 getCSPReport() 查看违规情况")
    print("3. 检查备份文件 (*.backup) 确认修复正确")
    print("4. 手动处理复杂的事件处理器")
    print("\n💡 调试命令:")
    print("   getCSPReport() - 查看 CSP 违规报告")
    print("   CSPReporter.clear() - 清除违规记录")
    print("   debugFrontend() - 查看前端状态")
