<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}">
    <style nonce="{{ csp_nonce }}">
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .main-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0 !important;
            padding: 30px;
            text-align: center;
            border: none;
        }

        .school-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .date-picker-container {
            padding: 40px;
        }

        .date-input {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .date-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-continue {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-continue:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .quick-date-btn {
            border: 2px solid #667eea;
            color: #667eea;
            border-radius: 10px;
            padding: 10px 20px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .quick-date-btn:hover {
            background: #667eea;
            color: white;
            transform: translateY(-1px);
        }

        .action-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .instruction-text {
            color: #6c757d;
            font-size: 1rem;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6 col-md-8">
                    <div class="card">
                        <!-- 卡片头部 -->
                        <div class="card-header">
                            <div class="school-icon">
                                <i class="fas fa-school"></i>
                            </div>
                            <h2 class="mb-2">{{ school.name }}</h2>
                            <span class="action-badge">{{ action_name }}</span>
                        </div>

                        <!-- 卡片内容 -->
                        <div class="date-picker-container">
                            <div class="text-center mb-4">
                                <h4 class="text-dark mb-3">
                                    <i class="fas fa-calendar-alt mr-2"></i>选择日期
                                </h4>
                                <p class="instruction-text">
                                    请选择要进行{{ action_name }}的日期
                                </p>
                            </div>

                            <!-- 快速日期选择 -->
                            <div class="text-center mb-4">
                                <h6 class="text-muted mb-3">快速选择</h6>
                                <button type="button" class="btn quick-date-btn" onclick="selectToday()">
                                    <i class="fas fa-calendar-day mr-1"></i>今天
                                </button>
                                <button type="button" class="btn quick-date-btn" onclick="selectYesterday()">
                                    <i class="fas fa-calendar-minus mr-1"></i>昨天
                                </button>
                                <button type="button" class="btn quick-date-btn" onclick="selectTomorrow()">
                                    <i class="fas fa-calendar-plus mr-1"></i>明天
                                </button>
                            </div>

                            <!-- 日期选择表单 -->
                            <form id="dateForm">
                                <div class="mb-4">
                                    <label for="selectedDate" class="form-label fw-bold">
                                        <i class="fas fa-calendar mr-2"></i>选择具体日期
                                    </label>
                                    <input type="date"
                                           class="form-control date-input"
                                           id="selectedDate"
                                           name="selectedDate"
                                           required>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-continue">
                                        <i class="fas fa-arrow-right mr-2"></i>继续
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script nonce="{{ csp_nonce }}" src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script nonce="{{ csp_nonce }}">
        // 获取今天的日期
        function getTodayString() {
            const today = new Date();
            return today.toISOString().split('T')[0];
        }

        // 获取昨天的日期
        function getYesterdayString() {
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            return yesterday.toISOString().split('T')[0];
        }

        // 获取明天的日期
        function getTomorrowString() {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            return tomorrow.toISOString().split('T')[0];
        }

        // 选择今天
        function selectToday() {
            document.getElementById('selectedDate').value = getTodayString();
        }

        // 选择昨天
        function selectYesterday() {
            document.getElementById('selectedDate').value = getYesterdayString();
        }

        // 选择明天
        function selectTomorrow() {
            document.getElementById('selectedDate').value = getTomorrowString();
        }

        // 表单提交处理
        document.getElementById('dateForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const selectedDate = document.getElementById('selectedDate').value;
            if (!selectedDate) {
                alert('请选择日期');
                return;
            }

            // 跳转到检查类型选择页面
            const url = `/daily-management/public/inspections/select-type/{{ school.id }}/{{ action }}/${selectedDate}`;
            window.location.href = url;
        });

        // 页面加载时设置默认日期为今天
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('selectedDate').value = getTodayString();
        });
    </script>
</body>
</html>
