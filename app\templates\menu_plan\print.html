<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单计划打印</title>
    <style nonce="{{ csp_nonce }}">
        body {
            font-family: SimSun, "宋体", "Microsoft YaHei", "微软雅黑", sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 14pt;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .title {
            font-size: 24pt;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 18pt;
            margin-bottom: 20px;
        }
        .info {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
        }
        .info-item {
            margin-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
        }
        .signature {
            margin-top: 50px;
        }
        @media print {
            body {
                padding: 0;
            }
            @page {
                size: A4;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">{{ project_name|default('初中毕业生学生去向管理系统') }}</div>
            <div class="subtitle">菜单计划表</div>
        </div>

        <div class="info">
            <div>
                <div class="info-item"><strong>区域：</strong>{{ menu_plan.area.name }}</div>
                <div class="info-item"><strong>计划日期：</strong>{{ menu_plan.plan_date }}</div>
                <div class="info-item"><strong>餐次：</strong>{{ menu_plan.meal_type }}</div>
            </div>
            <div>
                <div class="info-item"><strong>预计用餐人数：</strong>{{ menu_plan.expected_diners or '未设置' }}</div>
                <div class="info-item"><strong>状态：</strong>{{ menu_plan.status }}</div>
                <div class="info-item"><strong>编号：</strong>{{ menu_plan.id }}</div>
            </div>
        </div>

        <table>
            <thead>
                <tr>
                    <th style="width: 10%">序号</th>
                    <th style="width: 50%">食谱名称</th>
                    <th style="width: 20%">计划数量</th>
                    <th style="width: 20%">实际数量</th>
                </tr>
            </thead>
            <tbody>
                {% for menu_recipe in menu_recipes %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ menu_recipe.recipe.name }}</td>
                    <td>{{ menu_recipe.planned_quantity }}</td>
                    <td>{{ menu_recipe.actual_quantity or '' }}</td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="4" class="text-center">暂无食谱</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        {% if menu_plan.notes %}
        <div>
            <strong>备注：</strong>{{ menu_plan.notes }}
        </div>
        {% endif %}

        <div class="footer">
            <div>
                <div><strong>制表人：</strong>{{ menu_plan.creator.real_name or menu_plan.creator.username }}</div>
                <div class="signature">签名：________________</div>
            </div>
            <div>
                <div><strong>审核人：</strong>{{ menu_plan.approver.real_name or menu_plan.approver.username if menu_plan.approver else '________________' }}</div>
                <div class="signature">签名：________________</div>
            </div>
            <div>
                <div><strong>日期：</strong>{{  menu_plan.created_at|format_datetime('%Y年%m月%d日')  }}</div>
            </div>
        </div>
    </div>

    <script nonce="{{ csp_nonce }}">
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
