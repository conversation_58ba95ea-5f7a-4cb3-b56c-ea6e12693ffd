from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, make_response, abort, send_file
from flask_login import login_required, current_user
from app import db
from app.models import (
    PurchaseOrder, PurchaseOrderItem, Supplier, SupplierProduct,
    Ingredient, Recipe, RecipeIngredient, WeeklyMenu, WeeklyMenuRecipe,
    AdministrativeArea, Inventory, StockIn, StockInItem, SupplierSchoolRelation, MenuPlan,
    User
)
from app.utils.decorators import check_permission
from app.utils.pdf_generator import generate_purchase_order_pdf, generate_supplier_order_pdf
from app.forms.purchase_order import PurchaseOrderForm, PurchaseOrderFilterForm, MenuPurchaseOrderForm
from datetime import datetime, date, timedelta
import json
import uuid
import os
from sqlalchemy import func, text, Numeric, DateTime, String, Integer, Date
from decimal import Decimal
from sqlalchemy.types import Numeric
from sqlalchemy import bindparam
from sqlalchemy.orm import joinedload

# 辅助函数：格式化日期时间
def format_datetime(dt, format_str):
    """格式化日期时间对象为字符串"""
    if dt is None:
        return ''
    if isinstance(dt, str):
        try:
            # 尝试将字符串转换为datetime对象
            dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            try:
                # 尝试另一种常见格式
                dt = datetime.strptime(dt, '%Y-%m-%d')
            except ValueError:
                # 如果无法解析，直接返回原字符串
                return dt
    try:
        return dt.strftime(format_str)
    except Exception:
        return str(dt)

purchase_order_bp = Blueprint('purchase_order', __name__, url_prefix='/purchase-order')

def get_order_detailed_status(order):
    """获取采购订单的详细状态信息"""
    from app.models import StockOut, StockOutItem, ConsumptionPlan

    status_info = {
        'has_stock_in': False,
        'stock_in_status': '未入库',
        'stock_in_date': None,
        'stock_in_id': None,
        'consumption_status': '未消耗',
        'consumption_details': [],
        'items_status': []
    }

    try:
        # 1. 检查入库状态
        stock_ins = StockIn.query.filter_by(purchase_order_id=order.id).all()
        if stock_ins:
            status_info['has_stock_in'] = True
            # 获取最新的入库单
            latest_stock_in = max(stock_ins, key=lambda x: x.created_at)
            status_info['stock_in_status'] = latest_stock_in.status
            status_info['stock_in_date'] = latest_stock_in.stock_in_date
            status_info['stock_in_id'] = latest_stock_in.id

            # 2. 检查消耗状态
            if latest_stock_in.status == '已入库':
                # 查询该入库单的食材消耗情况
                stock_in_items = StockInItem.query.filter_by(stock_in_id=latest_stock_in.id).all()

                for item in stock_in_items:
                    item_status = {
                        'ingredient_id': item.ingredient_id,
                        'ingredient_name': item.ingredient.name if item.ingredient else '未知食材',
                        'batch_number': item.batch_number,
                        'quantity': item.quantity,
                        'unit': item.unit,
                        'consumption_status': '未消耗',
                        'consumption_meals': []
                    }

                    # 查询该批次的出库记录
                    stock_out_items = StockOutItem.query.filter_by(
                        ingredient_id=item.ingredient_id,
                        batch_number=item.batch_number
                    ).all()

                    if stock_out_items:
                        consumed_quantity = sum(float(soi.quantity or 0) for soi in stock_out_items)
                        remaining_quantity = float(item.quantity or 0) - consumed_quantity

                        if remaining_quantity <= 0:
                            item_status['consumption_status'] = '已消耗'
                        elif consumed_quantity > 0:
                            item_status['consumption_status'] = '部分消耗'

                        # 获取消耗的餐次信息
                        for soi in stock_out_items:
                            if soi.stock_out and soi.stock_out.consumption_plan:
                                cp = soi.stock_out.consumption_plan
                                meal_info = {
                                    'consumption_date': cp.consumption_date,
                                    'meal_type': cp.meal_type,
                                    'quantity': soi.quantity
                                }
                                item_status['consumption_meals'].append(meal_info)

                    status_info['items_status'].append(item_status)

                # 计算整体消耗状态
                if status_info['items_status']:
                    consumed_items = [item for item in status_info['items_status'] if item['consumption_status'] == '已消耗']
                    partial_consumed_items = [item for item in status_info['items_status'] if item['consumption_status'] == '部分消耗']

                    if len(consumed_items) == len(status_info['items_status']):
                        status_info['consumption_status'] = '已消耗'
                    elif len(consumed_items) > 0 or len(partial_consumed_items) > 0:
                        status_info['consumption_status'] = '部分消耗'

                    # 获取消耗详情
                    all_meals = []
                    for item in status_info['items_status']:
                        all_meals.extend(item['consumption_meals'])

                    # 按日期和餐次分组
                    meal_groups = {}
                    for meal in all_meals:
                        key = f"{meal['consumption_date']}_{meal['meal_type']}"
                        if key not in meal_groups:
                            meal_groups[key] = {
                                'consumption_date': meal['consumption_date'],
                                'meal_type': meal['meal_type'],
                                'total_items': 0
                            }
                        meal_groups[key]['total_items'] += 1

                    status_info['consumption_details'] = list(meal_groups.values())

        return status_info

    except Exception as e:
        current_app.logger.error(f"获取订单详细状态失败: {str(e)}")
        return status_info

@purchase_order_bp.route('/new')
@login_required
@check_permission('purchase_order', 'create')
def new():
    """创建新采购订单 - 简单版本，自动绑定用户学校"""
    # 获取当前用户的学校区域
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法创建采购订单', 'danger')
        return redirect(url_for('purchase_order.index'))

    # 重定向到表单创建页面
    return redirect(url_for('purchase_order.create_form'))

def standardize_unit(unit):
    """单位标准化函数"""
    # 单位标准化映射
    unit_map = {
        '克': 'kg',
        '千克': 'kg',
        'kg': 'kg',
        'g': 'kg',
        '公斤': 'kg',
        '斤': 'kg',
        '两': 'kg',

        '毫升': 'ml',
        '升': 'ml',
        'ml': 'ml',
        'l': 'ml',

        '瓶': '瓶',
        '罐': '瓶',
        '盒': '盒',
        '包': '包',
        '袋': '包',
        '个': '个',
        '只': '个',
        '条': '个',
        '根': '个'
    }

    # 如果有映射则返回标准单位，否则返回原单位
    return unit_map.get(unit, unit)

def calculate_ingredient_similarity(ingredients1, ingredients2):
    """计算两个食材集合的相似度（Jaccard相似度）"""
    if not ingredients1 or not ingredients2:
        return 0.0

    set1 = set(ingredients1)
    set2 = set(ingredients2)

    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))

    return intersection / union if union > 0 else 0.0

def analyze_purchase_similarity(meal_ingredients, existing_orders):
    """分析餐次食材与已存在采购订单的相似度"""
    if not existing_orders:
        return {
            'has_similar': False,
            'max_similarity': 0.0,
            'similar_orders': []
        }

    similar_orders = []
    max_similarity = 0.0

    for order in existing_orders:
        order_ingredients = order.get('main_ingredients', set())
        similarity = calculate_ingredient_similarity(meal_ingredients, order_ingredients)

        if similarity > 0.3:  # 相似度阈值30%
            similar_orders.append({
                'order': order,
                'similarity': similarity,
                'similarity_percent': round(similarity * 100, 1)
            })

        max_similarity = max(max_similarity, similarity)

    # 按相似度降序排序
    similar_orders.sort(key=lambda x: x['similarity'], reverse=True)

    return {
        'has_similar': len(similar_orders) > 0,
        'max_similarity': max_similarity,
        'max_similarity_percent': round(max_similarity * 100, 1),
        'similar_orders': similar_orders
    }

def generate_purchase_suggestions(meal_ingredients, existing_orders, similarity_analysis):
    """生成采购智能建议"""
    suggestions = {
        'action': 'create_new',  # create_new, skip, merge, review
        'message': '',
        'details': [],
        'confidence': 'high'  # high, medium, low
    }

    if not existing_orders:
        suggestions.update({
            'action': 'create_new',
            'message': '该日期暂无采购订单，建议创建新的采购计划',
            'confidence': 'high'
        })
        return suggestions

    # 检查是否有高相似度的订单
    if similarity_analysis['has_similar']:
        max_similarity = similarity_analysis['max_similarity']

        if max_similarity >= 0.8:  # 80%以上相似度
            suggestions.update({
                'action': 'skip',
                'message': f'发现高度相似的采购订单（相似度{similarity_analysis["max_similarity_percent"]}%），建议跳过创建',
                'details': [
                    '主材重复度很高，可能是重复采购',
                    '建议检查现有订单是否已满足需求',
                    '如需采购，请确认是否为不同供应商或规格'
                ],
                'confidence': 'high'
            })
        elif max_similarity >= 0.5:  # 50-80%相似度
            suggestions.update({
                'action': 'review',
                'message': f'发现中等相似的采购订单（相似度{similarity_analysis["max_similarity_percent"]}%），建议仔细审查',
                'details': [
                    '部分主材重复，可能存在优化空间',
                    '建议对比现有订单的食材数量',
                    '考虑是否可以合并或调整订单'
                ],
                'confidence': 'medium'
            })
        else:  # 30-50%相似度
            suggestions.update({
                'action': 'create_new',
                'message': f'发现低相似度订单（相似度{similarity_analysis["max_similarity_percent"]}%），可以创建新订单',
                'details': [
                    '主材差异较大，适合创建独立订单',
                    '建议关注食材采购的时效性'
                ],
                'confidence': 'medium'
            })
    else:
        suggestions.update({
            'action': 'create_new',
            'message': '该日期的主材组合较为独特，建议创建新的采购订单',
            'confidence': 'high'
        })

    return suggestions

@purchase_order_bp.route('/')
@login_required
@check_permission('purchase_order', 'view')
def index():
    """采购订单列表页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    area_id = request.args.get('area_id', type=int)
    status = request.args.get('status', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    supplier_id = request.args.get('supplier_id', type=int)

    # 构建查询
    query = PurchaseOrder.query.filter(PurchaseOrder.area_id.in_(area_ids))

    # 应用筛选条件
    if area_id:
        query = query.filter_by(area_id=area_id)
    if status:
        query = query.filter_by(status=status)
    if supplier_id:
        query = query.filter_by(supplier_id=supplier_id)
    if start_date:
        query = query.filter(PurchaseOrder.order_date >= datetime.strptime(start_date, '%Y-%m-%d'))
    if end_date:
        query = query.filter(PurchaseOrder.order_date <= datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1))

    # 排序并分页
    orders = query.order_by(PurchaseOrder.order_date.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # 获取每个订单的详细状态信息
    order_ids = [order.id for order in orders.items]
    orders_status_info = {}

    for order in orders.items:
        status_info = get_order_detailed_status(order)
        orders_status_info[order.id] = status_info

    # 创建一个集合，包含所有已创建入库单的采购订单ID（保持兼容性）
    stock_in_order_ids = {order_id for order_id, status in orders_status_info.items() if status['has_stock_in']}

    # 获取所有区域和供应商，用于筛选
    areas = accessible_areas
    suppliers = Supplier.query.all()

    return render_template('purchase_order/index.html',
                          title='采购订单列表',
                          orders=orders,
                          areas=areas,
                          suppliers=suppliers,
                          area_id=area_id,
                          supplier_id=supplier_id,
                          status=status,
                          start_date=start_date,
                          end_date=end_date,
                          stock_in_order_ids=stock_in_order_ids,
                          orders_status_info=orders_status_info)

@purchase_order_bp.route('/create-from-menu')
@login_required
@check_permission('purchase_order', 'create')
def create_from_menu():
    """从周菜单创建采购订单页面"""
    # 获取参数
    weekly_menu_id = request.args.get('weekly_menu_id', type=int)
    area_id = request.args.get('area_id', type=int)
    week_offset = request.args.get('week_offset', 0, type=int)  # 0=本周, 1=下周

    # 获取当前用户的学校区域
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法创建采购订单', 'danger')
        return redirect(url_for('purchase_order.index'))

    # 如果没有指定区域，使用用户的学校区域
    if not area_id:
        area_id = user_area.id

    # 检查用户是否有权限访问该区域
    if not current_user.can_access_area_by_id(area_id):
        flash('您没有权限访问该区域', 'danger')
        return redirect(url_for('purchase_order.index'))

    area = AdministrativeArea.query.get_or_404(area_id)

    # 计算目标周的开始和结束日期
    today = datetime.now().date()
    current_week_start = today - timedelta(days=today.weekday())
    target_week_start = current_week_start + timedelta(weeks=week_offset)
    target_week_end = target_week_start + timedelta(days=6)

    # 如果提供了weekly_menu_id，直接获取对应的菜单
    if weekly_menu_id:
        weekly_menu = WeeklyMenu.query.get_or_404(weekly_menu_id)
        # 验证菜单是否属于指定区域
        if weekly_menu.area_id != area_id:
            flash('菜单与区域不匹配', 'danger')
            return redirect(url_for('purchase_order.index'))
        week_start = weekly_menu.week_start
        week_end = weekly_menu.week_end
    else:
        # 获取指定周的菜单
        weekly_menu = WeeklyMenu.query.filter_by(
            area_id=area_id,
            week_start=target_week_start
        ).first()

        # 如果没有找到菜单，返回错误信息
        if not weekly_menu:
            week_name = "本周" if week_offset == 0 else "下周"
            flash(f'该区域还没有{week_name}菜单，请先创建周菜单', 'warning')
            return redirect(url_for('weekly_menu_v2.index'))

        week_start = weekly_menu.week_start
        week_end = weekly_menu.week_end

    # 准备周次选择数据
    week_options = []
    for i in range(2):  # 本周和下周
        option_week_start = current_week_start + timedelta(weeks=i)
        option_week_end = option_week_start + timedelta(days=6)

        # 检查是否有对应的菜单
        option_menu = WeeklyMenu.query.filter_by(
            area_id=area_id,
            week_start=option_week_start
        ).first()

        week_options.append({
            'offset': i,
            'name': '本周' if i == 0 else '下周',
            'start': option_week_start,
            'end': option_week_end,
            'start_str': option_week_start.strftime('%Y-%m-%d'),
            'end_str': option_week_end.strftime('%Y-%m-%d'),
            'selected': week_offset == i,
            'has_menu': option_menu is not None,
            'menu_id': option_menu.id if option_menu else None
        })

    # 获取周菜单数据
    week_data = {}
    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

    for i in range(7):
        current_date = week_start + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')

        # 获取该日期的菜单
        day_recipes = WeeklyMenuRecipe.query.filter_by(
            weekly_menu_id=weekly_menu.id,
            day_of_week=i + 1
        ).all()

        # 按餐次分类食谱
        meals = {
            '早餐': [],
            '午餐': [],
            '晚餐': []
        }

        for menu_recipe in day_recipes:
            if menu_recipe.recipe:
                recipe_data = {
                    'id': menu_recipe.recipe.id,
                    'name': menu_recipe.recipe.name,
                    'category': menu_recipe.recipe.category,
                    'ingredients': [
                        {
                            'id': ri.ingredient_id,
                            'name': ri.ingredient.name,
                            'quantity': ri.quantity,
                            'unit': ri.unit
                        }
                        for ri in menu_recipe.recipe.ingredients
                    ]
                }
                meals[menu_recipe.meal_type].append(recipe_data)

        week_data[date_str] = {
            'weekday': weekdays[i],
            'day_of_week': i + 1,
            'meals': meals
        }

    return render_template('purchase_order/create_from_menu.html',
                         area=area,
                         weekly_menu=weekly_menu,
                         week_start=week_start.strftime('%Y-%m-%d'),
                         week_end=week_end.strftime('%Y-%m-%d'),
                         week_data=week_data,
                         week_options=week_options,
                         current_week_offset=week_offset)

@purchase_order_bp.route('/analyze-selected-meals', methods=['POST'])
@login_required
@check_permission('purchase_order', 'create')
def analyze_selected_meals():
    """分析选中餐次的食材需求"""
    data = request.get_json()

    if not data or 'weekly_menu_id' not in data or 'selected_meals' not in data:
        return jsonify({
            'success': False,
            'message': '参数错误'
        })

    try:
        weekly_menu_id = int(data['weekly_menu_id'])  # 确保是整数类型
        selected_meals = data['selected_meals']
        area_id = data.get('area_id')
        if area_id:
            area_id = int(area_id)  # 确保是整数类型

        # 获取周菜单
        weekly_menu = WeeklyMenu.query.get_or_404(weekly_menu_id)
        if not area_id:
            area_id = weekly_menu.area_id

        # 获取该周已存在的采购订单
        existing_orders = PurchaseOrder.query.filter(
            PurchaseOrder.area_id == area_id,
            PurchaseOrder.order_date >= weekly_menu.week_start,
            PurchaseOrder.order_date <= weekly_menu.week_end
        ).all()

        # 按日期和餐次组织已存在的采购订单
        existing_orders_map = {}
        for order in existing_orders:
            # 确保 order_date 是 datetime 对象
            if hasattr(order.order_date, 'strftime'):
                order_date_str = order.order_date.strftime('%Y-%m-%d')
            else:
                # 如果是字符串，尝试转换
                order_date_str = str(order.order_date)[:10]  # 取前10个字符 YYYY-MM-DD

            # 采购订单通常不区分餐次，但我们可以根据备注或其他字段来判断
            order_key = f"{order_date_str}_采购"

            if order_key not in existing_orders_map:
                existing_orders_map[order_key] = []

            # 获取该采购订单的主材信息
            order_main_ingredients = set()
            order_items = PurchaseOrderItem.query.filter_by(order_id=order.id).all()
            for item in order_items:
                if item.ingredient and not item.ingredient.is_condiment:
                    order_main_ingredients.add(item.ingredient_id)

            existing_orders_map[order_key].append({
                'id': order.id,
                'order_number': order.order_number,
                'status': order.status,
                'created_at': order.created_at.isoformat() if hasattr(order.created_at, 'isoformat') else str(order.created_at),
                'main_ingredients': list(order_main_ingredients),  # 转换为列表
                'ingredients_count': len(order_main_ingredients),
                'total_amount': float(order.total_amount) if order.total_amount else 0.0
            })

        # 分析每个选中的餐次
        meal_analysis = {}
        all_ingredient_totals = {}

        for meal_selection in selected_meals:
            date_str, meal_type = meal_selection.split('_')
            meal_date = datetime.strptime(date_str, '%Y-%m-%d').date()

            # 确保 week_start 是 date 对象
            if hasattr(weekly_menu.week_start, 'date'):
                week_start_date = weekly_menu.week_start.date()
            else:
                week_start_date = weekly_menu.week_start

            day_of_week = (meal_date - week_start_date).days + 1

            # 获取该餐次的食谱
            weekly_recipes = WeeklyMenuRecipe.query.filter_by(
                weekly_menu_id=int(weekly_menu_id),
                day_of_week=int(day_of_week),
                meal_type=str(meal_type)
            ).all()

            if not weekly_recipes:
                meal_analysis[meal_selection] = {
                    'has_recipes': False,
                    'message': f'{date_str} {meal_type} 无菜品安排'
                }
                continue

            # 收集该餐次的食材需求和主材
            meal_ingredients = set()
            meal_ingredient_details = []
            meal_recipes_info = []
            meal_ingredient_by_category = {}
            meal_ingredient_totals = {}

            for weekly_recipe in weekly_recipes:
                if weekly_recipe.recipe_id and weekly_recipe.recipe:
                    recipe_info = {
                        'id': weekly_recipe.recipe.id,
                        'name': weekly_recipe.recipe.name,
                        'category': weekly_recipe.recipe.category,
                        'main_ingredients': [],
                        'condiments': []
                    }

                    recipe_ingredients = weekly_recipe.recipe.ingredients.all()
                    for recipe_ingredient in recipe_ingredients:
                        ingredient_id = recipe_ingredient.ingredient_id
                        quantity = recipe_ingredient.quantity
                        unit = recipe_ingredient.unit
                        ingredient_name = recipe_ingredient.ingredient.name
                        ingredient_category = recipe_ingredient.ingredient.category

                        # 累加到总需求中
                        if ingredient_id not in all_ingredient_totals:
                            all_ingredient_totals[ingredient_id] = {
                                'quantity': 0,
                                'unit': unit,
                                'name': ingredient_name,
                                'category': ingredient_category
                            }
                        all_ingredient_totals[ingredient_id]['quantity'] += quantity

                        # 分类处理主材和调味料
                        # 通过 is_condiment 字段或者分类名称来判断
                        if not recipe_ingredient.ingredient.is_condiment and ingredient_category != '调味品':
                            # 主材处理
                            meal_ingredients.add(ingredient_id)

                            # 累加该餐次的主材总量
                            if ingredient_id not in meal_ingredient_totals:
                                meal_ingredient_totals[ingredient_id] = {
                                    'name': ingredient_name,
                                    'quantity': 0,
                                    'unit': unit,
                                    'category': ingredient_category,
                                    'recipes': []
                                }
                            meal_ingredient_totals[ingredient_id]['quantity'] += quantity
                            meal_ingredient_totals[ingredient_id]['recipes'].append(weekly_recipe.recipe.name)

                            # 按分类统计主材
                            category_name = ingredient_category if ingredient_category else '未分类'
                            if category_name not in meal_ingredient_by_category:
                                meal_ingredient_by_category[category_name] = []

                            meal_ingredient_by_category[category_name].append({
                                'id': ingredient_id,
                                'name': ingredient_name,
                                'quantity': quantity,
                                'unit': unit,
                                'recipe': weekly_recipe.recipe.name
                            })

                            # 添加到食谱的主材列表
                            recipe_info['main_ingredients'].append({
                                'name': ingredient_name,
                                'quantity': quantity,
                                'unit': unit,
                                'category': category_name
                            })

                            # 收集主材详情用于分析
                            meal_ingredient_details.append({
                                'ingredient_id': ingredient_id,
                                'name': ingredient_name,
                                'quantity': quantity,
                                'unit': unit,
                                'is_condiment': False,
                                'category': category_name,
                                'recipe': weekly_recipe.recipe.name
                            })
                        else:
                            # 调味料处理（仅记录，不参与分析）
                            recipe_info['condiments'].append({
                                'name': ingredient_name,
                                'quantity': quantity,
                                'unit': unit
                            })

                    meal_recipes_info.append(recipe_info)

            # 分析与已存在采购订单的相似度
            order_date_key = f"{date_str}_采购"
            existing_orders_for_date = existing_orders_map.get(order_date_key, [])

            if meal_ingredients:
                similarity_analysis = analyze_purchase_similarity(meal_ingredients, existing_orders_for_date)
            else:
                similarity_analysis = {
                    'has_similar': False,
                    'max_similarity': 0.0,
                    'similar_orders': []
                }

            # 智能处理建议
            smart_suggestions = generate_purchase_suggestions(meal_ingredients, existing_orders_for_date, similarity_analysis)

            meal_analysis[meal_selection] = {
                'has_recipes': True,
                'recipes': meal_recipes_info,
                'recipes_count': len(meal_recipes_info),
                'ingredients': meal_ingredient_details,
                'main_ingredients_count': len(meal_ingredients),
                'ingredient_by_category': meal_ingredient_by_category,
                'ingredient_totals': meal_ingredient_totals,
                'category_stats': {
                    'total_categories': len(meal_ingredient_by_category),
                    'categories': list(meal_ingredient_by_category.keys())
                },
                'similarity_analysis': similarity_analysis,
                'existing_orders': existing_orders_for_date,
                'smart_suggestions': smart_suggestions
            }

        return jsonify({
            'success': True,
            'data': {
                'meal_analysis': meal_analysis,
                'total_ingredients': len(all_ingredient_totals),
                'all_ingredient_totals': all_ingredient_totals
            }
        })

    except Exception as e:
        current_app.logger.error(f'分析选中餐次失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'分析失败: {str(e)}'
        })

@purchase_order_bp.route('/get-ingredients', methods=['POST'])
@login_required
@check_permission('purchase_order', 'create')
def get_ingredients():
    """获取食材清单（保留原有功能，用于兼容）"""
    data = request.get_json()

    if not data or 'area_id' not in data or 'selected_meals' not in data:
        return jsonify({
            'success': False,
            'message': '参数错误'
        })

    area_id = data['area_id']
    selected_meals = data['selected_meals']
    weekly_menu_id = data.get('weekly_menu_id')

    try:
        # 确保参数类型正确
        area_id = int(area_id)
        if weekly_menu_id:
            weekly_menu_id = int(weekly_menu_id)

        # 获取区域信息
        area = AdministrativeArea.query.get_or_404(area_id)

        # 获取周菜单
        if weekly_menu_id:
            weekly_menu = WeeklyMenu.query.get_or_404(weekly_menu_id)
        else:
            weekly_menu = WeeklyMenu.query.filter_by(area_id=area_id).order_by(WeeklyMenu.week_start.desc()).first()
            if not weekly_menu:
                return jsonify({
                    'success': False,
                    'message': '未找到周菜单'
                })

        # 收集所有选中餐次的食材信息
        ingredients_data = {}

        for meal_selection in selected_meals:
            date_str, meal_type = meal_selection.split('_')
            meal_date = datetime.strptime(date_str, '%Y-%m-%d').date()

            # 确保 week_start 是 date 对象
            if hasattr(weekly_menu.week_start, 'date'):
                week_start_date = weekly_menu.week_start.date()
            else:
                week_start_date = weekly_menu.week_start

            day_of_week = (meal_date - week_start_date).days + 1

            # 获取该餐次的食谱
            weekly_recipes = WeeklyMenuRecipe.query.filter_by(
                weekly_menu_id=int(weekly_menu.id),
                day_of_week=int(day_of_week),
                meal_type=str(meal_type)
            ).all()

            for weekly_recipe in weekly_recipes:
                if weekly_recipe.recipe_id and weekly_recipe.recipe:
                    recipe_ingredients = weekly_recipe.recipe.ingredients.all()
                    for ri in recipe_ingredients:
                        ingredient = ri.ingredient
                        # 只处理主材，排除调味料
                        # 通过 is_condiment 字段或者分类名称来判断
                        if ingredient.is_condiment or ingredient.category == '调味品':
                            continue

                        key = (ingredient.id, ri.unit)

                        if key not in ingredients_data:
                            ingredients_data[key] = {
                                'id': ingredient.id,
                                'name': ingredient.name,
                                'category': ingredient.category,
                                'total_quantity': 0,
                                'unit': ri.unit,
                                'suppliers': []  # 供应商列表
                            }

                        # 累加数量
                        ingredients_data[key]['total_quantity'] += ri.quantity

        # 获取每个食材的供应商信息
        for key, ingredient_data in ingredients_data.items():
            ingredient_id = ingredient_data['id']

            # 获取可用的供应商
            supplier_products = db.session.query(
                SupplierProduct,
                Supplier
            ).join(
                Supplier,
                SupplierProduct.supplier_id == Supplier.id
            ).join(
                SupplierSchoolRelation,
                Supplier.id == SupplierSchoolRelation.supplier_id
            ).filter(
                SupplierProduct.ingredient_id == ingredient_id,
                SupplierProduct.is_available == 1,
                Supplier.status == 1,
                SupplierSchoolRelation.area_id == area_id,
                SupplierSchoolRelation.status == 1
            ).all()

            # 添加供应商信息
            for supplier_product, supplier in supplier_products:
                supplier_data = {
                    'id': supplier.id,
                    'name': supplier.name,
                    'product_id': supplier_product.id,
                    'specification': supplier_product.specification or ''
                }
                ingredient_data['suppliers'].append(supplier_data)

        # 转换为列表
        ingredients_list = list(ingredients_data.values())

        # 按类别分组
        grouped_ingredients = {}
        for ingredient in ingredients_list:
            category = ingredient['category']
            if category not in grouped_ingredients:
                grouped_ingredients[category] = []
            grouped_ingredients[category].append(ingredient)

        return jsonify({
            'success': True,
            'data': {
                'ingredients': grouped_ingredients
            }
        })

    except Exception as e:
        current_app.logger.error(f'获取食材清单失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取食材清单失败'
        })

@purchase_order_bp.route('/create-order', methods=['POST'])
@login_required
@check_permission('purchase_order', 'create')
def create_order():
    """创建采购订单"""
    # 从请求中获取JSON数据
    json_data = request.get_json()
    if not json_data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据'
        })

    try:
        # 生成订单编号和时间
        order_number = generate_order_number()
        now = datetime.now().replace(microsecond=0)
        delivery_date = now + timedelta(days=1)

        # 确定供应商ID - 使用0表示自购订单
        supplier_id = 0
        ingredients = json_data.get('ingredients', [])
        if ingredients:
            # 检查第一个有供应商的食材
            for item in ingredients:
                if item.get('product_id'):
                    supplier_product = SupplierProduct.query.get(item.get('product_id'))
                    if supplier_product:
                        supplier_id = supplier_product.supplier_id
                        break

        # 构建包含餐次信息的备注
        notes_content = []

        # 添加餐次信息
        selected_meals = json_data.get('selected_meals', [])
        weekly_menu_id = json_data.get('weekly_menu_id')
        if selected_meals:
            notes_content.append("=== 采购餐次信息 ===")
            if weekly_menu_id:
                notes_content.append(f"周菜单ID: {weekly_menu_id}")
            notes_content.append("选中餐次:")
            for meal in selected_meals:
                # 格式化餐次显示，如 "2025-01-13_午餐" -> "2025-01-13 午餐"
                formatted_meal = meal.replace('_', ' ')
                notes_content.append(f"  • {formatted_meal}")
            notes_content.append("")  # 空行分隔

        # 添加用户备注
        user_notes = json_data.get('notes', '').strip()
        if user_notes:
            notes_content.append("=== 用户备注 ===")
            notes_content.append(user_notes)

        notes = '\n'.join(notes_content)

        # 准备订单数据
        order_data = {
            'order_number': order_number,
            'supplier_id': supplier_id,  # 使用0表示自购订单，确保不为NULL
            'total_amount': Decimal('0.00'),
            'order_date': now,
            'status': '待确认',  # 设置为待确认状态，遵循正常的业务流程
            'delivery_date': delivery_date,
            'created_by': current_user.id,
            'created_at': now,
            'requisition_id': 0,  # 使用0代替NULL
            'area_id': json_data.get('area_id'),
            'expected_delivery_date': delivery_date.date(),
            'payment_terms': '',  # 使用空字符串代替NULL
            'delivery_terms': '',  # 使用空字符串代替NULL
            'approved_by': None,  # 初始状态未审核
            'updated_at': now,
            'notes': notes  # 包含餐次信息和用户备注的完整备注
        }

        # 创建采购订单的 SQL 语句
        insert_order_stmt = text("""
            INSERT INTO purchase_orders (
                order_number, supplier_id, total_amount, order_date, status,
                delivery_date, created_by, created_at, requisition_id, area_id,
                expected_delivery_date, payment_terms, delivery_terms,
                approved_by, updated_at, notes
            )
            OUTPUT inserted.id
            VALUES (
                :order_number, :supplier_id, :total_amount, :order_date, :status,
                :delivery_date, :created_by, :created_at, :requisition_id, :area_id,
                :expected_delivery_date, :payment_terms, :delivery_terms,
                :approved_by, :updated_at, :notes
            )
        """).bindparams(
            bindparam('order_number', type_=String),
            bindparam('supplier_id', type_=Integer),
            bindparam('total_amount', type_=Numeric(18, 2)),
            bindparam('order_date', type_=DateTime),
            bindparam('status', type_=String),
            bindparam('delivery_date', type_=DateTime),
            bindparam('created_by', type_=Integer),
            bindparam('created_at', type_=DateTime),
            bindparam('requisition_id', type_=Integer),
            bindparam('area_id', type_=Integer),
            bindparam('expected_delivery_date', type_=Date),
            bindparam('payment_terms', type_=String),
            bindparam('delivery_terms', type_=String),
            bindparam('approved_by', type_=Integer),
            bindparam('updated_at', type_=DateTime),
            bindparam('notes', type_=String)
        )

        # 执行订单插入并获取ID
        result = db.session.execute(insert_order_stmt, order_data)
        order_id = result.scalar()

        # 创建订单明细的 SQL 语句
        insert_item_stmt = text("""
            INSERT INTO purchase_order_items (
                order_id, ingredient_id, product_id, quantity, unit,
                unit_price, total_price, created_at, updated_at
            )
            VALUES (
                :order_id, :ingredient_id, :product_id, :quantity, :unit,
                :unit_price, :total_price, :created_at, :updated_at
            )
        """).bindparams(
            bindparam('order_id', type_=Integer),
            bindparam('ingredient_id', type_=Integer),
            bindparam('product_id', type_=Integer),
            bindparam('quantity', type_=Numeric(10, 2)),
            bindparam('unit', type_=String),
            bindparam('unit_price', type_=Numeric(10, 2)),
            bindparam('total_price', type_=Numeric(10, 2)),
            bindparam('created_at', type_=DateTime),
            bindparam('updated_at', type_=DateTime)
        )

        # 处理订单明细
        total_amount = Decimal('0.00')

        for item_data in ingredients:
            # 获取食材信息
            ingredient = Ingredient.query.get(item_data.get('id'))
            if not ingredient:
                continue

            # 如果选择了供应商，获取供应商产品信息
            supplier_product = None
            if item_data.get('product_id'):
                supplier_product = SupplierProduct.query.get(item_data.get('product_id'))

            # 使用 Decimal 进行精确计算
            try:
                quantity = Decimal(str(item_data.get('purchase_quantity', '0')))
                unit_price = Decimal(str(supplier_product.price)) if supplier_product else Decimal('0.00')
            except (TypeError, ValueError):
                quantity = Decimal('0')
                unit_price = Decimal('0.00')

            # 计算总价
            total_price = quantity * unit_price

            # 准备订单项数据
            item_data = {
                'order_id': order_id,
                'ingredient_id': item_data.get('id'),
                'product_id': item_data.get('product_id') or 0,  # 使用0代替NULL
                'quantity': quantity,
                'unit': item_data.get('unit', ''),  # 使用传入的单位
                'unit_price': unit_price,
                'total_price': total_price,
                'created_at': now,
                'updated_at': now
            }

            # 执行订单项插入
            db.session.execute(insert_item_stmt, item_data)
            total_amount += total_price

        # 更新订单总金额
        update_total_stmt = text("""
            UPDATE purchase_orders
            SET total_amount = :total_amount
            WHERE id = :order_id
        """).bindparams(
            bindparam('total_amount', type_=Numeric(18, 2)),
            bindparam('order_id', type_=Integer)
        )

        db.session.execute(update_total_stmt, {
            'total_amount': total_amount,
            'order_id': order_id
        })

        # 不再自动创建入库记录，遵循正常的业务流程
        # 入库流程将在采购订单确认、送货和验收后单独处理

        # 提交事务
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '采购订单创建成功，等待确认',
            'redirect_url': url_for('purchase_order.index')
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'创建采购订单失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '创建采购订单失败，请检查数据是否正确'
        })

def generate_order_number():
    """生成订单编号"""
    now = datetime.now()
    date_str = now.strftime('%Y%m%d')
    time_str = now.strftime('%H%M%S')
    random_str = str(int(now.timestamp() * 1000))[-3:]
    return f'PO{date_str}{time_str}{random_str}'

@purchase_order_bp.route('/api/purchase-orders/available')
@login_required
def api_available_orders():
    """获取可用于入库的采购订单列表（API接口）"""
    try:
        # 获取当前用户可访问的区域
        accessible_areas = current_user.get_accessible_areas()
        area_ids = [area.id for area in accessible_areas]

        if not area_ids:
            return jsonify({
                'success': False,
                'message': '您没有可访问的区域'
            }), 403

        # 查询状态为"已确认"或"准备入库"的采购订单
        query = PurchaseOrder.query.filter(
            PurchaseOrder.area_id.in_(area_ids),
            PurchaseOrder.status.in_(['已确认', '准备入库'])
        )

        # 排序并获取结果
        orders = query.order_by(PurchaseOrder.order_date.desc()).limit(50).all()

        # 构建返回数据
        result_data = []
        for order in orders:
            # 获取供应商信息
            supplier_name = order.supplier.name if order.supplier else '自购'

            # 格式化创建时间
            created_at = order.created_at.strftime('%Y-%m-%d %H:%M') if order.created_at else order.order_date.strftime('%Y-%m-%d')

            result_data.append({
                'id': order.id,
                'order_number': order.order_number,
                'status': order.status,
                'supplier_name': supplier_name,
                'created_at': created_at,
                'order_date': order.order_date.strftime('%Y-%m-%d'),
                'delivery_date': order.delivery_date.strftime('%Y-%m-%d') if order.delivery_date else None,
                'total_amount': float(order.total_amount) if order.total_amount else None,
                'notes': order.notes or ''
            })

        return jsonify({
            'success': True,
            'data': result_data,
            'count': len(result_data)
        })

    except Exception as e:
        current_app.logger.error(f"获取可用采购订单失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取采购订单列表失败，请稍后重试'
        }), 500

@purchase_order_bp.route('/list-json')
@login_required
def list_json():
    """获取采购订单列表（JSON格式）"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    status = request.args.get('status', '')

    # 构建查询
    query = PurchaseOrder.query.filter(PurchaseOrder.area_id.in_(area_ids))

    # 应用筛选条件
    if status:
        query = query.filter_by(status=status)
    else:
        # 默认只显示待确认和已确认状态的订单
        query = query.filter(PurchaseOrder.status.in_(['待确认', '已确认']))

    # 排序并获取结果
    orders = query.order_by(PurchaseOrder.order_date.desc()).all()

    # 获取每个订单是否已经创建了入库单
    order_ids = [order.id for order in orders]
    stock_in_orders = db.session.query(StockIn.purchase_order_id).filter(
        StockIn.purchase_order_id.in_(order_ids)
    ).all()

    # 创建一个集合，包含所有已创建入库单的采购订单ID
    stock_in_order_ids = {order_id for (order_id,) in stock_in_orders}

    # 构建响应数据
    result = []
    for order in orders:
        # 跳过已经创建入库单的订单
        if order.id in stock_in_order_ids:
            continue

        order_data = {
            'id': order.id,
            'order_number': order.order_number,
            'created_at': format_datetime(order.created_at, '%Y-%m-%d'),
            'supplier_name': order.supplier.name if order.supplier else '自购',
            'status': order.status,
            'total_amount': float(order.total_amount) if order.total_amount else 0,
            'has_stock_in': order.id in stock_in_order_ids
        }
        result.append(order_data)

    return jsonify({
        'success': True,
        'data': result
    })

@purchase_order_bp.route('/<int:id>')
@login_required
@check_permission('purchase_order', 'view')
def view(id):
    """查看采购订单详情"""
    # 使用no_autoflush避免自动刷新
    with db.session.no_autoflush:
        order = PurchaseOrder.query.get_or_404(id)

        # 检查用户是否有权限查看
        if not current_user.can_access_area_by_id(order.area_id):
            flash('您没有权限查看该采购订单', 'danger')
            return redirect(url_for('purchase_order.index'))

        # 获取详细的状态信息
        order_status_info = get_order_detailed_status(order)

        # 创建一个临时对象用于显示，不修改数据库中的对象
        display_order = {
            'id': order.id,
            'order_number': order.order_number,
            'supplier_id': order.supplier_id,
            'supplier_name': order.supplier.name if order.supplier else '自购',
            'requisition_id': order.requisition_id,
            'area_id': order.area_id,
            'area_name': order.area.name if order.area else None,
            'total_amount': float(order.total_amount),
            'status': order.status,
            'created_by': order.created_by,
            'creator_name': order.creator.real_name or order.creator.username,
            'approved_by': order.approved_by,
            'approver_name': order.approver.real_name or order.approver.username if order.approver else None,
            'notes': order.notes,
            'order_items': [item.to_dict() for item in order.order_items],
            'has_stock_in': order_status_info['has_stock_in'],
            'status_info': order_status_info
        }

        # 添加状态显示文本
        status_map = {
            'pending': '待确认',
            'confirmed': '已确认',
            'delivered': '已送达',
            'cancelled': '已取消',
            # 添加中文状态值的映射
            '待确认': '待确认',
            '已确认': '已确认',
            '已送达': '已送达',
            '准备入库': '准备入库',
            '已入库': '已入库',
            '已取消': '已取消'
        }
        # 直接存储状态显示文本，而不是使用lambda函数
        display_order['status_display'] = status_map.get(display_order['status'], display_order['status'])

        # 处理日期时间字段
        if isinstance(order.order_date, str):
            try:
                display_order['order_date'] = datetime.strptime(order.order_date, '%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError):
                display_order['order_date'] = datetime.now().replace(microsecond=0)
        else:
            display_order['order_date'] = order.order_date

        if isinstance(order.delivery_date, str):
            try:
                display_order['delivery_date'] = datetime.strptime(order.delivery_date, '%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError):
                display_order['delivery_date'] = None
        else:
            display_order['delivery_date'] = order.delivery_date

        if isinstance(order.expected_delivery_date, str):
            try:
                display_order['expected_delivery_date'] = datetime.strptime(order.expected_delivery_date, '%Y-%m-%d').date()
            except (ValueError, TypeError):
                display_order['expected_delivery_date'] = None
        else:
            display_order['expected_delivery_date'] = order.expected_delivery_date

        # 格式化日期时间字段
        display_order['order_date_formatted'] = format_datetime(display_order['order_date'], '%Y-%m-%d %H:%M:%S') if display_order['order_date'] else ''
        display_order['delivery_date_formatted'] = format_datetime(display_order['delivery_date'], '%Y-%m-%d %H:%M:%S') if display_order['delivery_date'] else ''
        display_order['expected_delivery_date_formatted'] = format_datetime(display_order['expected_delivery_date'], '%Y-%m-%d') if display_order['expected_delivery_date'] else ''
        display_order['created_at'] = format_datetime(order.created_at, '%Y-%m-%d %H:%M:%S') if order.created_at else ''
        display_order['updated_at'] = format_datetime(order.updated_at, '%Y-%m-%d %H:%M:%S') if order.updated_at else ''

    return render_template('purchase_order/view.html', order=display_order)

@purchase_order_bp.route('/create-form', methods=['GET', 'POST'])
@login_required
@check_permission('purchase_order', 'create')
def create_form():
    """使用表单创建采购订单"""
    # 获取当前用户的学校区域
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法创建采购订单', 'danger')
        return redirect(url_for('purchase_order.index'))

    # 只允许为当前用户的学校创建采购订单
    area_choices = [(user_area.id, user_area.name)]

    # 获取与当前学校有合作关系的供应商
    from app.models import SupplierSchoolRelation
    supplier_relations = SupplierSchoolRelation.query.filter_by(
        area_id=user_area.id,
        status=1
    ).all()

    supplier_ids = [rel.supplier_id for rel in supplier_relations]
    suppliers = Supplier.query.filter(
        Supplier.id.in_(supplier_ids),
        Supplier.status == 1
    ).all() if supplier_ids else []

    supplier_choices = [(0, '自购')] + [(s.id, s.name) for s in suppliers]

    # 创建表单
    form = PurchaseOrderForm()
    form.area_id.choices = area_choices
    form.supplier_id.choices = supplier_choices

    # 默认选择当前用户的学校
    if not form.area_id.data:
        form.area_id.data = user_area.id

    # 为表单中的每个item设置supplier_id的choices
    for item_form in form.items:
        item_form.supplier_id.choices = supplier_choices

    # 获取所有食材
    ingredients = Ingredient.query.filter_by(status=1).all()

    # 自动生成批次号
    if not form.batch_number.data:
        form.batch_number.data = f"PO{datetime.now().strftime('%Y%m%d%H%M%S')}"

    # 处理表单提交
    if form.validate_on_submit():
        try:
            # 确保区域ID是用户的学校
            if form.area_id.data != user_area.id:
                flash('只能为您的学校创建采购订单', 'danger')
                return render_template('purchase_order/create_form.html',
                                      form=form,
                                      ingredients=ingredients,
                                      title='创建采购订单',
                                      user_area=user_area)

            # 生成订单编号
            batch_number = form.batch_number.data
            supplier_suffix = form.supplier_id.data if form.supplier_id.data != 0 else "SELF"
            order_number = f"{batch_number}-{supplier_suffix}-{uuid.uuid4().hex[:6]}"

            # 计算总金额
            total_amount = sum(float(item.total_price.data or 0) for item in form.items)

            # 创建采购订单
            order = PurchaseOrder(
                order_number=order_number,
                supplier_id=form.supplier_id.data if form.supplier_id.data != 0 else None,  # 如果是自购，设置为None
                area_id=user_area.id,  # 强制使用用户的学校ID
                total_amount=total_amount,
                order_date=form.order_date.data,
                expected_delivery_date=form.expected_delivery_date.data,
                status='待确认',  # 设置为待确认状态，遵循正常的业务流程
                created_by=current_user.id,
                approved_by=None,  # 初始状态未审核
                notes=form.notes.data
            )
            db.session.add(order)
            db.session.flush()  # 获取ID

            # 添加订单明细
            for item_form in form.items:
                ingredient_id = int(item_form.ingredient_id.data)
                quantity = float(item_form.quantity.data)
                unit_price = float(item_form.unit_price.data)

                # 标准化单位
                unit = item_form.unit.data
                standard_unit = standardize_unit(unit)

                # 查找对应的产品ID
                product_id = None
                if form.supplier_id.data != 0:  # 非自购
                    product = SupplierProduct.query.filter_by(
                        supplier_id=form.supplier_id.data,
                        ingredient_id=ingredient_id
                    ).first()
                    if product:
                        product_id = product.id

                order_item = PurchaseOrderItem(
                    order_id=order.id,
                    product_id=product_id,
                    ingredient_id=ingredient_id,
                    quantity=quantity,
                    unit=standard_unit,
                    unit_price=unit_price,
                    total_price=quantity * unit_price,
                    notes=item_form.notes.data
                )
                db.session.add(order_item)

            # 不再自动创建入库记录，遵循正常的业务流程
            # 入库流程将在采购订单确认、送货和验收后单独处理

            db.session.commit()

            flash(f'采购订单 {order_number} 创建成功，等待确认', 'success')
            return redirect(url_for('purchase_order.view', id=order.id))

        except Exception as e:
            db.session.rollback()
            flash(f'创建采购订单失败: {str(e)}', 'danger')

    # 如果是GET请求或表单验证失败，显示创建页面
    return render_template('purchase_order/create_form.html',
                          form=form,
                          ingredients=ingredients,
                          title='创建采购订单',
                          user_area=user_area)

@purchase_order_bp.route('/<int:id>/confirm', methods=['POST'])
@login_required
@check_permission('purchase_order', 'approve')
def confirm_order(id):
    """确认采购订单"""
    order = PurchaseOrder.query.get_or_404(id)

    # 学校管理员和系统管理员可以确认任何状态的订单
    if not current_user.is_admin() and not current_user.has_role('学校管理员'):
        if order.status != '待确认':
            return jsonify({
                'success': False,
                'message': '只能确认待确认状态的订单'
            })

    try:
        # 使用中文状态
        order.status = '已确认'
        order.confirmed_at = datetime.now()
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '订单已确认'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'确认订单失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '确认订单失败'
        })

@purchase_order_bp.route('/<int:id>/cancel', methods=['POST'])
@login_required
@check_permission('purchase_order', 'approve')
def cancel_order(id):
    """取消采购订单"""
    data = request.get_json()

    if not data or 'reason' not in data:
        return jsonify({
            'success': False,
            'message': '请提供取消原因'
        })

    order = PurchaseOrder.query.get_or_404(id)

    # 学校管理员和系统管理员可以取消任何状态的订单
    if not current_user.is_admin() and not current_user.has_role('学校管理员'):
        if order.status != '待确认':
            return jsonify({
                'success': False,
                'message': '只能取消待确认状态的订单'
            })

    try:
        # 使用中文状态
        order.status = '已取消'
        order.cancelled_at = datetime.now()
        order.cancel_reason = data['reason']
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '订单已取消'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'取消订单失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '取消订单失败'
        })

@purchase_order_bp.route('/<int:id>/deliver', methods=['POST'])
@login_required
@check_permission('purchase_order', 'execute')
def deliver_order(id):
    """标记订单送达并准备入库"""
    data = request.get_json()
    notes = data.get('notes', '')

    order = PurchaseOrder.query.get_or_404(id)

    # 学校管理员和系统管理员可以标记任何状态的订单为准备入库
    if not current_user.is_admin() and not current_user.has_role('学校管理员'):
        if order.status != '已确认':
            return jsonify({
                'success': False,
                'message': '只能标记已确认状态的订单为准备入库'
            })

    try:
        # 使用中文状态
        order.status = '准备入库'  # 修改为准备入库状态
        order.delivered_at = datetime.now()
        order.delivery_notes = notes
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '订单已标记为准备入库，请进行验收检查'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'标记订单准备入库失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '标记订单准备入库失败'
        })

@purchase_order_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
@check_permission('purchase_order', 'delete')
def delete_order(id):
    """删除采购订单"""
    order = PurchaseOrder.query.get_or_404(id)

    # 检查用户权限
    if not current_user.can_access_area_by_id(order.area_id):
        return jsonify({
            'success': False,
            'message': '您没有权限删除该采购订单'
        })

    # 检查是否已经创建了入库单或已入库
    has_stock_in = db.session.query(StockIn).filter_by(purchase_order_id=id).first() is not None
    if has_stock_in or order.status == '已入库':
        return jsonify({
            'success': False,
            'message': '该订单已入库或已创建入库单，无法删除'
        })

    # 学校管理员和系统管理员可以删除任何未入库的订单
    if not current_user.is_admin() and not current_user.has_role('学校管理员'):
        # 普通用户只能删除待确认或已取消状态的未入库订单
        if order.status not in ['待确认', '已取消']:
            return jsonify({
                'success': False,
                'message': '只能删除待确认或已取消状态的订单'
            })

    try:
        # 保存订单信息用于审计日志
        order_info = {
            'id': order.id,
            'order_number': order.order_number,
            'supplier_id': order.supplier_id,
            'supplier_name': order.supplier.name if order.supplier else '自购',
            'area_id': order.area_id,
            'area_name': order.area.name if order.area else None,
            'total_amount': float(order.total_amount) if order.total_amount else 0,
            'status': order.status,
            'order_date': order.order_date
        }

        # 先删除订单明细
        PurchaseOrderItem.query.filter_by(order_id=id).delete()

        # 再删除订单
        db.session.delete(order)

        # 记录审计日志
        from app.utils.log_activity import log_activity
        log_activity(
            action='delete',
            resource_type='PurchaseOrder',
            resource_id=id,
            area_id=order_info['area_id'],
            details=order_info
        )

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '采购订单删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除采购订单失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'删除采购订单失败: {str(e)}'
        })

@purchase_order_bp.route('/<int:id>/json')
@login_required
def get_order_json(id):
    """获取采购订单JSON数据"""
    try:
        # 获取采购订单
        order = PurchaseOrder.query.get_or_404(id)

        # 简化权限检查，允许所有登录用户查看
        if not current_user.is_admin() and not current_user.can_access_area_by_id(order.area_id):
            return jsonify({
                'success': False,
                'message': '您没有权限查看该采购订单'
            }), 403

        # 获取订单项目
        items = []
        for item in order.items:
            item_data = {
                'id': item.id,
                'ingredient_id': item.ingredient_id,
                'ingredient_name': item.ingredient.name if item.ingredient else '未知食材',
                'quantity': float(item.quantity) if item.quantity else 0,
                'unit': item.unit,
                'unit_price': float(item.unit_price) if item.unit_price else 0,
                'total_price': float(item.quantity * item.unit_price) if item.quantity and item.unit_price else 0
            }
            items.append(item_data)

        # 构建订单数据
        order_data = {
            'id': order.id,
            'order_number': order.order_number,
            'order_date': order.order_date.strftime('%Y-%m-%d') if order.order_date else None,
            'delivery_date': order.delivery_date.strftime('%Y-%m-%d') if order.delivery_date else None,
            'status': order.status,
            'total_amount': float(order.total_amount) if order.total_amount else 0,
            'notes': order.notes,
            'supplier_id': order.supplier_id,
            'supplier_name': order.supplier.name if order.supplier else '自购',
            'area_id': order.area_id,
            'area_name': order.area.name if order.area else '未知区域',
            'creator_id': order.creator_id,
            'creator_name': order.creator.username if order.creator else '未知用户',
            'created_at': order.created_at.strftime('%Y-%m-%d %H:%M:%S') if order.created_at else None,
            'items': items
        }

        return jsonify({
            'success': True,
            'data': order_data
        })

    except Exception as e:
        current_app.logger.error(f"获取采购订单JSON数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取采购订单JSON数据失败: {str(e)}'
        }), 500

@purchase_order_bp.route('/print/<int:order_id>')
@login_required
@check_permission('purchase_order', 'view')
def print_order(order_id):
    """打印采购订单"""
    try:
        # 获取订单
        order = PurchaseOrder.query.get_or_404(order_id)

        # 检查权限
        if not current_user.can_access_area(order.area):
            flash('您没有权限查看该区域的订单', 'danger')
            return redirect(url_for('purchase_order.index'))

        # 获取订单项
        order_items = PurchaseOrderItem.query.filter_by(order_id=order_id).all()

        # 获取关联信息
        area = AdministrativeArea.query.get(order.area_id)
        creator = User.query.get(order.created_by) if order.created_by else None
        approver = User.query.get(order.approved_by) if order.approved_by else None
        supplier = Supplier.query.get(order.supplier_id) if order.supplier_id else None

        # 添加显示信息
        order.area_name = area.name if area else current_user.area.name
        order.creator_name = creator.real_name if creator and creator.real_name else (creator.username if creator else '-')
        order.approver_name = approver.real_name if approver and approver.real_name else (approver.username if approver else '-')
        order.supplier_name = supplier.name if supplier else '自购'

        # 添加状态显示文本
        order.status_display = order.get_status_display()

        # 处理订单项
        for item in order_items:
            # 获取食材名称
            ingredient = Ingredient.query.get(item.ingredient_id)
            item.ingredient_name = ingredient.name if ingredient else f'未知食材({item.ingredient_id})'

            # 获取供应商名称
            if item.product_id:
                product = SupplierProduct.query.get(item.product_id)
                if product and product.supplier:
                    item.supplier_name = product.supplier.name
                else:
                    item.supplier_name = '自购'
            else:
                item.supplier_name = '自购'

        # 添加订单项到订单对象
        order.order_items = order_items

        return render_template('purchase_order/print.html',
                              order=order,
                              current_time=datetime.now())

    except Exception as e:
        current_app.logger.error(f"打印采购订单出错: {str(e)}")
        flash(f'打印订单失败: {str(e)}', 'danger')
        return redirect(url_for('purchase_order.index'))

@purchase_order_bp.route('/check-updates', methods=['POST'])
@login_required
def check_updates():
    """检查采购订单状态更新"""
    try:
        data = request.get_json()
        if not data or 'order_ids' not in data:
            return jsonify({
                'success': False,
                'message': '参数错误'
            })

        order_ids = data['order_ids']
        if not order_ids:
            return jsonify({
                'success': True,
                'updates': []
            })

        # 获取当前用户可访问的区域
        accessible_areas = current_user.get_accessible_areas()
        area_ids = [area.id for area in accessible_areas]

        # 查询订单状态
        orders = PurchaseOrder.query.filter(
            PurchaseOrder.id.in_(order_ids),
            PurchaseOrder.area_id.in_(area_ids)
        ).all()

        updates = []
        for order in orders:
            # 这里可以添加更复杂的状态变更检测逻辑
            # 目前简单返回当前状态，实际应用中可以与缓存的状态进行比较
            updates.append({
                'order_id': order.id,
                'order_number': order.order_number,
                'current_status': order.status,
                'new_status': order.status,
                'status_changed': False,  # 实际应用中需要比较状态变化
                'last_updated': order.updated_at.isoformat() if hasattr(order, 'updated_at') and order.updated_at else None
            })

        return jsonify({
            'success': True,
            'updates': updates
        })

    except Exception as e:
        current_app.logger.error(f'检查订单更新失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '检查更新失败'
        })

