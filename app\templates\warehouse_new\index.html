{% extends 'base.html' %}

{% block title %}仓库管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">仓库管理</h5>
                    <div class="card-tools">
                        <a href="{{ url_for('warehouse_new.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 创建仓库
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 筛选表单 -->
                    <form method="GET" action="{{ url_for('warehouse_new.index') }}" class="mb-3">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="area_id">区域</label>
                                    <select name="area_id" id="area_id" class="form-control">
                                        <option value="">全部</option>
                                        {% for area in areas %}
                                        <option value="{{ area.id }}" {% if area_id == area.id %}selected{% endif %}>{{ area.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="status">状态</label>
                                    <select name="status" id="status" class="form-control">
                                        <option value="">全部</option>
                                        <option value="正常" {% if status == '正常' %}selected{% endif %}>正常</option>
                                        <option value="维护中" {% if status == '维护中' %}selected{% endif %}>维护中</option>
                                        <option value="已关闭" {% if status == '已关闭' %}selected{% endif %}>已关闭</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-block">筛选</button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <a href="{{ url_for('warehouse_new.index') }}" class="btn btn-secondary btn-block">重置</a>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <!-- 仓库列表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>名称</th>
                                    <th>区域</th>
                                    <th>位置</th>
                                    <th>管理员</th>
                                    <th>容量</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for warehouse in warehouses %}
                                <tr>
                                    <td>{{ warehouse.id }}</td>
                                    <td>{{ warehouse.name }}</td>
                                    <td>{{ warehouse.area.name }}</td>
                                    <td>{{ warehouse.location }}</td>
                                    <td>{{ warehouse.manager.real_name or warehouse.manager.username }}</td>
                                    <td>{{ warehouse.capacity }} {{ warehouse.capacity_unit }}</td>
                                    <td>
                                        {% if warehouse.status == '正常' %}
                                        <span class="badge badge-success">{{ warehouse.status }}</span>
                                        {% elif warehouse.status == '维护中' %}
                                        <span class="badge badge-warning">{{ warehouse.status }}</span>
                                        {% else %}
                                        <span class="badge badge-danger">{{ warehouse.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ warehouse.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                    <td>
                                        <a href="{{ url_for('warehouse_new.view', id=warehouse.id) }}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                        <a href="{{ url_for('warehouse_new.edit', id=warehouse.id) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="9" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('warehouse_new.index', page=pagination.prev_num, status=status, area_id=area_id) }}">上一页</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page in pagination.iter_pages() %}
                                {% if page %}
                                    {% if page != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('warehouse_new.index', page=page, status=status, area_id=area_id) }}">{{ page }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <a class="page-link" href="#">{{ page }}</a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('warehouse_new.index', page=pagination.next_num, status=status, area_id=area_id) }}">下一页</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 使用Select2增强下拉框
        $('.form-control').select2({
            theme: 'bootstrap4',
            width: '100%'
        });
    });
</script>
{% endblock %}
