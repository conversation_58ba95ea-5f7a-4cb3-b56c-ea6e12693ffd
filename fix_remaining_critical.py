#!/usr/bin/env python3
"""
修复剩余关键问题脚本
处理更复杂的删除确认和表单验证模式
"""

import os
import re
import glob

def fix_remaining_critical_issues():
    """修复剩余的关键问题"""
    print("🔧 修复剩余的关键问题")
    print("=" * 50)
    
    html_files = glob.glob('app/templates/**/*.html', recursive=True)
    
    fixed_files = 0
    total_fixes = 0
    
    for file_path in html_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = 0
            
            # 1. 修复各种删除确认模式
            delete_patterns = [
                # 简单的 confirm 删除
                r'onclick\s*=\s*["\']return\s+confirm\s*\([^)]*\)["\']',
                # 带 && 的删除确认
                r'onclick\s*=\s*["\']return\s+confirm\s*\([^)]*\)\s*&&[^"\']*["\']',
                # if confirm 模式
                r'onclick\s*=\s*["\']if\s*\(\s*confirm\s*\([^)]*\)\s*\)[^"\']*["\']',
                # 包含 delete 关键字的 onclick
                r'onclick\s*=\s*["\'][^"\']*delete[^"\']*\([^)]*\)[^"\']*["\']',
                # 包含 remove 关键字的 onclick
                r'onclick\s*=\s*["\'][^"\']*remove[^"\']*\([^)]*\)[^"\']*["\']',
            ]
            
            for pattern in delete_patterns:
                matches = list(re.finditer(pattern, content, re.IGNORECASE))
                for match in matches:
                    original_onclick = match.group(0)
                    
                    # 提取 onclick 内容
                    onclick_match = re.search(r'["\']([^"\']*)["\']', original_onclick)
                    if onclick_match:
                        onclick_content = onclick_match.group(1)
                        
                        # 生成替代方案
                        replacement = f'data-action="critical-confirm" data-original-onclick="{onclick_content}" style="cursor: pointer;"'
                        
                        content = content.replace(original_onclick, replacement)
                        file_fixes += 1
            
            # 2. 修复表单验证
            submit_patterns = [
                r'onsubmit\s*=\s*["\']return\s+[^"\']*["\']',
            ]
            
            for pattern in submit_patterns:
                matches = list(re.finditer(pattern, content, re.IGNORECASE))
                for match in matches:
                    original_onsubmit = match.group(0)
                    
                    # 提取 onsubmit 内容
                    submit_match = re.search(r'["\']([^"\']*)["\']', original_onsubmit)
                    if submit_match:
                        submit_content = submit_match.group(1)
                        
                        # 生成替代方案
                        replacement = f'data-validation="critical" data-original-onsubmit="{submit_content}"'
                        
                        content = content.replace(original_onsubmit, replacement)
                        file_fixes += 1
            
            # 如果有修改，保存文件
            if content != original_content:
                # 创建备份
                backup_path = file_path + '.remaining.backup'
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                
                # 保存修复后的文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                fixed_files += 1
                total_fixes += file_fixes
                print(f"✅ {file_path}: 修复了 {file_fixes} 个关键事件")
                
        except Exception as e:
            print(f"❌ 处理文件失败 {file_path}: {e}")
    
    print(f"\n📊 剩余关键问题修复总结:")
    print(f"   修复文件数: {fixed_files}")
    print(f"   总修复数: {total_fixes}")
    
    return fixed_files, total_fixes

def update_critical_handler():
    """更新关键事件处理器"""
    print("\n📝 更新关键事件处理器...")
    
    handler_script = '''/**
 * 增强的关键事件处理器
 * 处理所有类型的删除确认和表单验证
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // 处理所有关键确认操作
    document.querySelectorAll('[data-action="critical-confirm"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const originalOnclick = this.getAttribute('data-original-onclick');
            
            // 检查是否包含 confirm
            if (originalOnclick.includes('confirm(')) {
                // 提取确认消息
                const confirmMatch = originalOnclick.match(/confirm\\s*\\(\\s*['"](.*?)['"]\\s*\\)/);
                const confirmMessage = confirmMatch ? confirmMatch[1] : '确定要执行此操作吗？';
                
                if (confirm(confirmMessage)) {
                    try {
                        // 执行原有逻辑
                        executeOriginalCode(originalOnclick);
                    } catch (error) {
                        console.error('执行失败:', error);
                        alert('操作失败，请重试');
                    }
                }
            } else {
                // 对于没有确认的删除操作，添加确认
                if (originalOnclick.toLowerCase().includes('delete') || 
                    originalOnclick.toLowerCase().includes('remove')) {
                    
                    if (confirm('确定要删除吗？')) {
                        try {
                            executeOriginalCode(originalOnclick);
                        } catch (error) {
                            console.error('删除失败:', error);
                            alert('删除失败，请重试');
                        }
                    }
                } else {
                    // 其他操作直接执行
                    try {
                        executeOriginalCode(originalOnclick);
                    } catch (error) {
                        console.error('执行失败:', error);
                    }
                }
            }
        });
    });
    
    // 处理关键表单验证
    document.querySelectorAll('[data-validation="critical"]').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const originalOnsubmit = this.getAttribute('data-original-onsubmit');
            
            try {
                // 执行原有验证逻辑
                const result = executeOriginalCode(originalOnsubmit);
                
                // 如果返回 false，阻止提交
                if (result === false) {
                    e.preventDefault();
                    console.log('表单验证失败，已阻止提交');
                }
            } catch (error) {
                console.error('表单验证失败:', error);
                e.preventDefault();
                alert('表单验证失败，请检查输入');
            }
        });
    });
    
    // 处理之前的删除确认（兼容性）
    document.querySelectorAll('[data-action="delete-confirm"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const functionCode = this.getAttribute('data-function');
            const confirmMessage = '确定要执行此操作吗？';
            
            if (confirm(confirmMessage)) {
                try {
                    executeOriginalCode(functionCode);
                } catch (error) {
                    console.error('执行失败:', error);
                    alert('操作失败，请重试');
                }
            }
        });
    });
    
    // 处理之前的表单验证（兼容性）
    document.querySelectorAll('[data-validation="true"]').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const validator = this.getAttribute('data-validator');
            
            try {
                const result = executeOriginalCode(validator);
                if (result === false) {
                    e.preventDefault();
                    console.log('表单验证失败');
                }
            } catch (error) {
                console.error('验证失败:', error);
                e.preventDefault();
                alert('表单验证失败，请检查输入');
            }
        });
    });
    
    // 安全执行原有代码的函数
    function executeOriginalCode(code) {
        if (!code) return;
        
        try {
            // 清理代码
            code = code.trim();
            
            // 如果是 return 语句，提取返回值
            if (code.startsWith('return ')) {
                code = code.substring(7);
                return eval(code);
            } else {
                // 直接执行
                eval(code);
                return true;
            }
        } catch (error) {
            console.error('代码执行失败:', error);
            throw error;
        }
    }
    
    console.log('✅ 增强的关键事件处理器已加载');
});'''
    
    # 更新处理器文件
    handler_path = 'app/static/js/critical-handler-simple.js'
    
    with open(handler_path, 'w', encoding='utf-8') as f:
        f.write(handler_script)
    
    print(f"✅ 关键事件处理器已更新")

def verify_critical_fixes():
    """验证关键修复"""
    print("\n🔍 验证关键修复...")
    
    # 统计修复情况
    html_files = glob.glob('app/templates/**/*.html', recursive=True)
    
    critical_data_attrs = 0
    remaining_onclick = 0
    remaining_onsubmit = 0
    
    for file_path in html_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 统计修复后的属性
            critical_data_attrs += len(re.findall(r'data-action="critical-confirm"', content))
            critical_data_attrs += len(re.findall(r'data-validation="critical"', content))
            critical_data_attrs += len(re.findall(r'data-action="delete-confirm"', content))
            critical_data_attrs += len(re.findall(r'data-validation="true"', content))
            
            # 统计剩余的问题
            remaining_onclick += len(re.findall(r'onclick\s*=\s*["\'][^"\']*confirm[^"\']*["\']', content, re.IGNORECASE))
            remaining_onclick += len(re.findall(r'onclick\s*=\s*["\'][^"\']*delete[^"\']*["\']', content, re.IGNORECASE))
            remaining_onsubmit += len(re.findall(r'onsubmit\s*=\s*["\'][^"\']*["\']', content, re.IGNORECASE))
            
        except Exception:
            continue
    
    print(f"📊 验证结果:")
    print(f"   已修复的关键事件: {critical_data_attrs} 个")
    print(f"   剩余的 onclick 问题: {remaining_onclick} 个")
    print(f"   剩余的 onsubmit 问题: {remaining_onsubmit} 个")
    
    if remaining_onclick + remaining_onsubmit == 0:
        print("🎉 所有关键问题已修复！")
        return True
    elif remaining_onclick + remaining_onsubmit < 10:
        print("✅ 大部分关键问题已修复")
        return True
    else:
        print("⚠️ 还有较多关键问题需要处理")
        return False

if __name__ == '__main__':
    print("🔧 修复剩余关键问题工具")
    print("=" * 60)
    
    # 1. 修复剩余关键问题
    fixed_files, total_fixes = fix_remaining_critical_issues()
    
    # 2. 更新关键事件处理器
    update_critical_handler()
    
    # 3. 验证修复效果
    success = verify_critical_fixes()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 关键问题修复完成！")
        print("🔒 删除确认和表单验证功能已全面修复")
        print("✅ 系统安全性得到保障")
    else:
        print("⚠️ 还有部分问题需要手动处理")
    
    print(f"\n📈 本次修复统计:")
    print(f"   修复文件数: {fixed_files}")
    print(f"   修复事件数: {total_fixes}")
    
    print(f"\n🚀 后续步骤:")
    print("1. 重启应用服务器")
    print("2. 测试删除功能的确认对话框")
    print("3. 测试表单验证功能")
    print("4. 检查浏览器控制台的 CSP 错误")
    
    print(f"\n💡 修复说明:")
    print("- 所有危险的删除操作都会显示确认对话框")
    print("- 表单验证功能保持原有逻辑")
    print("- 使用安全的数据属性替代内联事件")
    print("- 兼容之前的修复，不会重复处理")
