/**
 * 增强的关键事件处理器
 * 处理所有类型的删除确认和表单验证
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // 处理所有关键确认操作
    document.querySelectorAll('[data-action="critical-confirm"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const originalOnclick = this.getAttribute('data-original-onclick');
            
            // 检查是否包含 confirm
            if (originalOnclick.includes('confirm(')) {
                // 提取确认消息
                const confirmMatch = originalOnclick.match(/confirm\s*\(\s*['"](.*?)['"]\s*\)/);
                const confirmMessage = confirmMatch ? confirmMatch[1] : '确定要执行此操作吗？';
                
                if (confirm(confirmMessage)) {
                    try {
                        // 执行原有逻辑
                        executeOriginalCode(originalOnclick);
                    } catch (error) {
                        console.error('执行失败:', error);
                        alert('操作失败，请重试');
                    }
                }
            } else {
                // 对于没有确认的删除操作，添加确认
                if (originalOnclick.toLowerCase().includes('delete') || 
                    originalOnclick.toLowerCase().includes('remove')) {
                    
                    if (confirm('确定要删除吗？')) {
                        try {
                            executeOriginalCode(originalOnclick);
                        } catch (error) {
                            console.error('删除失败:', error);
                            alert('删除失败，请重试');
                        }
                    }
                } else {
                    // 其他操作直接执行
                    try {
                        executeOriginalCode(originalOnclick);
                    } catch (error) {
                        console.error('执行失败:', error);
                    }
                }
            }
        });
    });
    
    // 处理关键表单验证
    document.querySelectorAll('[data-validation="critical"]').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const originalOnsubmit = this.getAttribute('data-original-onsubmit');
            
            try {
                // 执行原有验证逻辑
                const result = executeOriginalCode(originalOnsubmit);
                
                // 如果返回 false，阻止提交
                if (result === false) {
                    e.preventDefault();
                    console.log('表单验证失败，已阻止提交');
                }
            } catch (error) {
                console.error('表单验证失败:', error);
                e.preventDefault();
                alert('表单验证失败，请检查输入');
            }
        });
    });
    
    // 处理之前的删除确认（兼容性）
    document.querySelectorAll('[data-action="delete-confirm"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const functionCode = this.getAttribute('data-function');
            const confirmMessage = '确定要执行此操作吗？';
            
            if (confirm(confirmMessage)) {
                try {
                    executeOriginalCode(functionCode);
                } catch (error) {
                    console.error('执行失败:', error);
                    alert('操作失败，请重试');
                }
            }
        });
    });
    
    // 处理之前的表单验证（兼容性）
    document.querySelectorAll('[data-validation="true"]').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const validator = this.getAttribute('data-validator');
            
            try {
                const result = executeOriginalCode(validator);
                if (result === false) {
                    e.preventDefault();
                    console.log('表单验证失败');
                }
            } catch (error) {
                console.error('验证失败:', error);
                e.preventDefault();
                alert('表单验证失败，请检查输入');
            }
        });
    });
    
    // 安全执行原有代码的函数
    function executeOriginalCode(code) {
        if (!code) return;
        
        try {
            // 清理代码
            code = code.trim();
            
            // 如果是 return 语句，提取返回值
            if (code.startsWith('return ')) {
                code = code.substring(7);
                return eval(code);
            } else {
                // 直接执行
                eval(code);
                return true;
            }
        } catch (error) {
            console.error('代码执行失败:', error);
            throw error;
        }
    }
    
    console.log('✅ 增强的关键事件处理器已加载');
});