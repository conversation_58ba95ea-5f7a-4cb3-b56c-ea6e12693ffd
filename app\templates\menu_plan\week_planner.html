{% extends 'base.html' %}

{% block title %}周菜单安排 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
  .meal-cell {
    min-height: 120px;
    border: 1px solid #ddd;
    padding: 10px;
    position: relative;
  }
  .meal-header {
    font-weight: bold;
    margin-bottom: 10px;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
  }
  .meal-content {
    min-height: 80px;
  }
  .meal-item {
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px 8px;
    margin-bottom: 5px;
    cursor: move;
    position: relative;
  }
  .meal-item .badge {
    margin-left: 5px;
  }
  .meal-item .remove-recipe {
    padding: 0 5px;
    margin-left: 5px;
    font-size: 12px;
  }
  .recipe-list {
    max-height: 400px;
    overflow-y: auto;
  }
  .recipe-option {
    cursor: pointer;
    padding: 5px 10px;
    border-bottom: 1px solid #eee;
  }
  .recipe-option:hover {
    background-color: #f0f0f0;
  }
  .week-date {
    font-weight: bold;
  }
  .drop-hover {
    background-color: #e9ecef;
  }
  .sync-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 10px 15px;
    border-radius: 4px;
    z-index: 1000;
    display: none;
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row mb-4">
    <div class="col-md-8">
      <h2>周菜单安排</h2>
    </div>
    <div class="col-md-4 text-right">
      <button id="save-all-btn" class="btn btn-primary">
        <i class="fas fa-save"></i> 保存所有菜单
      </button>
      <a href="{{ url_for('menu_plan.print_week_menu', area_id=area_id, week_start=week_start) }}" class="btn btn-info" target="_blank">
        <i class="fas fa-print"></i> 打印菜单
      </a>
    </div>
  </div>

  <div class="row">
    <div class="col-md-3">
      <!-- 筛选和选择区域 -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">筛选条件</h5>
        </div>
        <div class="card-body">
          <form id="filter-form" method="get">
            <div class="form-group">
              <label for="area_id">区域</label>
              <select class="form-control" id="area_id" name="area_id">
                {% for area in areas %}
                <option value="{{ area.id }}" {% if area.id == area_id %}selected{% endif %}>
                  {{ area.get_level_name() }} - {{ area.name }}
                </option>
                {% endfor %}
              </select>
            </div>
            <div class="form-group">
              <label for="week_start">周开始日期</label>
              <input type="date" class="form-control" id="week_start" name="week_start" value="{{ week_start }}">
            </div>
            <button type="submit" class="btn btn-primary btn-block">应用筛选</button>
          </form>
        </div>
      </div>

      <!-- 菜品列表 -->
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">菜品列表</h5>
          <div class="input-group mt-2">
            <input type="text" class="form-control" id="recipe-search" placeholder="搜索菜品...">
            <div class="input-group-append">
              <button class="btn btn-outline-secondary" type="button">
                <i class="fas fa-search"></i>
              </button>
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="recipe-list">
            {% for recipe in recipes %}
            <div class="recipe-option" data-recipe-id="{{ recipe.id }}" data-recipe-name="{{ recipe.name }}">
              <span class="recipe-name">{{ recipe.name }}</span>
              <small class="text-muted ml-2">{{ recipe.category }}</small>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>

    <!-- 周菜单表格 -->
    <div class="col-md-9">
      <div class="table-responsive">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th style="width: 12%">日期</th>
              <th style="width: 29%">早餐</th>
              <th style="width: 29%">午餐</th>
              <th style="width: 29%">晚餐</th>
            </tr>
          </thead>
          <tbody>
            {% for date_str, day_data in week_dates.items() %}
            <tr>
              <td>
                <div class="week-date">{{ day_data.weekday }}</div>
                <div>{{ date_str[5:] }}</div>
              </td>
              {% for meal_type in ['早餐', '午餐', '晚餐'] %}
              <td class="meal-cell" data-date="{{ date_str }}" data-meal-type="{{ meal_type }}">
                <div class="meal-header">{{ meal_type }}</div>
                <div class="meal-content">
                  {% if existing_menu.get(date_str, {}).get(meal_type) %}
                  {% for recipe in existing_menu[date_str][meal_type] %}
                  <div class="meal-item" data-recipe-id="{{ recipe.id }}">
                    {{ recipe.name }}
                    <span class="badge badge-info">{{ recipe.quantity }}份</span>
                    <button type="button" class="btn btn-sm btn-danger float-right remove-recipe">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                  {% endfor %}
                  {% endif %}
                </div>
              </td>
              {% endfor %}
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- 同步状态提示 -->
<div id="sync-status" class="sync-status"></div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
  $(document).ready(function() {
    // 初始化拖曳功能
    $('.meal-content').sortable({
      connectWith: '.meal-content',
      placeholder: 'drop-hover',
      handle: '.meal-item',
      update: function(event, ui) {
        updateMenuData();
      }
    });

    // 食谱选择
    $('.recipe-option').click(function() {
      var recipeId = $(this).data('recipe-id');
      var recipeName = $(this).data('recipe-name');
      
      // 创建新的菜品项
      var mealItem = $('<div class="meal-item" data-recipe-id="' + recipeId + '">' +
        recipeName +
        '<span class="badge badge-info">1份</span>' +
        '<button type="button" class="btn btn-sm btn-danger float-right remove-recipe">' +
        '<i class="fas fa-times"></i>' +
        '</button>' +
        '</div>');

      // 添加到当前选中的餐次
      $('.meal-cell.drop-hover .meal-content').append(mealItem);
      
      // 更新菜单数据
      updateMenuData();
    });

    // 删除菜品
    $(document).on('click', '.remove-recipe', function() {
      $(this).closest('.meal-item').remove();
      updateMenuData();
    });

    // 保存所有菜单
    $('#save-all-btn').click(function() {
      var menuData = {
        area_id: $('#area_id').val(),
        week_start: $('#week_start').val(),
        menu_data: window.menuData
      };

      $.ajax({
        url: '/api/menu-plan/week/save',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(menuData),
        success: function(response) {
          if (response.success) {
            showSyncStatus('保存成功', 'success');
          } else {
            showSyncStatus('保存失败: ' + response.message, 'error');
          }
        },
        error: function() {
          showSyncStatus('保存失败，请重试', 'error');
        }
      });
    });

    // 搜索菜品
    $('#recipe-search').on('input', function() {
      var searchText = $(this).val().toLowerCase();
      $('.recipe-option').each(function() {
        var recipeName = $(this).data('recipe-name').toLowerCase();
        if (recipeName.indexOf(searchText) > -1) {
          $(this).show();
        } else {
          $(this).hide();
        }
      });
    });

    // 初始化菜单数据
    window.menuData = {};
    updateMenuData();

    // 显示同步状态
    function showSyncStatus(message, type) {
      var $status = $('#sync-status');
      $status.text(message);
      $status.removeClass('bg-success bg-danger');
      
      if (type === 'success') {
        $status.addClass('bg-success text-white');
      } else {
        $status.addClass('bg-danger text-white');
      }
      
      $status.fadeIn();
      setTimeout(function() {
        $status.fadeOut();
      }, 3000);
    }

    // 更新菜单数据
    function updateMenuData() {
      var menuData = {};
      
      $('.meal-cell').each(function() {
        var date = $(this).data('date');
        var mealType = $(this).data('meal-type');
        var recipes = [];

        $(this).find('.meal-item').each(function() {
          var recipeId = $(this).data('recipe-id');
          var recipeName = $(this).text().trim();
          var quantity = $(this).find('.badge').text().match(/\d+/)[0];

          recipes.push({
            id: recipeId,
            name: recipeName,
            quantity: parseInt(quantity)
          });
        });

        if (!menuData[date]) {
          menuData[date] = {};
        }

        menuData[date][mealType] = recipes;
      });
      
      window.menuData = menuData;
    }
  });
</script>
{% endblock %}
