{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .card-header-custom {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    .order-status {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
    }
    .status-pending {
        background-color: #ffeeba;
        color: #856404;
    }
    .status-approved {
        background-color: #d4edda;
        color: #155724;
    }
    .status-rejected {
        background-color: #f8d7da;
        color: #721c24;
    }
    .status-completed {
        background-color: #d1ecf1;
        color: #0c5460;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">入库检查</h1>
    </div>

    <!-- 订单选择卡片 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">选择采购订单</h6>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label for="orderSelect">请选择需要检查的采购订单</label>
                <select class="form-control" id="orderSelect" onchange="loadOrderItems()">
                    <option value="">-- 请选择订单 --</option>
                    {% for order in orders %}
                    <option value="{{ order.id }}">{{ order.order_number }} - {{ order.supplier_name }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
    </div>

    <!-- 订单信息卡片 -->
    <div id="orderInfoCard" class="card shadow mb-4" style="display:none;">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">订单信息</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <p><strong>订单编号：</strong> <span id="orderId"></span></p>
                    <p><strong>供应商：</strong> <span id="supplierName"></span></p>
                </div>
                <div class="col-md-4">
                    <p><strong>订单日期：</strong> <span id="orderDate"></span></p>
                    <p><strong>状态：</strong> <span id="orderStatus"></span></p>
                </div>
                <div class="col-md-4">
                    <p><strong>总金额：</strong> <span id="totalAmount"></span></p>
                    <p><strong>仓库：</strong> <span id="warehouse"></span></p>
                </div>
            </div>
        </div>
    </div>

    <!-- 食材列表卡片 -->
    <div id="itemsCard" class="card shadow mb-4" style="display:none;">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">食材列表</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="itemsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>食材名称</th>
                            <th>数量</th>
                            <th>单位</th>
                            <th>单价</th>
                            <th>金额</th>
                            <th>检查状态</th>
                        </tr>
                    </thead>
                    <tbody id="itemsTableBody">
                        <!-- 食材列表将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <div class="text-center mt-4">
                <button id="startInspectionBtn" class="btn btn-primary" onclick="startInspection()">开始检查</button>
            </div>
        </div>
    </div>

    <!-- 检查表单卡片 -->
    <div id="inspectionFormCard" class="card shadow mb-4" style="display:none;">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">入库检查表单</h6>
        </div>
        <div class="card-body">
            <form id="inspectionForm" method="post" action="{{ url_for('inspection.save_inspection') }}" enctype="multipart/form-data">
                <input type="hidden" id="purchaseOrderId" name="purchase_order_id">

                <div class="form-group">
                    <label for="inspectionStatus">检查结果</label>
                    <select class="form-control" id="inspectionStatus" name="status" required>
                        <option value="normal">通过</option>
                        <option value="abnormal">不通过</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="inspectionDescription">检查说明</label>
                    <textarea class="form-control" id="inspectionDescription" name="description" rows="3" placeholder="请输入检查说明..."></textarea>
                </div>

                <div class="form-group">
                    <label for="inspectionPhotos">上传照片（可选）</label>
                    <input type="file" class="form-control-file" id="inspectionPhotos" name="photos" multiple>
                </div>

                <div class="text-center mt-4">
                    <button type="button" class="btn btn-secondary mr-2" onclick="cancelInspection()">取消</button>
                    <button type="submit" class="btn btn-success">保存检查结果</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    function loadOrderItems() {
        const orderId = document.getElementById('orderSelect').value;
        if (!orderId) {
            document.getElementById('orderInfoCard').style.display = 'none';
            document.getElementById('itemsCard').style.display = 'none';
            document.getElementById('inspectionFormCard').style.display = 'none';
            return;
        }

        // 发送AJAX请求获取订单信息和食材列表
        fetch(`/api/purchase-orders/${orderId}`)
            .then(response => response.json())
            .then(data => {
                // 显示订单信息
                document.getElementById('orderId').textContent = data.order_number;
                document.getElementById('supplierName').textContent = data.supplier_name;
                document.getElementById('orderDate').textContent = data.order_date;
                document.getElementById('totalAmount').textContent = `¥${parseFloat(data.total_amount).toFixed(2)}`;
                document.getElementById('warehouse').textContent = data.warehouse_name;

                // 设置订单状态
                const statusElement = document.getElementById('orderStatus');
                statusElement.textContent = data.status;
                statusElement.className = 'order-status';
                if (data.status === '待审核') {
                    statusElement.classList.add('status-pending');
                } else if (data.status === '已审核') {
                    statusElement.classList.add('status-approved');
                } else if (data.status === '已拒绝') {
                    statusElement.classList.add('status-rejected');
                } else if (data.status === '已入库') {
                    statusElement.classList.add('status-completed');
                }

                document.getElementById('orderInfoCard').style.display = 'block';

                // 显示食材列表
                const tbody = document.getElementById('itemsTableBody');
                tbody.innerHTML = '';

                data.items.forEach(item => {
                    const row = document.createElement('tr');
                    const amount = parseFloat(item.quantity) * parseFloat(item.price);
                    row.innerHTML = `
                        <td>${item.name}</td>
                        <td>${item.quantity}</td>
                        <td>${item.unit}</td>
                        <td>¥${parseFloat(item.price).toFixed(2)}</td>
                        <td>¥${amount.toFixed(2)}</td>
                        <td><span class="badge badge-secondary">待检查</span></td>
                    `;
                    tbody.appendChild(row);
                });

                document.getElementById('itemsCard').style.display = 'block';

                // 设置检查表单的订单ID
                document.getElementById('purchaseOrderId').value = orderId;
            })
            .catch(error => {
                console.error('获取订单信息失败:', error);
                alert('获取订单信息失败，请重试');
            });
    }

    function startInspection() {
        document.getElementById('inspectionFormCard').style.display = 'block';
        document.getElementById('startInspectionBtn').disabled = true;

        // 滚动到检查表单
        document.getElementById('inspectionFormCard').scrollIntoView({ behavior: 'smooth' });
    }

    function cancelInspection() {
        document.getElementById('inspectionFormCard').style.display = 'none';
        document.getElementById('startInspectionBtn').disabled = false;
    }
</script>
{% endblock %}
