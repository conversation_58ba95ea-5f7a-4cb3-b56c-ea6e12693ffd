{% extends 'base.html' %}

{% block title %}创建菜单计划{% endblock %}

{% block styles %}
{{ super() }}
<link href="{{ url_for('static', filename='vendor/select2/css/select2.min.css') }}" rel="stylesheet" />
<link href="{{ url_for('static', filename='vendor/select2/css/select2-bootstrap4.min.css') }}" rel="stylesheet" />
<style>
    .recipe-list {
        max-height: 500px;
        overflow-y: auto;
        padding-right: 10px;
    }
    .recipe-list::-webkit-scrollbar {
        width: 6px;
    }
    .recipe-list::-webkit-scrollbar-thumb {
        background-color: rgba(0,0,0,0.2);
        border-radius: 3px;
    }
    .recipe-counter {
        font-weight: bold;
        color: #666;
    }
    .recipe-item {
        transition: all 0.3s ease;
    }
    .recipe-item:hover {
        box-shadow: 0 0 8px rgba(0,0,0,0.1);
    }
    .recipe-category-list {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px;
    }
    .recipe-category-item {
        cursor: pointer;
        padding: 5px;
        border-radius: 3px;
    }
    .recipe-category-item:hover {
        background-color: #f8f9fa;
    }
    .recipe-category-item.active {
        background-color: #007bff;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">创建菜单计划</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('menu_plan.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('menu_plan.create') }}"><div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="area_id">区域 <span class="text-danger">*</span></label>
                                    <select name="area_id" id="area_id" class="form-control select2" required>
                                        <option value="">请选择区域</option>
                                        {% for area in areas %}
                                        <option value="{{ area.id }}">{{ area.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="plan_date">计划日期 <span class="text-danger">*</span></label>
                                    <input type="date" name="plan_date" id="plan_date" class="form-control" value="{{ plan_date }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="meal_type">餐次 <span class="text-danger">*</span></label>
                                    <select name="meal_type" id="meal_type" class="form-control" required>
                                        <option value="">请选择餐次</option>
                                        <option value="早餐" {% if meal_type == '早餐' %}selected{% endif %}>早餐</option>
                                        <option value="午餐" {% if meal_type == '午餐' %}selected{% endif %}>午餐</option>
                                        <option value="晚餐" {% if meal_type == '晚餐' %}selected{% endif %}>晚餐</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="expected_diners">预计用餐人数</label>
                                    <input type="number" name="expected_diners" id="expected_diners" class="form-control" min="1">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="notes">备注</label>
                            <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">选择食谱</h4>
                                <div class="card-tools">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-success btn-sm" id="addMultipleRecipesBtn" data-toggle="modal" data-target="#bulkAddModal">
                                            <i class="fas fa-list"></i> 批量添加
                                        </button>
                                        <button type="button" class="btn btn-primary btn-sm" id="addRecipeBtn">
                                            <i class="fas fa-plus"></i> 添加食谱
                                        </button>
                                        <button type="button" class="btn btn-info btn-sm" id="addCommonRecipesBtn" data-toggle="modal" data-target="#commonRecipesModal">
                                            <i class="fas fa-star"></i> 常用菜品
                                        </button>
                                        <button type="button" class="btn btn-warning btn-sm" id="addFromTemplateBtn" data-toggle="modal" data-target="#templateModal">
                                            <i class="fas fa-copy"></i> 从模板添加
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="fas fa-info-circle"></i> 已添加 <span id="recipeCount" class="badge badge-pill badge-primary">0</span> 个菜品
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-secondary" id="collapseAllBtn">
                                                <i class="fas fa-compress-alt"></i> 折叠全部
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" id="expandAllBtn">
                                                <i class="fas fa-expand-alt"></i> 展开全部
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div id="recipeContainer" class="recipe-list">
                                    <!-- 食谱项将在这里动态添加 -->
                                </div>
                                <div class="text-center mt-3" id="noRecipesMessage" style="display: none;">
                                    <p class="text-muted">暂无菜品，请点击上方按钮添加</p>
                                </div>
                            </div>
                        </div>

                        <div class="form-group text-center mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存
                            </button>
                            <a href="{{ url_for('menu_plan.index') }}" class="btn btn-default">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量添加食谱模态框 -->
<div class="modal fade" id="bulkAddModal" tabindex="-1" role="dialog" aria-labelledby="bulkAddModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkAddModalLabel">批量添加食谱</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>按餐次筛选</label>
                            <select id="filterMealType" class="form-control">
                                <option value="">全部</option>
                                <option value="早餐">早餐</option>
                                <option value="午餐">午餐</option>
                                <option value="晚餐">晚餐</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>食谱分类</label>
                            <div class="recipe-category-list" id="recipeCategoryList">
                                <div class="recipe-category-item active" data-category="">全部</div>
                                <div class="recipe-category-item" data-category="主食">主食</div>
                                <div class="recipe-category-item" data-category="荤菜">荤菜</div>
                                <div class="recipe-category-item" data-category="素菜">素菜</div>
                                <div class="recipe-category-item" data-category="汤品">汤品</div>
                                <div class="recipe-category-item" data-category="小吃">小吃</div>
                                <div class="recipe-category-item" data-category="水果">水果</div>
                                <div class="recipe-category-item" data-category="饮品">饮品</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="addMultipleCheckbox">
                                <label class="custom-control-label" for="addMultipleCheckbox">选择多个一次添加</label>
                            </div>
                        </div>
                        <div id="selectedRecipesContainer" style="display: none;">
                            <div class="form-group">
                                <label>已选择的食谱 (<span id="selectedRecipesCount">0</span>)</label>
                                <div class="list-group" id="selectedRecipesList" style="max-height: 150px; overflow-y: auto;">
                                    <!-- 已选择的食谱将在这里显示 -->
                                </div>
                            </div>
                            <div class="form-group">
                                <button type="button" class="btn btn-success btn-block" id="addSelectedRecipesBtn">
                                    <i class="fas fa-plus"></i> 添加所有选中菜品
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="form-group">
                            <label>搜索食谱</label>
                            <div class="input-group">
                                <input type="text" id="searchRecipe" class="form-control" placeholder="输入食谱名称搜索">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="d-flex justify-content-between align-items-center">
                                <label>可选食谱列表</label>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-secondary" id="sortByNameBtn">按名称</button>
                                    <button type="button" class="btn btn-outline-secondary" id="sortByCategoryBtn">按分类</button>
                                </div>
                            </div>
                            <div class="list-group" id="availableRecipesList" style="max-height: 300px; overflow-y: auto;">
                                {% for recipe in recipes %}
                                <div class="list-group-item list-group-item-action recipe-list-item"
                                     data-id="{{ recipe.id }}"
                                     data-name="{{ recipe.name }}"
                                     data-category="{{ recipe.category }}"
                                     data-meal-type="{{ recipe.meal_type }}">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">{{ recipe.name }}</h6>
                                        <small>{{ recipe.category }}</small>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <small class="text-muted">{{ recipe.meal_type or '不限' }}</small>
                                        <div class="input-group input-group-sm" style="width: 150px;">
                                            <input type="number" class="form-control bulk-quantity" min="1" step="0.1" value="1">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-success add-bulk-recipe" type="button">添加</button>
                                                <button class="btn btn-outline-primary select-recipe-btn" type="button" style="display: none;">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 常用菜品模态框 -->
<div class="modal fade" id="commonRecipesModal" tabindex="-1" role="dialog" aria-labelledby="commonRecipesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="commonRecipesModalLabel">常用菜品</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-tabs" id="commonRecipesTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="recent-tab" data-toggle="tab" href="#recent" role="tab">最近使用</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="frequent-tab" data-toggle="tab" href="#frequent" role="tab">使用频率最高</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="favorite-tab" data-toggle="tab" href="#favorite" role="tab">收藏菜品</a>
                    </li>
                </ul>
                <div class="tab-content mt-3" id="commonRecipesTabContent">
                    <div class="tab-pane fade show active" id="recent" role="tabpanel">
                        <div class="row">
                            <!-- 这里将通过AJAX加载最近使用的菜品 -->
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">红烧肉</h5>
                                        <p class="card-text text-muted">荤菜 | 午餐/晚餐</p>
                                        <div class="d-flex justify-content-between">
                                            <div class="input-group input-group-sm" style="width: 100px;">
                                                <input type="number" class="form-control" value="1" min="1" step="0.1">
                                            </div>
                                            <button class="btn btn-sm btn-primary add-common-recipe" data-id="1" data-name="红烧肉">
                                                <i class="fas fa-plus"></i> 添加
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">清炒白菜</h5>
                                        <p class="card-text text-muted">素菜 | 午餐/晚餐</p>
                                        <div class="d-flex justify-content-between">
                                            <div class="input-group input-group-sm" style="width: 100px;">
                                                <input type="number" class="form-control" value="1" min="1" step="0.1">
                                            </div>
                                            <button class="btn btn-sm btn-primary add-common-recipe" data-id="2" data-name="清炒白菜">
                                                <i class="fas fa-plus"></i> 添加
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="frequent" role="tabpanel">
                        <!-- 这里将通过AJAX加载使用频率最高的菜品 -->
                    </div>
                    <div class="tab-pane fade" id="favorite" role="tabpanel">
                        <!-- 这里将通过AJAX加载收藏的菜品 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 从模板添加模态框 -->
<div class="modal fade" id="templateModal" tabindex="-1" role="dialog" aria-labelledby="templateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="templateModalLabel">从模板添加</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>选择模板类型</label>
                            <select class="form-control" id="templateType">
                                <option value="system">系统模板</option>
                                <option value="personal">个人模板</option>
                                <option value="history">历史菜单</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>搜索模板</label>
                            <input type="text" class="form-control" id="searchTemplate" placeholder="输入模板名称搜索">
                        </div>
                    </div>
                </div>

                <div class="template-list">
                    <!-- 这里将通过AJAX加载模板列表 -->
                    <div class="card mb-3 template-item">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">标准午餐模板</h5>
                                <span class="badge badge-info">系统模板</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <p>包含4个菜品：2荤1素1汤</p>
                            <div class="template-recipes mb-3">
                                <span class="badge badge-light mr-1">红烧肉(1份)</span>
                                <span class="badge badge-light mr-1">清蒸鱼(1份)</span>
                                <span class="badge badge-light mr-1">炒青菜(1份)</span>
                                <span class="badge badge-light mr-1">紫菜汤(1份)</span>
                            </div>
                            <div class="text-right">
                                <button class="btn btn-sm btn-primary apply-template-btn" data-template-id="1">
                                    <i class="fas fa-check"></i> 应用此模板
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 食谱项模板 -->
<template id="recipeItemTemplate">
    <div class="recipe-item card mb-2">
        <div class="card-header py-2 recipe-header" data-toggle="collapse" data-target="#recipeCollapse_TEMP_ID" aria-expanded="true">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="fas fa-chevron-down mr-2 collapse-icon"></i>
                    <h5 class="recipe-title m-0">食谱项</h5>
                </div>
                <div class="recipe-actions">
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-secondary recipe-move-up-btn" title="上移">
                            <i class="fas fa-arrow-up"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary recipe-move-down-btn" title="下移">
                            <i class="fas fa-arrow-down"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger remove-recipe-btn" title="移除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="collapse show" id="recipeCollapse_TEMP_ID">
            <div class="card-body py-2">
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group mb-2">
                            <label>食谱 <span class="text-danger">*</span></label>
                            <select name="recipe_ids[]" class="form-control recipe-select" required>
                                <option value="">请选择食谱</option>
                                {% for recipe in recipes %}
                                <option value="{{ recipe.id }}" data-name="{{ recipe.name }}" data-category="{{ recipe.category }}">{{ recipe.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group mb-2">
                            <label>计划数量 <span class="text-danger">*</span></label>
                            <input type="number" name="quantities[]" class="form-control quantity-input" min="0.1" step="0.1" value="1" required>
                        </div>
                    </div>
                </div>
                <div class="recipe-notes mt-2" style="display: none;">
                    <div class="form-group mb-0">
                        <label>备注</label>
                        <textarea name="recipe_notes[]" class="form-control form-control-sm recipe-note" rows="1" placeholder="可选：添加关于此菜品的备注"></textarea>
                    </div>
                </div>
                <div class="text-right">
                    <button type="button" class="btn btn-link btn-sm toggle-notes-btn">
                        <i class="fas fa-sticky-note"></i> 添加备注
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<!-- 已选择食谱项模板 -->
<template id="selectedRecipeItemTemplate">
    <div class="list-group-item d-flex justify-content-between align-items-center selected-recipe-item" data-id="">
        <div>
            <span class="recipe-name"></span>
            <small class="text-muted ml-2">(<span class="recipe-quantity">1</span>)</small>
        </div>
        <button type="button" class="btn btn-sm btn-outline-danger remove-selected-btn">
            <i class="fas fa-times"></i>
        </button>
    </div>
</template>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/select2/select2.min.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 初始化Select2
        $('.select2').select2({
            theme: 'bootstrap4'
        });

        // 生成唯一ID
        function generateUniqueId() {
            return 'id_' + Math.random().toString(36).substr(2, 9);
        }

        // 更新食谱计数
        function updateRecipeCount() {
            var count = $('.recipe-item').length;
            $('#recipeCount').text(count);

            // 显示或隐藏"暂无菜品"提示
            if (count > 0) {
                $('#noRecipesMessage').hide();
            } else {
                $('#noRecipesMessage').show();
            }
        }

        // 添加食谱按钮点击事件
        $('#addRecipeBtn').click(function() {
            addRecipeItem();
            updateRecipeCount();
        });

        // 页面加载时添加一个空的食谱项
        addRecipeItem();
        updateRecipeCount();

        // 动态绑定移除食谱按钮点击事件
        $(document).on('click', '.remove-recipe-btn', function() {
            $(this).closest('.recipe-item').remove();
            updateRecipeCount();
        });

        // 动态绑定食谱选择变化事件
        $(document).on('change', '.recipe-select', function() {
            var selectedOption = $(this).find('option:selected');
            var recipeName = selectedOption.data('name');
            var category = selectedOption.data('category');

            var recipeItem = $(this).closest('.recipe-item');
            recipeItem.find('.recipe-title').text(recipeName || '食谱项');

            // 添加分类标签
            if (category) {
                if (recipeItem.find('.recipe-category-badge').length === 0) {
                    recipeItem.find('.recipe-title').after('<span class="badge badge-secondary ml-2 recipe-category-badge">' + category + '</span>');
                } else {
                    recipeItem.find('.recipe-category-badge').text(category);
                }
            }
        });

        // 动态绑定折叠/展开事件
        $(document).on('click', '.recipe-header', function() {
            var collapseIcon = $(this).find('.collapse-icon');
            var isExpanded = $(this).attr('aria-expanded') === 'true';

            if (isExpanded) {
                collapseIcon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
                $(this).attr('aria-expanded', 'false');
            } else {
                collapseIcon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
                $(this).attr('aria-expanded', 'true');
            }
        });

        // 折叠全部按钮点击事件
        $('#collapseAllBtn').click(function() {
            $('.recipe-item .collapse').collapse('hide');
            $('.collapse-icon').removeClass('fa-chevron-down').addClass('fa-chevron-right');
            $('.recipe-header').attr('aria-expanded', 'false');
        });

        // 展开全部按钮点击事件
        $('#expandAllBtn').click(function() {
            $('.recipe-item .collapse').collapse('show');
            $('.collapse-icon').removeClass('fa-chevron-right').addClass('fa-chevron-down');
            $('.recipe-header').attr('aria-expanded', 'true');
        });

        // 动态绑定上移按钮点击事件
        $(document).on('click', '.recipe-move-up-btn', function(e) {
            e.stopPropagation();
            var currentItem = $(this).closest('.recipe-item');
            var prevItem = currentItem.prev('.recipe-item');

            if (prevItem.length) {
                currentItem.insertBefore(prevItem);
            }
        });

        // 动态绑定下移按钮点击事件
        $(document).on('click', '.recipe-move-down-btn', function(e) {
            e.stopPropagation();
            var currentItem = $(this).closest('.recipe-item');
            var nextItem = currentItem.next('.recipe-item');

            if (nextItem.length) {
                currentItem.insertAfter(nextItem);
            }
        });

        // 动态绑定备注切换按钮点击事件
        $(document).on('click', '.toggle-notes-btn', function() {
            var notesSection = $(this).closest('.card-body').find('.recipe-notes');
            var button = $(this);

            if (notesSection.is(':visible')) {
                notesSection.slideUp();
                button.html('<i class="fas fa-sticky-note"></i> 添加备注');
            } else {
                notesSection.slideDown();
                button.html('<i class="fas fa-times"></i> 隐藏备注');
            }
        });

        // 添加食谱项函数
        function addRecipeItem() {
            var template = document.getElementById('recipeItemTemplate');
            var clone = document.importNode(template.content, true);

            // 生成唯一ID
            var uniqueId = generateUniqueId();
            $(clone).find('[data-target="#recipeCollapse_TEMP_ID"]').attr('data-target', '#recipeCollapse_' + uniqueId);
            $(clone).find('#recipeCollapse_TEMP_ID').attr('id', 'recipeCollapse_' + uniqueId);

            $('#recipeContainer').append(clone);

            // 初始化新添加的Select2
            $('.recipe-select').last().select2({
                theme: 'bootstrap4'
            });
        }

        // 添加特定食谱函数
        function addSpecificRecipe(recipeId, recipeName, quantity, category) {
            var template = document.getElementById('recipeItemTemplate');
            var clone = document.importNode(template.content, true);

            // 生成唯一ID
            var uniqueId = generateUniqueId();
            $(clone).find('[data-target="#recipeCollapse_TEMP_ID"]').attr('data-target', '#recipeCollapse_' + uniqueId);
            $(clone).find('#recipeCollapse_TEMP_ID').attr('id', 'recipeCollapse_' + uniqueId);

            // 设置食谱名称
            $(clone).find('.recipe-title').text(recipeName);

            // 添加分类标签
            if (category) {
                $(clone).find('.recipe-title').after('<span class="badge badge-secondary ml-2 recipe-category-badge">' + category + '</span>');
            }

            // 设置食谱ID
            var select = $(clone).find('.recipe-select');
            select.val(recipeId);

            // 设置数量
            $(clone).find('.quantity-input').val(quantity);

            // 添加到容器
            $('#recipeContainer').append(clone);

            // 初始化Select2并设置选中值
            var selectElement = $('.recipe-select').last();
            selectElement.select2({
                theme: 'bootstrap4'
            });
            selectElement.val(recipeId).trigger('change');

            // 更新计数
            updateRecipeCount();
        }

        // 批量添加食谱按钮点击事件
        $(document).on('click', '.add-bulk-recipe', function() {
            var listItem = $(this).closest('.recipe-list-item');
            var recipeId = listItem.data('id');
            var recipeName = listItem.data('name');
            var category = listItem.data('category');
            var quantity = listItem.find('.bulk-quantity').val();

            addSpecificRecipe(recipeId, recipeName, quantity, category);

            // 显示添加成功提示
            var button = $(this);
            var originalText = button.html();
            button.html('<i class="fas fa-check"></i> 已添加');
            button.removeClass('btn-outline-success').addClass('btn-success');

            setTimeout(function() {
                button.html(originalText);
                button.removeClass('btn-success').addClass('btn-outline-success');
            }, 1000);
        });

        // 多选模式切换
        $('#addMultipleCheckbox').change(function() {
            var isChecked = $(this).prop('checked');

            if (isChecked) {
                // 启用多选模式
                $('#selectedRecipesContainer').slideDown();
                $('.add-bulk-recipe').hide();
                $('.select-recipe-btn').show();
            } else {
                // 禁用多选模式
                $('#selectedRecipesContainer').slideUp();
                $('.add-bulk-recipe').show();
                $('.select-recipe-btn').hide();
            }
        });

        // 选择食谱按钮点击事件
        $(document).on('click', '.select-recipe-btn', function() {
            var listItem = $(this).closest('.recipe-list-item');
            var recipeId = listItem.data('id');
            var recipeName = listItem.data('name');
            var quantity = listItem.find('.bulk-quantity').val();

            // 检查是否已经选择了该食谱
            var existingItem = $('#selectedRecipesList').find('[data-id="' + recipeId + '"]');
            if (existingItem.length > 0) {
                // 更新数量
                existingItem.find('.recipe-quantity').text(quantity);
            } else {
                // 添加到已选择列表
                var template = document.getElementById('selectedRecipeItemTemplate');
                var clone = document.importNode(template.content, true);

                $(clone).find('.selected-recipe-item').attr('data-id', recipeId);
                $(clone).find('.recipe-name').text(recipeName);
                $(clone).find('.recipe-quantity').text(quantity);

                $('#selectedRecipesList').append(clone);
            }

            // 更新选择计数
            updateSelectedRecipesCount();

            // 显示选择成功提示
            var button = $(this);
            button.removeClass('btn-outline-primary').addClass('btn-primary');

            setTimeout(function() {
                button.removeClass('btn-primary').addClass('btn-outline-primary');
            }, 1000);
        });

        // 移除已选择食谱按钮点击事件
        $(document).on('click', '.remove-selected-btn', function() {
            $(this).closest('.selected-recipe-item').remove();
            updateSelectedRecipesCount();
        });

        // 添加所有选中菜品按钮点击事件
        $('#addSelectedRecipesBtn').click(function() {
            $('#selectedRecipesList .selected-recipe-item').each(function() {
                var recipeId = $(this).data('id');
                var recipeName = $(this).find('.recipe-name').text();
                var quantity = $(this).find('.recipe-quantity').text();

                // 获取分类信息
                var category = '';
                $('.recipe-list-item').each(function() {
                    if ($(this).data('id') == recipeId) {
                        category = $(this).data('category');
                        return false;
                    }
                });

                addSpecificRecipe(recipeId, recipeName, quantity, category);
            });

            // 清空已选择列表
            $('#selectedRecipesList').empty();
            updateSelectedRecipesCount();

            // 关闭模态框
            $('#bulkAddModal').modal('hide');
        });

        // 更新已选择食谱计数
        function updateSelectedRecipesCount() {
            var count = $('#selectedRecipesList .selected-recipe-item').length;
            $('#selectedRecipesCount').text(count);
        }

        // 清除搜索按钮点击事件
        $('#clearSearchBtn').click(function() {
            $('#searchRecipe').val('').trigger('input');
        });

        // 按名称排序按钮点击事件
        $('#sortByNameBtn').click(function() {
            var items = $('#availableRecipesList .recipe-list-item').get();
            items.sort(function(a, b) {
                var nameA = $(a).data('name').toUpperCase();
                var nameB = $(b).data('name').toUpperCase();
                return (nameA < nameB) ? -1 : (nameA > nameB) ? 1 : 0;
            });
            $.each(items, function(i, item) {
                $('#availableRecipesList').append(item);
            });
        });

        // 按分类排序按钮点击事件
        $('#sortByCategoryBtn').click(function() {
            var items = $('#availableRecipesList .recipe-list-item').get();
            items.sort(function(a, b) {
                var catA = $(a).data('category').toUpperCase();
                var catB = $(b).data('category').toUpperCase();
                if (catA === catB) {
                    var nameA = $(a).data('name').toUpperCase();
                    var nameB = $(b).data('name').toUpperCase();
                    return (nameA < nameB) ? -1 : (nameA > nameB) ? 1 : 0;
                }
                return (catA < catB) ? -1 : 1;
            });
            $.each(items, function(i, item) {
                $('#availableRecipesList').append(item);
            });
        });

        // 常用菜品添加按钮点击事件
        $(document).on('click', '.add-common-recipe', function() {
            var recipeId = $(this).data('id');
            var recipeName = $(this).data('name');
            var quantity = $(this).closest('.card-body').find('input').val();

            addSpecificRecipe(recipeId, recipeName, quantity);

            // 显示添加成功提示
            var button = $(this);
            var originalText = button.html();
            button.html('<i class="fas fa-check"></i> 已添加');
            button.removeClass('btn-primary').addClass('btn-success');

            setTimeout(function() {
                button.html(originalText);
                button.removeClass('btn-success').addClass('btn-primary');
            }, 1000);
        });

        // 应用模板按钮点击事件
        $(document).on('click', '.apply-template-btn', function() {
            var templateId = $(this).data('template-id');

            // 这里应该通过AJAX获取模板详情，这里简化处理
            var templateRecipes = [
                {id: 1, name: '红烧肉', quantity: 1, category: '荤菜'},
                {id: 2, name: '清蒸鱼', quantity: 1, category: '荤菜'},
                {id: 3, name: '炒青菜', quantity: 1, category: '素菜'},
                {id: 4, name: '紫菜汤', quantity: 1, category: '汤品'}
            ];

            // 添加模板中的所有菜品
            templateRecipes.forEach(function(recipe) {
                addSpecificRecipe(recipe.id, recipe.name, recipe.quantity, recipe.category);
            });

            // 关闭模态框
            $('#templateModal').modal('hide');
        });

        // 食谱分类筛选
        $(document).on('click', '.recipe-category-item', function() {
            // 更新活动状态
            $('.recipe-category-item').removeClass('active');
            $(this).addClass('active');

            // 获取筛选条件
            var category = $(this).data('category');
            var mealType = $('#filterMealType').val();
            var searchText = $('#searchRecipe').val().toLowerCase();

            // 筛选食谱列表
            filterRecipes(category, mealType, searchText);
        });

        // 餐次筛选变化事件
        $('#filterMealType').change(function() {
            var mealType = $(this).val();
            var category = $('.recipe-category-item.active').data('category');
            var searchText = $('#searchRecipe').val().toLowerCase();

            // 筛选食谱列表
            filterRecipes(category, mealType, searchText);
        });

        // 搜索食谱
        $('#searchRecipe').on('input', function() {
            var searchText = $(this).val().toLowerCase();
            var category = $('.recipe-category-item.active').data('category');
            var mealType = $('#filterMealType').val();

            // 筛选食谱列表
            filterRecipes(category, mealType, searchText);
        });

        // 筛选食谱列表函数
        function filterRecipes(category, mealType, searchText) {
            $('.recipe-list-item').each(function() {
                var item = $(this);
                var itemCategory = item.data('category');
                var itemMealType = item.data('meal-type');
                var itemName = item.data('name').toLowerCase();

                var categoryMatch = !category || itemCategory === category;
                var mealTypeMatch = !mealType || itemMealType === mealType || !itemMealType;
                var searchMatch = !searchText || itemName.includes(searchText);

                if (categoryMatch && mealTypeMatch && searchMatch) {
                    item.show();
                } else {
                    item.hide();
                }
            });
        }

        // 表单提交前验证
        $('form').submit(function(e) {
            // 检查是否至少有一个食谱项
            if ($('.recipe-item').length === 0) {
                e.preventDefault();
                alert('请至少添加一个食谱');
                return false;
            }

            // 检查所有食谱项是否都已选择食谱和数量
            var valid = true;
            $('.recipe-select').each(function() {
                if (!$(this).val()) {
                    valid = false;
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            $('.quantity-input').each(function() {
                if (!$(this).val() || $(this).val() <= 0) {
                    valid = false;
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            if (!valid) {
                e.preventDefault();
                alert('请确保所有食谱项都已正确填写');
                return false;
            }

            return true;
        });
    });
</script>
{% endblock %}
