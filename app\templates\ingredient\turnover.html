{% extends "base.html" %}

{% block title %}{{ ingredient.name }} - 周转情况{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
/* 食材周转页面样式 */
.compact-toolbar {
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.table-compact {
    font-size: 15px;
}

.table-compact th {
    padding: 10px 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-weight: 600;
    font-size: 14px;
    color: white;
    border: none;
}

.table-compact td {
    padding: 10px 8px;
    vertical-align: middle;
}

.turnover-summary {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 25px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-card {
    text-align: center;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.summary-number {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.summary-label {
    font-size: 13px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ingredient-info {
    background: #e3f2fd;
    color: #1976d2;
    padding: 10px 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.ingredient-name {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.ingredient-category {
    color: #dc3545;
    font-size: 14px;
    font-weight: 500;
}

.batch-number {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 13px;
    color: #495057;
    border: 1px solid #e9ecef;
}

.quantity-display {
    font-weight: 600;
    font-size: 16px;
    color: #2980b9;
}

.date-display {
    font-size: 13px;
    color: #495057;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题工具栏 -->
    <div class="compact-toolbar d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            <h5 class="mb-0 mr-3"><i class="fas fa-chart-line mr-2"></i>食材周转情况</h5>
            <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> 返回库存
            </a>
        </div>
    </div>

    <!-- 食材基本信息 -->
    <div class="ingredient-info">
        <div class="ingredient-name">{{ ingredient.name }}</div>
        {% if ingredient.category %}
        <div class="ingredient-category">{{ ingredient.category.name }}</div>
        {% endif %}
    </div>

    <!-- 周转汇总信息 -->
    <div class="turnover-summary">
        <h6 class="mb-3">周转汇总</h6>
        <div class="summary-grid">
            <div class="summary-card">
                <div class="summary-number text-info">{{ turnover_info.total_stock_in }}{{ ingredient.unit }}</div>
                <div class="summary-label">总入库</div>
            </div>
            <div class="summary-card">
                <div class="summary-number text-warning">{{ turnover_info.total_stock_out }}{{ ingredient.unit }}</div>
                <div class="summary-label">总出库</div>
            </div>
            <div class="summary-card">
                <div class="summary-number text-success">{{ turnover_info.current_stock }}{{ ingredient.unit }}</div>
                <div class="summary-label">当前库存</div>
            </div>
            <div class="summary-card">
                <div class="summary-number text-primary">{{ turnover_info.turnover_rate }}%</div>
                <div class="summary-label">周转率</div>
            </div>
        </div>
    </div>

    <!-- 当前库存明细 -->
    <div class="mb-4">
        <h6 class="mb-3">当前库存明细</h6>
        <div class="table-responsive">
            <table class="table table-compact table-hover table-bordered">
                <thead>
                    <tr>
                        <th style="width: 20%;">存储位置</th>
                        <th style="width: 20%;">批次号</th>
                        <th style="width: 15%;">数量</th>
                        <th style="width: 15%;">生产日期</th>
                        <th style="width: 15%;">过期日期</th>
                        <th style="width: 15%;">状态</th>
                    </tr>
                </thead>
                <tbody>
                    {% for inventory in inventories %}
                    <tr>
                        <td>{{ inventory.storage_location.name }}</td>
                        <td><span class="batch-number">{{ inventory.batch_number }}</span></td>
                        <td class="text-right">
                            <div class="quantity-display">{{ inventory.quantity }}{{ inventory.unit }}</div>
                        </td>
                        <td><div class="date-display">{{ inventory.production_date|format_datetime('%m-%d') }}</div></td>
                        <td><div class="date-display">{{ inventory.expiry_date|format_datetime('%m-%d') }}</div></td>
                        <td>
                            {% if inventory.status == '正常' %}
                                <span class="badge badge-success badge-sm">正常</span>
                            {% elif inventory.status == '待检' %}
                                <span class="badge badge-warning badge-sm">待检</span>
                            {% elif inventory.status == '冻结' %}
                                <span class="badge badge-info badge-sm">冻结</span>
                            {% elif inventory.status == '已过期' %}
                                <span class="badge badge-danger badge-sm">已过期</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <i class="fas fa-box-open text-muted"></i>
                            <br><small class="text-muted">暂无库存记录</small>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- 最近入库记录 -->
    <div class="mb-4">
        <h6 class="mb-3">最近入库记录 (最近20条)</h6>
        <div class="table-responsive">
            <table class="table table-compact table-hover table-bordered">
                <thead>
                    <tr>
                        <th style="width: 15%;">入库日期</th>
                        <th style="width: 20%;">批次号</th>
                        <th style="width: 15%;">数量</th>
                        <th style="width: 15%;">单价</th>
                        <th style="width: 20%;">供应商</th>
                        <th style="width: 15%;">状态</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in stock_in_items %}
                    <tr>
                        <td><div class="date-display">{{ item.stock_in.stock_in_date|format_datetime('%m-%d') }}</div></td>
                        <td><span class="batch-number">{{ item.batch_number }}</span></td>
                        <td class="text-right">
                            <div class="quantity-display">{{ item.quantity }}{{ item.unit }}</div>
                        </td>
                        <td class="text-right">¥{{ "%.2f"|format(item.unit_price) }}</td>
                        <td>{{ item.stock_in.supplier.name if item.stock_in.supplier else '-' }}</td>
                        <td>
                            {% if item.stock_in.status == '已入库' %}
                                <span class="badge badge-success badge-sm">已入库</span>
                            {% elif item.stock_in.status == '已审核' %}
                                <span class="badge badge-info badge-sm">已审核</span>
                            {% elif item.stock_in.status == '待审核' %}
                                <span class="badge badge-warning badge-sm">待审核</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <i class="fas fa-inbox text-muted"></i>
                            <br><small class="text-muted">暂无入库记录</small>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- 最近出库记录 -->
    <div class="mb-4">
        <h6 class="mb-3">最近出库记录 (最近20条)</h6>
        <div class="table-responsive">
            <table class="table table-compact table-hover table-bordered">
                <thead>
                    <tr>
                        <th style="width: 15%;">出库日期</th>
                        <th style="width: 20%;">批次号</th>
                        <th style="width: 15%;">数量</th>
                        <th style="width: 25%;">出库用途</th>
                        <th style="width: 15%;">操作人</th>
                        <th style="width: 10%;">状态</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in stock_out_items %}
                    <tr>
                        <td><div class="date-display">{{ item.stock_out.stock_out_date|format_datetime('%m-%d') }}</div></td>
                        <td><span class="batch-number">{{ item.batch_number }}</span></td>
                        <td class="text-right">
                            <div class="quantity-display">{{ item.quantity }}{{ item.unit }}</div>
                        </td>
                        <td>{{ item.stock_out.purpose or '消耗计划' }}</td>
                        <td>{{ item.stock_out.operator.real_name or item.stock_out.operator.username if item.stock_out.operator else '-' }}</td>
                        <td>
                            {% if item.stock_out.status == '已出库' %}
                                <span class="badge badge-success badge-sm">已出库</span>
                            {% elif item.stock_out.status == '已审核' %}
                                <span class="badge badge-info badge-sm">已审核</span>
                            {% elif item.stock_out.status == '待审核' %}
                                <span class="badge badge-warning badge-sm">待审核</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <i class="fas fa-sign-out-alt text-muted"></i>
                            <br><small class="text-muted">暂无出库记录</small>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- 食材溯源信息 -->
    <div class="mb-4">
        <h6 class="mb-3"><i class="fas fa-search-location mr-2"></i>食材溯源信息 - 用在哪一天哪一餐哪个食谱</h6>
        <div class="table-responsive">
            <table class="table table-compact table-hover table-bordered">
                <thead>
                    <tr>
                        <th style="width: 10%;">消耗日期</th>
                        <th style="width: 8%;">餐次</th>
                        <th style="width: 18%;">食谱名称</th>
                        <th style="width: 10%;">食谱分类</th>
                        <th style="width: 12%;">批次号</th>
                        <th style="width: 10%;">消耗数量</th>
                        <th style="width: 15%;">供应商</th>
                        <th style="width: 8%;">用餐人数</th>
                        <th style="width: 9%;">学校</th>
                    </tr>
                </thead>
                <tbody>
                    {% for trace in traceability_info %}
                    <tr>
                        <td>
                            <div class="date-display">
                                {% if trace.consumption_date %}
                                    {{ trace.consumption_date.strftime('%m-%d') }}
                                {% else %}
                                    -
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if trace.meal_type %}
                                {% if trace.meal_type == '早餐' %}
                                    <span class="badge badge-info badge-sm">早餐</span>
                                {% elif trace.meal_type == '午餐' %}
                                    <span class="badge badge-success badge-sm">午餐</span>
                                {% elif trace.meal_type == '晚餐' %}
                                    <span class="badge badge-warning badge-sm">晚餐</span>
                                {% else %}
                                    <span class="badge badge-secondary badge-sm">{{ trace.meal_type }}</span>
                                {% endif %}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if trace.recipe_name %}
                                <div class="ingredient-name" style="font-size: 15px;">{{ trace.recipe_name }}</div>
                            {% else %}
                                <span class="text-muted">未关联食谱</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if trace.recipe_category %}
                                <span class="ingredient-category" style="font-size: 12px;">{{ trace.recipe_category }}</span>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td><span class="batch-number">{{ trace.batch_number }}</span></td>
                        <td class="text-right">
                            <div class="quantity-display">{{ trace.consumed_quantity }}{{ trace.unit }}</div>
                        </td>
                        <td>
                            {% if trace.supplier_name %}
                                <div style="font-size: 13px; font-weight: 500; color: #2c3e50;">{{ trace.supplier_name }}</div>
                                {% if trace.supplier_contact %}
                                <small class="text-muted">{{ trace.supplier_contact }}</small>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">未知供应商</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if trace.diners_count %}
                                {{ trace.diners_count }}人
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ trace.area_name or '-' }}</small>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="9" class="text-center py-4">
                            <i class="fas fa-search text-muted"></i>
                            <br><small class="text-muted">暂无溯源信息</small>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% if traceability_info %}
        <div class="mt-3">
            <small class="text-info">
                <i class="fas fa-info-circle mr-1"></i>
                溯源原理：通过食材批次号 → 出库记录 → 消耗计划 → 菜单计划 → 食谱配方，实现完整的食品安全追溯链条
            </small>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
