<!DOCTYPE html>
<html>
<head>
    <title>入库单打印</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* 打印样式 */
        @page {
            size: 210mm 297mm; /* A4纸张精确尺寸 */
            margin: 1.5cm;
        }
        /* 确保打印时背景色显示 */
        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
        }
        html, body {
            width: 210mm; /* A4宽度 */
            height: 297mm; /* A4高度 */
            margin: 0 auto;
            padding: 0;
            font-family: "Microsoft YaHei", "微软雅黑", SimSun, "宋体", sans-serif;
            font-size: 11pt;
            line-height: 1.6;
            background-color: white;
            color: #333;
        }
        .container {
            width: 180mm; /* A4宽度减去页边距 */
            margin: 0 auto;
            padding: 15px 0;
            box-sizing: border-box;
        }
        .header {
            text-align: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #2c3e50;
        }
        .header h2 {
            margin: 0 0 5px 0;
            font-size: 22pt;
            font-weight: 600;
            color: #2c3e50;
        }
        .header p {
            margin: 5px 0;
            font-size: 12pt;
            color: #555;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .info-table th, .info-table td {
            border: 1px solid #ddd;
            padding: 8px 12px;
        }
        .info-table th {
            width: 22%;
            text-align: right;
            background-color: #f5f7fa;
            color: #2c3e50;
            font-weight: 600;
        }
        .info-table td {
            background-color: #fff;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .items-table th, .items-table td {
            border: 1px solid #ddd;
            padding: 10px 8px;
            text-align: center;
            vertical-align: middle;
        }
        .items-table th {
            background-color: #2c3e50;
            color: white;
            font-weight: 600;
            font-size: 11pt;
        }
        .items-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .items-table tr:hover {
            background-color: #f5f5f5;
        }
        .footer {
            margin-top: 25px;
            display: flex;
            justify-content: space-between;
        }
        .signature {
            width: 45%;
            padding: 10px;
            border-top: 1px solid #ddd;
        }
        .signature p {
            margin: 5px 0;
        }
        .total-row {
            font-weight: bold;
            background-color: #f5f7fa !important;
            color: #2c3e50;
        }
        .total-row td {
            border-top: 2px solid #ddd !important;
        }
        .price {
            color: #e74c3c;
            font-weight: 600;
        }
        .no-print {
            text-align: center;
            margin-top: 30px;
        }
        .no-print button {
            padding: 8px 20px;
            margin: 0 10px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .no-print button:hover {
            background-color: #2980b9;
        }
        @media screen {
            body {
                background-color: #f0f0f0;
                padding: 20px 0;
            }
            .container {
                background-color: white;
                box-shadow: 0 0 15px rgba(0,0,0,0.15);
                min-height: 297mm;
                padding: 20px;
                border-radius: 5px;
            }
        }
        @media print {
            html, body {
                width: 210mm;
                height: 297mm;
            }
            .no-print {
                display: none !important;
            }
            .container {
                width: 100%;
                padding: 0;
                box-shadow: none;
            }
            /* 允许表格跨页 */
            table { page-break-inside: auto; }
            tr { page-break-inside: avoid; }
            /* 避免页面元素被分割 */
            .header, .footer { page-break-inside: avoid; }

            /* 表头在每页重复显示 */
            thead { display: table-header-group; }
            tfoot { display: table-footer-group; }

            /* 分页后保持表头样式 */
            .items-table thead tr th {
                background-color: #2c3e50 !important;
                color: white !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>入库单</h2>
            <p>单号：{{ stock_in.stock_in_number }}</p>
        </div>

        <table class="info-table">
            <tr>
                <th>仓库</th>
                <td>{{ stock_in.warehouse.name }}</td>
                <th>入库日期</th>
                <td>{{ stock_in.stock_in_date|format_datetime('%Y-%m-%d') }}</td>
            </tr>
            <tr>
                <th>入库类型</th>
                <td>{{ stock_in.stock_in_type }}</td>
                <th>供应商</th>
                <td>{{ stock_in.supplier.name if stock_in.supplier else '-' }}</td>
            </tr>
            <tr>
                <th>操作人</th>
                <td>{{ stock_in.operator.real_name or stock_in.operator.username }}</td>
                <th>状态</th>
                <td>{{ stock_in.status }}</td>
            </tr>
            <tr>
                <th>备注</th>
                <td colspan="3">{{ stock_in.notes or '-' }}</td>
            </tr>
        </table>

        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%">序号</th>
                    <th style="width: 15%">食材名称</th>
                    <th style="width: 15%">存储位置</th>
                    <th style="width: 10%">批次号/供货商</th>
                    <th style="width: 8%">数量</th>
                    <th style="width: 7%">单位</th>
                    <th style="width: 10%">单价(元)</th>
                    <th style="width: 10%">总价(元)</th>
                    <th style="width: 10%">生产日期</th>
                    <th style="width: 10%">过期日期</th>
                </tr>
            </thead>
            <tbody>
                {% set ns = namespace(total_amount=0) %}
                {% for item in stock_in_items %}
                {% set item_total = (item.quantity|float * (item.unit_price|float if item.unit_price else 0))|round(2) %}
                {% set ns.total_amount = ns.total_amount + item_total %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ item.ingredient.name }}</td>
                    <td>{{ item.storage_location.name }} ({{ item.storage_location.location_code }})</td>
                    <td>
                        {{ item.batch_number }}
                        {% if item.supplier %}
                        <br>
                        <small style="color: #666; font-size: 9pt;">{{ item.supplier.name }}</small>
                        {% endif %}
                    </td>
                    <td>{{ item.quantity|float|round(2) }}</td>
                    <td>{{ item.unit }}</td>
                    <td>{{ item.unit_price|float|round(2) if item.unit_price else '-' }}</td>
                    <td class="price">{{ item_total }}</td>
                    <td>{{ item.production_date|format_datetime('%Y-%m-%d') }}</td>
                    <td>{{ item.expiry_date|format_datetime('%Y-%m-%d') }}</td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr class="total-row">
                    <td colspan="7" style="text-align: right;">总计金额:</td>
                    <td colspan="3" class="price">{{ ns.total_amount|round(2) }} 元</td>
                </tr>
                <!-- 空的表脚行，用于分页时保持表格结构 -->
                <tr style="visibility: hidden; height: 0;">
                    <td></td><td></td><td></td><td></td><td></td>
                    <td></td><td></td><td></td><td></td><td></td>
                </tr>
            </tfoot>
        </table>

        <div class="footer">
            <div class="signature">
                <p>制单人：{{ stock_in.operator.real_name or stock_in.operator.username }}</p>
                <p>签名：________________</p>
            </div>
            <div class="signature">
                <p>审核人：{{ stock_in.approver.real_name or stock_in.approver.username if stock_in.approver else '________________' }}</p>
                <p>签名：________________</p>
            </div>
        </div>

        <div class="footer">
            <div class="signature">
                <p>仓库管理员：________________</p>
                <p>签名：________________</p>
            </div>
            <div class="signature">
                <p>打印时间：{{ current_time|format_datetime('%Y-%m-%d %H:%M:%S') }}</p>
            </div>
        </div>

        <div class="no-print" style="text-align: center; margin-top: 20px;">
            <button onclick="window.print()">打印</button>
            <button onclick="window.close()">关闭</button>
            {% if stock_in.status not in ['已入库', '已取消'] %}
            <a href="{{ url_for('stock_in.execute', id=stock_in.id) }}" class="btn btn-success" id="executeBtn" style="display: inline-block; padding: 8px 20px; margin: 0 10px; background-color: #2ecc71; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; text-decoration: none;" onclick="return confirm('确定要执行入库操作吗？这将更新库存数量。')">
                入库
            </a>
            {% else %}
            <button disabled style="display: inline-block; padding: 8px 20px; margin: 0 10px; background-color: #95a5a6; color: white; border: none; border-radius: 4px; cursor: not-allowed; font-size: 14px;">
                {{ '已入库' if stock_in.status == '已入库' else '已取消' }}
            </button>
            {% endif %}
        </div>

        <script nonce="{{ csp_nonce }}">
            // 页面加载完成后自动聚焦
            window.onload = function() {
                window.focus();

                // 检测打印媒体查询支持
                if (window.matchMedia) {
                    var mediaQueryList = window.matchMedia('print');
                    mediaQueryList.addListener(function(mql) {
                        if (mql.matches) {
                            console.log('打印前准备');
                            document.body.classList.add('printing');

                            // 确保表头在每页都显示
                            var tableHeaders = document.querySelectorAll('thead');
                            tableHeaders.forEach(function(thead) {
                                thead.style.display = 'table-header-group';
                            });

                            // 确保表头样式正确
                            var headerCells = document.querySelectorAll('.items-table thead th');
                            headerCells.forEach(function(th) {
                                th.style.backgroundColor = '#2c3e50';
                                th.style.color = 'white';
                            });
                        } else {
                            console.log('打印后恢复');
                            document.body.classList.remove('printing');
                        }
                    });
                }

                // 自动调整打印设置
                document.querySelector('.no-print button').addEventListener('click', function() {
                    var style = document.createElement('style');
                    style.innerHTML = '@page { size: A4; margin: 2cm; }';
                    document.head.appendChild(style);

                    // 添加分页支持的样式
                    var pagingStyle = document.createElement('style');
                    pagingStyle.innerHTML = `
                        @media print {
                            thead { display: table-header-group !important; }
                            tfoot { display: table-footer-group !important; }
                            .items-table thead tr th {
                                background-color: #2c3e50 !important;
                                color: white !important;
                                -webkit-print-color-adjust: exact !important;
                                color-adjust: exact !important;
                            }
                        }
                    `;
                    document.head.appendChild(pagingStyle);

                    setTimeout(function() {
                        window.print();
                    }, 100);
                });
            };
        </script>
    </div>
</body>
</html>
