{% extends 'base.html' %}

{% block title %}编辑消耗计划{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">编辑消耗计划</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('consumption_plan.view', id=consumption_plan.id) }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回详情
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('consumption_plan.edit', id=consumption_plan.id) }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="consumption_date">消耗日期</label>
                                    <input type="date" class="form-control" id="consumption_date" name="consumption_date" 
                                           value="{{ consumption_plan.consumption_date.strftime('%Y-%m-%d') if consumption_plan.consumption_date else (menu_plan.plan_date.strftime('%Y-%m-%d') if menu_plan else '') }}">
                                </div>
                                <div class="form-group">
                                    <label for="meal_type">餐次</label>
                                    <select class="form-control" id="meal_type" name="meal_type">
                                        <option value="早餐" {{ 'selected' if consumption_plan.meal_type == '早餐' or (not consumption_plan.meal_type and menu_plan and menu_plan.meal_type == '早餐') }}>早餐</option>
                                        <option value="午餐" {{ 'selected' if consumption_plan.meal_type == '午餐' or (not consumption_plan.meal_type and menu_plan and menu_plan.meal_type == '午餐') }}>午餐</option>
                                        <option value="晚餐" {{ 'selected' if consumption_plan.meal_type == '晚餐' or (not consumption_plan.meal_type and menu_plan and menu_plan.meal_type == '晚餐') }}>晚餐</option>
                                        <option value="加餐" {{ 'selected' if consumption_plan.meal_type == '加餐' or (not consumption_plan.meal_type and menu_plan and menu_plan.meal_type == '加餐') }}>加餐</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="diners_count">用餐人数</label>
                                    <input type="number" class="form-control" id="diners_count" name="diners_count" 
                                           value="{{ consumption_plan.diners_count or (menu_plan.expected_diners if menu_plan else '') }}">
                                </div>
                                <div class="form-group">
                                    <label for="notes">备注</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3">{{ consumption_plan.notes or '' }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 消耗明细列表 -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h4 class="card-title">消耗明细列表</h4>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>食材名称</th>
                                                <th>计划消耗量</th>
                                                <th>单位</th>
                                                <th>当前库存</th>
                                                <th>库存状态</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for detail in consumption_details %}
                                            <tr>
                                                <td>{{ detail.ingredient.name }}</td>
                                                <td>
                                                    <input type="number" class="form-control" name="quantity_{{ detail.id }}" 
                                                           value="{{ detail.planned_quantity }}" min="0" step="0.1">
                                                </td>
                                                <td>{{ detail.unit }}</td>
                                                <td id="inventory_{{ detail.id }}">
                                                    {% if inventory_status %}
                                                    {{ inventory_status[detail.id].total }}
                                                    {% else %}
                                                    -
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if inventory_status %}
                                                        {% if inventory_status[detail.id].sufficient %}
                                                        <span class="badge badge-success">库存充足</span>
                                                        {% else %}
                                                        <span class="badge badge-danger">库存不足</span>
                                                        {% endif %}
                                                    {% else %}
                                                    -
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% else %}
                                            <tr>
                                                <td colspan="5" class="text-center">暂无消耗明细</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="row mt-4">
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> 保存修改
                                </button>
                                <a href="{{ url_for('consumption_plan.view', id=consumption_plan.id) }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> 取消
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 数量变化时检查库存状态
        $('input[name^="quantity_"]').on('change', function() {
            let detailId = $(this).attr('name').split('_')[1];
            let quantity = parseFloat($(this).val());
            let inventory = parseFloat($('#inventory_' + detailId).text());
            
            if (quantity > inventory) {
                $(this).addClass('is-invalid');
                alert('警告：计划消耗量超过当前库存！');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
    });
</script>
{% endblock %}
