{% extends 'base.html' %}

{% block title %}入库向导（简化版）{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col">
            <h2>入库向导（简化版）</h2>
            <p class="text-muted">测试版本，用于排查问题</p>
        </div>
    </div>

    <!-- 学校信息显示 -->
    <div class="card mb-4 border-primary">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-school"></i> 当前学校：{{ user_area.name }}
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-1"><strong>默认仓库：</strong>{{ default_warehouse.name }}</p>
                    <p class="mb-1"><strong>仓库地址：</strong>{{ default_warehouse.location or '未设置' }}</p>
                </div>
                <div class="col-md-6">
                    <p class="mb-1"><strong>可用存储位置：</strong>{{ storage_locations|length }} 个</p>
                    <p class="mb-1"><strong>合作供应商：</strong>{{ suppliers|length }} 个</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 测试按钮 -->
    <div class="card">
        <div class="card-header">
            <h3>功能测试</h3>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <button type="button" class="btn btn-primary btn-lg btn-block" id="testApiBtn">
                        测试API连接
                    </button>
                </div>
                <div class="col-md-6">
                    <button type="button" class="btn btn-success btn-lg btn-block" id="showModalBtn">
                        显示模态框
                    </button>
                </div>
            </div>
            <div class="mt-3">
                <div id="testResult" class="alert" style="display: none;"></div>
            </div>
        </div>
    </div>

    <!-- 采购计划选择模态框 -->
    <div class="modal fade" id="purchaseOrderModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">选择采购计划</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>订单信息</th>
                                <th>创建时间</th>
                                <th>供应商</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="purchaseOrdersTable">
                            <tr>
                                <td colspan="5" class="text-center">
                                    点击"测试API连接"按钮加载数据
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    console.log('简化版入库向导页面加载完成');
    
    // 测试API按钮
    $('#testApiBtn').on('click', function() {
        console.log('点击测试API按钮');
        testApiConnection();
    });
    
    // 显示模态框按钮
    $('#showModalBtn').on('click', function() {
        console.log('点击显示模态框按钮');
        $('#purchaseOrderModal').modal('show');
    });
});

function testApiConnection() {
    console.log('开始测试API连接');
    
    showResult('正在测试API连接...', 'info');
    
    $.ajax({
        url: '/api/purchase-orders/available',
        type: 'GET',
        dataType: 'json',
        timeout: 10000,
        beforeSend: function() {
            console.log('发送API请求...');
            $('#testApiBtn').prop('disabled', true).text('测试中...');
        },
        success: function(response) {
            console.log('API响应成功:', response);
            $('#testApiBtn').prop('disabled', false).text('测试API连接');
            
            if (response && response.success) {
                showResult(`API连接成功！找到 ${response.count || 0} 个采购订单`, 'success');
                if (response.data && response.data.length > 0) {
                    renderPurchaseOrders(response.data);
                }
            } else {
                showResult('API连接成功，但没有返回数据', 'warning');
            }
        },
        error: function(xhr, status, error) {
            console.error('API请求失败:', {
                status: status,
                error: error,
                responseText: xhr.responseText
            });
            $('#testApiBtn').prop('disabled', false).text('测试API连接');
            
            if (status === 'timeout') {
                showResult('API请求超时', 'danger');
            } else {
                showResult(`API请求失败: ${status} - ${error}`, 'danger');
            }
        }
    });
}

function renderPurchaseOrders(orders) {
    console.log('渲染采购订单列表:', orders);
    
    let html = '';
    orders.forEach(order => {
        html += `
            <tr>
                <td>
                    <strong>${order.order_number}</strong><br>
                    <small class="text-muted">ID: ${order.id}</small>
                </td>
                <td>${order.created_at}</td>
                <td>${order.supplier_name || '自购'}</td>
                <td><span class="badge bg-success">${order.status}</span></td>
                <td>
                    <button class="btn btn-primary btn-sm" onclick="selectOrder(${order.id}, '${order.order_number}')">
                        选择
                    </button>
                </td>
            </tr>
        `;
    });
    
    $('#purchaseOrdersTable').html(html);
}

function selectOrder(orderId, orderNumber) {
    console.log('选择采购订单:', orderId, orderNumber);
    alert(`已选择采购订单: ${orderNumber} (ID: ${orderId})`);
    $('#purchaseOrderModal').modal('hide');
}

function showResult(message, type) {
    const alertClass = `alert-${type}`;
    $('#testResult')
        .removeClass('alert-info alert-success alert-warning alert-danger')
        .addClass(alertClass)
        .text(message)
        .show();
}
</script>
{% endblock %}
