#!/usr/bin/env python3
"""
内联样式批量修复脚本
将常见的内联样式转换为 CSS 类
"""

import os
import re
import glob
from collections import Counter

def fix_inline_styles():
    """批量修复内联样式"""
    print("🎨 批量修复内联样式...")
    print("=" * 50)
    
    # 常见内联样式的替换规则
    style_replacements = {
        # 显示/隐藏
        r'style\s*=\s*["\']display:\s*none["\']': 'class="d-none"',
        r'style\s*=\s*["\']display:\s*block["\']': 'class="d-block"',
        r'style\s*=\s*["\']display:\s*inline["\']': 'class="d-inline"',
        r'style\s*=\s*["\']display:\s*inline-block["\']': 'class="d-inline-block"',
        
        # 文本对齐
        r'style\s*=\s*["\']text-align:\s*center["\']': 'class="text-center"',
        r'style\s*=\s*["\']text-align:\s*left["\']': 'class="text-start"',
        r'style\s*=\s*["\']text-align:\s*right["\']': 'class="text-end"',
        
        # 字体样式
        r'style\s*=\s*["\']font-weight:\s*bold["\']': 'class="fw-bold"',
        r'style\s*=\s*["\']font-weight:\s*normal["\']': 'class="fw-normal"',
        
        # 颜色
        r'style\s*=\s*["\']color:\s*red["\']': 'class="text-danger"',
        r'style\s*=\s*["\']color:\s*green["\']': 'class="text-success"',
        r'style\s*=\s*["\']color:\s*blue["\']': 'class="text-primary"',
        r'style\s*=\s*["\']color:\s*gray["\']': 'class="text-muted"',
        r'style\s*=\s*["\']color:\s*grey["\']': 'class="text-muted"',
        
        # 边距 (简单的)
        r'style\s*=\s*["\']margin:\s*0["\']': 'class="m-0"',
        r'style\s*=\s*["\']margin-top:\s*10px["\']': 'class="mt-2"',
        r'style\s*=\s*["\']margin-top:\s*20px["\']': 'class="mt-4"',
        r'style\s*=\s*["\']margin-bottom:\s*10px["\']': 'class="mb-2"',
        r'style\s*=\s*["\']margin-bottom:\s*20px["\']': 'class="mb-4"',
        
        # 内边距
        r'style\s*=\s*["\']padding:\s*0["\']': 'class="p-0"',
        r'style\s*=\s*["\']padding:\s*10px["\']': 'class="p-2"',
        r'style\s*=\s*["\']padding:\s*20px["\']': 'class="p-4"',
        
        # 宽度 (常见的百分比)
        r'style\s*=\s*["\']width:\s*100%["\']': 'class="w-100"',
        r'style\s*=\s*["\']width:\s*50%["\']': 'class="w-50"',
        r'style\s*=\s*["\']width:\s*25%["\']': 'class="w-25"',
        r'style\s*=\s*["\']width:\s*75%["\']': 'class="w-75"',
        
        # 高度
        r'style\s*=\s*["\']height:\s*100%["\']': 'class="h-100"',
        
        # 边框
        r'style\s*=\s*["\']border:\s*none["\']': 'class="border-0"',
        r'style\s*=\s*["\']border:\s*1px\s+solid["\']': 'class="border"',
    }
    
    # 查找所有 HTML 文件
    html_files = glob.glob('app/templates/**/*.html', recursive=True)
    
    fixed_files = 0
    total_replacements = 0
    
    for file_path in html_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_replacements = 0
            
            # 应用所有替换规则
            for pattern, replacement in style_replacements.items():
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
                    file_replacements += len(matches)
            
            # 如果有修改，保存文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                fixed_files += 1
                total_replacements += file_replacements
                print(f"✅ {file_path}: 修复了 {file_replacements} 个内联样式")
                
        except Exception as e:
            print(f"❌ 处理 {file_path} 失败: {e}")
    
    print(f"\n📊 内联样式修复总结:")
    print(f"   修复文件数: {fixed_files}")
    print(f"   总替换数: {total_replacements}")
    
    return fixed_files, total_replacements

def create_custom_css_classes():
    """创建自定义 CSS 类来处理特殊的内联样式"""
    print("\n📝 创建自定义 CSS 类...")
    
    custom_css = '''/* 自定义 CSS 类 - 用于替换内联样式 */

/* 宽度类 */
.w-15 { width: 15% !important; }
.w-20 { width: 20% !important; }
.w-30 { width: 30% !important; }
.w-35 { width: 35% !important; }
.w-40 { width: 40% !important; }
.w-60 { width: 60% !important; }
.w-70 { width: 70% !important; }
.w-80 { width: 80% !important; }
.w-85 { width: 85% !important; }
.w-90 { width: 90% !important; }

/* 高度类 */
.h-auto { height: auto !important; }
.h-50px { height: 50px !important; }
.h-100px { height: 100px !important; }
.h-200px { height: 200px !important; }
.h-300px { height: 300px !important; }

/* 最大宽度 */
.max-w-200 { max-width: 200px !important; }
.max-w-300 { max-width: 300px !important; }
.max-w-400 { max-width: 400px !important; }
.max-w-500 { max-width: 500px !important; }

/* 最大高度 */
.max-h-200 { max-height: 200px !important; }
.max-h-300 { max-height: 300px !important; }
.max-h-400 { max-height: 400px !important; }

/* 溢出处理 */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-scroll { overflow: scroll !important; }

/* 位置 */
.pos-relative { position: relative !important; }
.pos-absolute { position: absolute !important; }
.pos-fixed { position: fixed !important; }

/* Z-index */
.z-1 { z-index: 1 !important; }
.z-2 { z-index: 2 !important; }
.z-3 { z-index: 3 !important; }
.z-999 { z-index: 999 !important; }
.z-1000 { z-index: 1000 !important; }

/* 光标 */
.cursor-pointer { cursor: pointer !important; }
.cursor-default { cursor: default !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

/* 用户选择 */
.user-select-none { user-select: none !important; }
.user-select-all { user-select: all !important; }

/* 空白处理 */
.white-space-nowrap { white-space: nowrap !important; }
.white-space-pre { white-space: pre !important; }
.white-space-pre-wrap { white-space: pre-wrap !important; }

/* 文本溢出 */
.text-ellipsis {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

/* 垂直对齐 */
.vertical-align-top { vertical-align: top !important; }
.vertical-align-middle { vertical-align: middle !important; }
.vertical-align-bottom { vertical-align: bottom !important; }

/* 打印相关 */
@media print {
    .print-hidden { display: none !important; }
    .print-block { display: block !important; }
    .print-break-before { page-break-before: always !important; }
    .print-break-after { page-break-after: always !important; }
}

/* 表格相关 */
.table-layout-fixed { table-layout: fixed !important; }
.table-layout-auto { table-layout: auto !important; }

/* 边框圆角 */
.rounded-sm { border-radius: 0.125rem !important; }
.rounded-lg { border-radius: 0.5rem !important; }
.rounded-xl { border-radius: 0.75rem !important; }

/* 阴影 */
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important; }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important; }

/* 透明度 */
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }'''
    
    # 保存自定义 CSS
    css_path = 'app/static/css/custom-csp-fixes.css'
    os.makedirs(os.path.dirname(css_path), exist_ok=True)
    
    with open(css_path, 'w', encoding='utf-8') as f:
        f.write(custom_css)
    
    print(f"✅ 自定义 CSS 类已保存到: {css_path}")

def fix_common_width_styles():
    """修复常见的宽度样式"""
    print("\n🔧 修复常见的宽度样式...")
    
    # 扩展的宽度替换规则
    width_replacements = {
        r'style\s*=\s*["\']width:\s*15%["\']': 'class="w-15"',
        r'style\s*=\s*["\']width:\s*15%;?\s*["\']': 'class="w-15"',
        r'style\s*=\s*["\']width:\s*20%["\']': 'class="w-20"',
        r'style\s*=\s*["\']width:\s*30%["\']': 'class="w-30"',
        r'style\s*=\s*["\']width:\s*28%["\']': 'class="w-30"',  # 近似
        r'style\s*=\s*["\']width:\s*35%["\']': 'class="w-35"',
        r'style\s*=\s*["\']width:\s*40%["\']': 'class="w-40"',
        r'style\s*=\s*["\']width:\s*60%["\']': 'class="w-60"',
        r'style\s*=\s*["\']width:\s*70%["\']': 'class="w-70"',
        r'style\s*=\s*["\']width:\s*80%["\']': 'class="w-80"',
        r'style\s*=\s*["\']width:\s*85%["\']': 'class="w-85"',
        r'style\s*=\s*["\']width:\s*90%["\']': 'class="w-90"',
    }
    
    html_files = glob.glob('app/templates/**/*.html', recursive=True)
    
    fixed_files = 0
    total_replacements = 0
    
    for file_path in html_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_replacements = 0
            
            # 应用宽度替换规则
            for pattern, replacement in width_replacements.items():
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
                    file_replacements += len(matches)
            
            # 如果有修改，保存文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                if file_replacements > 0:
                    fixed_files += 1
                    total_replacements += file_replacements
                    print(f"✅ {file_path}: 修复了 {file_replacements} 个宽度样式")
                
        except Exception as e:
            print(f"❌ 处理 {file_path} 失败: {e}")
    
    print(f"\n📊 宽度样式修复总结:")
    print(f"   修复文件数: {fixed_files}")
    print(f"   总替换数: {total_replacements}")
    
    return fixed_files, total_replacements

if __name__ == '__main__':
    print("🎨 内联样式批量修复工具")
    print("=" * 60)
    
    # 1. 创建自定义 CSS 类
    create_custom_css_classes()
    
    # 2. 修复常见的内联样式
    fixed1, total1 = fix_inline_styles()
    
    # 3. 修复常见的宽度样式
    fixed2, total2 = fix_common_width_styles()
    
    print("\n" + "=" * 60)
    print("🎉 内联样式修复完成！")
    print(f"✅ 总修复文件数: {fixed1 + fixed2}")
    print(f"✅ 总替换数: {total1 + total2}")
    print("\n📝 后续步骤:")
    print("1. 在基础模板中引入 custom-csp-fixes.css")
    print("2. 重启应用服务器")
    print("3. 测试页面样式是否正常")
    print("4. 手动处理剩余的复杂内联样式")
    print("\n💡 提示:")
    print("大部分常见的内联样式已经转换为 CSS 类")
    print("剩余的复杂样式可能需要保留或手动处理")
