/**
 * 主题切换系统
 * 支持动态切换主题颜色，保存用户偏好，实时预览效果
 */

class ThemeSwitcher {
    constructor() {
        this.currentTheme = 'primary';
        this.themes = {
            // 现代专业系列 - 与CSS主题定义完全匹配
            'primary': { name: '🌊 海洋蓝主题', color: '#001F3F', description: '专业、信任、稳定' },
            'secondary': { name: '🔘 现代灰主题', color: '#333333', description: '简约、专业、平衡' },
            'success': { name: '🌿 自然绿主题', color: '#4A7856', description: '健康、成长、和谐' },
            'warning': { name: '🔥 活力橙主题', color: '#FF6B35', description: '活力、创新、温暖' },
            'info': { name: '💜 优雅紫主题', color: '#B399D4', description: '创新、优雅、神秘' },
            'danger': { name: '❤️ 深邃红主题', color: '#C91F37', description: '力量、重要、警示' },
            'dark': { name: '🌙 深色主题', color: '#00F3FF', description: '现代、护眼、专业' },

            // 经典优雅系列 - 与CSS主题定义完全匹配
            'classic-neutral': { name: '🏛️ 经典中性风', color: '#8b4513', description: '米白色·深棕色·金色，温暖优雅' },
            'modern-neutral': { name: '🏢 现代中性风', color: '#4a5568', description: '灰白色·深灰色·香槟金，简洁高雅' },
            'vintage-elegant': { name: '🍷 复古典雅风', color: '#004225', description: '酒红色·墨绿色·金色，浓郁醇厚' },
            'noble-elegant': { name: '👑 贵族典雅风', color: '#191970', description: '藏青色·深紫色·银色，深邃高贵' },
            'fresh-elegant': { name: '🌸 清新优雅风', color: '#4682b4', description: '钢蓝色·热粉色·幽灵白，温馨浪漫' },
            'spring-fresh': { name: '🌱 春日清新风', color: '#228b22', description: '森林绿·金色·薄荷奶油，清爽宜人' },
            'luxury-solemn': { name: '✨ 华丽庄重风', color: '#ffd700', description: '金色·黑色·红色，华丽高贵' },
            'royal-solemn': { name: '🎭 皇室庄重风', color: '#4b0082', description: '深紫色·深红色·黑色，神秘庄重' }
        };

        this.init();
    }

    /**
     * 初始化主题系统
     */
    init() {
        // 从系统设置或本地存储加载主题
        this.loadTheme();

        // 应用主题
        this.applyTheme(this.currentTheme);

        // 绑定事件监听器
        this.bindEvents();

        // 创建主题切换器（如果需要）
        this.createThemeSwitcher();
    }

    /**
     * 加载主题设置
     */
    loadTheme() {
        // 优先从系统设置加载
        const systemTheme = this.getSystemTheme();
        if (systemTheme) {
            this.currentTheme = systemTheme;
            return;
        }

        // 其次从本地存储加载
        const savedTheme = localStorage.getItem('user-theme');
        if (savedTheme && this.themes[savedTheme]) {
            this.currentTheme = savedTheme;
            return;
        }

        // 最后使用默认主题
        this.currentTheme = 'primary';
    }

    /**
     * 获取系统设置的主题
     */
    getSystemTheme() {
        // 从页面的 data-theme 属性获取
        const bodyTheme = document.body.getAttribute('data-theme');
        if (bodyTheme && this.themes[bodyTheme]) {
            return bodyTheme;
        }

        // 从页面的 meta 标签获取系统主题设置
        const metaTheme = document.querySelector('meta[name="theme-color"]');
        if (metaTheme) {
            const themeValue = metaTheme.getAttribute('content');
            if (this.themes[themeValue]) {
                return themeValue;
            }
        }

        // 从全局变量获取（如果有的话）
        if (window.systemSettings && window.systemSettings.theme_color) {
            return window.systemSettings.theme_color;
        }

        // 从系统设置页面的选择器获取
        const systemSelector = document.getElementById('setting_theme_color');
        if (systemSelector && systemSelector.value) {
            return systemSelector.value;
        }

        return null;
    }

    /**
     * 应用主题
     */
    applyTheme(themeName) {
        if (!this.themes[themeName]) {
            console.warn(`主题 "${themeName}" 不存在，使用默认主题`);
            themeName = 'primary';
        }

        // 设置 data-theme 属性
        document.documentElement.setAttribute('data-theme', themeName);
        document.body.setAttribute('data-theme', themeName);

        // 更新导航栏背景色
        this.updateNavbarTheme(themeName);

        // 更新当前主题
        this.currentTheme = themeName;

        // 保存到本地存储
        localStorage.setItem('user-theme', themeName);

        // 更新页面标题栏颜色（移动端）
        this.updateMetaThemeColor(this.themes[themeName].color);

        // 触发主题变更事件
        this.dispatchThemeChangeEvent(themeName);

        // 更新主题选择器状态
        this.updateThemeSelectorState(themeName);

        // 保存到服务器（如果用户已登录）
        this.saveThemeToServer(themeName);

        console.log(`主题已切换到: ${this.themes[themeName].name}`);
    }

    /**
     * 更新导航栏主题
     */
    updateNavbarTheme(themeName) {
        const navbar = document.querySelector('.navbar');
        if (!navbar) return;

        // 移除所有可能的背景色类
        const bgClasses = ['bg-primary', 'bg-secondary', 'bg-success', 'bg-warning', 'bg-info', 'bg-danger', 'bg-dark',
                          'bg-classic-neutral', 'bg-modern-neutral', 'bg-vintage-elegant', 'bg-noble-elegant',
                          'bg-fresh-elegant', 'bg-spring-fresh', 'bg-luxury-solemn', 'bg-royal-solemn'];

        bgClasses.forEach(cls => navbar.classList.remove(cls));

        // 添加新的背景色类
        navbar.classList.add(`bg-${themeName}`);
    }

    /**
     * 更新 meta theme-color
     */
    updateMetaThemeColor(color) {
        let metaTheme = document.querySelector('meta[name="theme-color"]');
        if (!metaTheme) {
            metaTheme = document.createElement('meta');
            metaTheme.name = 'theme-color';
            document.head.appendChild(metaTheme);
        }
        metaTheme.content = color;
    }

    /**
     * 触发主题变更事件
     */
    dispatchThemeChangeEvent(themeName) {
        const event = new CustomEvent('themeChanged', {
            detail: {
                theme: themeName,
                themeData: this.themes[themeName]
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 监听系统设置页面的主题选择器
        const systemThemeSelector = document.getElementById('setting_theme_color');
        if (systemThemeSelector) {
            systemThemeSelector.addEventListener('change', (e) => {
                this.applyTheme(e.target.value);
            });
        }

        // 监听自定义主题切换器
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('theme-option')) {
                const theme = e.target.getAttribute('data-theme');
                if (theme) {
                    this.applyTheme(theme);
                }
            }
        });

        // 监听键盘快捷键（可选）
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                this.showThemeSelector();
            }
        });
    }

    /**
     * 创建主题切换器
     */
    createThemeSwitcher() {
        // 检查是否已存在主题切换器
        if (document.getElementById('theme-switcher')) {
            return;
        }

        // 创建主题切换器HTML
        const switcherHtml = `
            <div id="theme-switcher" class="theme-switcher">
                <button class="btn btn-sm btn-outline-secondary" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-palette"></i> 主题
                </button>
                <div class="dropdown-menu">
                    <h6 class="dropdown-header">选择主题颜色</h6>
                    ${Object.entries(this.themes).map(([key, theme]) => `
                        <a class="dropdown-item theme-option" href="#" data-theme="${key}">
                            <span class="theme-preview ${key}"></span>
                            ${theme.name}
                        </a>
                    `).join('')}
                </div>
            </div>
        `;

        // 将主题切换器添加到导航栏
        const navbar = document.querySelector('.navbar-nav');
        if (navbar) {
            const li = document.createElement('li');
            li.className = 'nav-item dropdown';
            li.innerHTML = switcherHtml;
            navbar.appendChild(li);
        }
    }

    /**
     * 更新主题选择器状态
     */
    updateThemeSelectorState(themeName) {
        // 更新系统设置页面的选择器
        const systemSelector = document.getElementById('setting_theme_color');
        if (systemSelector) {
            systemSelector.value = themeName;
        }

        // 更新自定义主题切换器的状态
        const themeOptions = document.querySelectorAll('.theme-option');
        themeOptions.forEach(option => {
            option.classList.remove('active');
            if (option.getAttribute('data-theme') === themeName) {
                option.classList.add('active');
            }
        });
    }

    /**
     * 显示主题选择器
     */
    showThemeSelector() {
        const switcher = document.getElementById('theme-switcher');
        if (switcher) {
            const dropdown = switcher.querySelector('.dropdown-menu');
            dropdown.classList.toggle('show');
        }
    }

    /**
     * 获取当前主题
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * 获取所有可用主题
     */
    getAvailableThemes() {
        return this.themes;
    }

    /**
     * 预览主题（临时应用，不保存）
     */
    previewTheme(themeName) {
        if (!this.themes[themeName]) {
            return;
        }

        // 临时应用主题
        document.documentElement.setAttribute('data-theme', themeName);

        // 3秒后恢复原主题
        setTimeout(() => {
            document.documentElement.setAttribute('data-theme', this.currentTheme);
        }, 3000);
    }

    /**
     * 重置为默认主题
     */
    resetToDefault() {
        this.applyTheme('primary');
    }

    /**
     * 保存主题设置到服务器
     */
    saveThemeToServer(themeName) {
        // 临时禁用服务器保存功能，避免404错误
        console.log('主题设置仅保存在本地（服务器保存功能已临时禁用）');
        return;

        // 检查是否有CSRF token和管理员权限
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const isAdmin = document.body.dataset.userIsAdmin === 'true';

        if (!csrfToken || !isAdmin) {
            console.log('主题设置仅保存在本地（用户无管理员权限或缺少CSRF token）');
            return;
        }

        // 发送AJAX请求保存主题设置
        fetch('/admin/system/settings/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrfToken.getAttribute('content')
            },
            body: `setting_theme_color=${themeName}&value_type_theme_color=string`
        })
        .then(response => {
            if (response.ok) {
                console.log('主题设置已保存到服务器');
            } else {
                console.warn('保存主题设置到服务器失败，仅保存在本地');
            }
        })
        .catch(error => {
            console.warn('保存主题设置时发生错误，仅保存在本地:', error);
        });
    }
}

// 全局主题切换器实例
let themeSwitcher;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    themeSwitcher = new ThemeSwitcher();

    // 将实例暴露到全局作用域，方便其他脚本使用
    window.themeSwitcher = themeSwitcher;
});

// 监听主题变更事件的示例
document.addEventListener('themeChanged', function(e) {
    console.log('主题已变更:', e.detail);

    // 可以在这里添加主题变更后的额外处理逻辑
    // 例如：更新图表颜色、重新渲染某些组件等
});

// 导出类（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeSwitcher;
}
