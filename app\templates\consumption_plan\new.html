{% extends 'base.html' %}

{% block title %}创建消耗计划{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .ingredient-checkbox:checked + label:after {
        background-color: #007bff;
    }
    .condiment-row {
        transition: all 0.3s ease;
    }
    .highlight-row {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
    }
    .category-tab {
        cursor: pointer;
    }
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">创建消耗计划</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回消耗计划列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('consumption_plan.new') }}" id="consumption-form">
                        <!-- 消耗计划基本信息 -->
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">消耗计划信息</h4>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="area_id">区域</label>
                                            <select name="area_id" id="area_id" class="form-control" required>
                                                <option value="">请选择区域</option>
                                                {% for area in areas %}
                                                <option value="{{ area.id }}">{{ area.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="warehouse_id">仓库</label>
                                            <select name="warehouse_id" id="warehouse_id" class="form-control" required>
                                                <option value="">请先选择区域</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="consumption_date">消耗日期</label>
                                            <input type="date" name="consumption_date" id="consumption_date" class="form-control" value="{{ today_date }}" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="meal_type">餐次</label>
                                            <select name="meal_type" id="meal_type" class="form-control" required>
                                                <option value="早餐">早餐</option>
                                                <option value="午餐">午餐</option>
                                                <option value="晚餐">晚餐</option>
                                                <option value="加餐">加餐</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="diners_count">用餐人数</label>
                                            <input type="number" name="diners_count" id="diners_count" class="form-control" min="1">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="notes">备注</label>
                                    <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 仓库食材选择 -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h4 class="card-title">仓库食材选择</h4>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body position-relative">
                                <div id="loading-overlay" class="loading-overlay">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">加载中...</span>
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> 请先选择区域和仓库，然后选择需要消耗的主要食材，调味品可在下方自由添加。
                                </div>

                                <!-- 食材分类选项卡 -->
                                <ul class="nav nav-tabs" id="ingredient-tabs" role="tablist">
                                    {% for category in ingredient_categories %}
                                    <li class="nav-item">
                                        <a class="nav-link category-tab {% if loop.first %}active{% endif %}"
                                           id="category-{{ category.id }}-tab"
                                           data-toggle="tab"
                                           href="#category-{{ category.id }}"
                                           role="tab"
                                           data-category-id="{{ category.id }}">
                                            {{ category.name }}
                                        </a>
                                    </li>
                                    {% endfor %}
                                </ul>

                                <!-- 食材列表 -->
                                <div class="tab-content mt-3" id="ingredient-content">
                                    {% for category in ingredient_categories %}
                                    <div class="tab-pane fade {% if loop.first %}show active{% endif %}"
                                         id="category-{{ category.id }}"
                                         role="tabpanel">

                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 40px">选择</th>
                                                        <th>食材名称</th>
                                                        <th>库存数量</th>
                                                        <th>单位</th>
                                                        <th>计划消耗数量</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="category-{{ category.id }}-body">
                                                    <tr>
                                                        <td colspan="5" class="text-center">请先选择区域和仓库</td>
                                                    </tr>
                                                    <tr id="create-warehouse-row" style="display: none;">
                                                        <td colspan="5" class="text-center">
                                                            <div class="alert alert-warning">
                                                                <p><i class="fas fa-exclamation-triangle"></i> 所选区域没有关联的仓库，无法显示库存食材。</p>
                                                                <a href="{{ url_for('warehouse.index') }}" class="btn btn-primary btn-sm" target="_blank">
                                                                    <i class="fas fa-plus"></i> 前往仓库管理
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>

                        <!-- 调味品和其他消耗 -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h4 class="card-title">调味品和其他消耗</h4>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> 可自由添加调味品和其他消耗项目。
                                </div>

                                <div id="condiments-container">
                                    <div class="row condiment-row mb-2">
                                        <div class="col-md-4">
                                            <input type="text" name="condiment_name[]" class="form-control" placeholder="名称">
                                        </div>
                                        <div class="col-md-3">
                                            <input type="number" name="condiment_quantity[]" class="form-control" placeholder="数量" step="0.01" min="0">
                                        </div>
                                        <div class="col-md-3">
                                            <input type="text" name="condiment_unit[]" class="form-control" placeholder="单位">
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-danger remove-condiment">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <button type="button" id="add-condiment" class="btn btn-success mt-2">
                                    <i class="fas fa-plus"></i> 添加调味品
                                </button>
                            </div>
                        </div>

                        <div class="form-group text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save"></i> 创建消耗计划
                            </button>
                            <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-default btn-lg">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>

                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 初始化隐藏加载遮罩
        $('#loading-overlay').hide();

        // 页面加载时自动选择第一个区域和仓库
        if ($('#area_id option').length > 1) {
            // 如果有区域选项（除了"请选择区域"外）
            var firstAreaId = $('#area_id option:eq(1)').val();
            $('#area_id').val(firstAreaId).trigger('change');

            // 注意：仓库的选择会在区域change事件中处理
            // 如果区域只有一个仓库，会自动选择该仓库
        }

        // 处理区域选择
        $('#area_id').change(function() {
            var areaId = $(this).val();
            if (!areaId) {
                // 清空仓库下拉框
                $('#warehouse_id').html('<option value="">请先选择区域</option>');
                // 清空所有分类的食材列表
                $('.tab-pane tbody').empty().html('<tr><td colspan="5" class="text-center">请先选择区域和仓库</td></tr>');
                return;
            }

            // 显示加载遮罩
            $('#loading-overlay').show();

            // 获取该区域的仓库列表
            $.getJSON('{{ url_for("consumption_plan.get_warehouses", area_id=0) }}'.replace('0', areaId))
                .done(function(data) {
                    // 清空仓库下拉框
                    $('#warehouse_id').empty();

                    // 添加默认选项
                    $('#warehouse_id').append('<option value="">请选择仓库</option>');

                    // 添加仓库选项
                    if (data.warehouses && data.warehouses.length > 0) {
                        $.each(data.warehouses, function(index, warehouse) {
                            $('#warehouse_id').append('<option value="' + warehouse.id + '">' + warehouse.name + '</option>');
                        });

                        // 如果只有一个仓库，自动选择
                        if (data.warehouses.length === 1) {
                            $('#warehouse_id').val(data.warehouses[0].id).trigger('change');
                        }
                    } else {
                        // 如果没有仓库，显示提示
                        $('.tab-pane tbody').empty().html(`
                            <tr>
                                <td colspan="5" class="text-center">
                                    <div class="alert alert-warning">
                                        <p><i class="fas fa-exclamation-triangle"></i> 该区域没有关联的仓库</p>
                                        <p>请先在仓库管理中为该区域创建仓库</p>
                                        <a href="{{ url_for('warehouse.index') }}" class="btn btn-primary btn-sm" target="_blank">
                                            <i class="fas fa-plus"></i> 前往仓库管理
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        `);
                    }
                })
                .fail(function(jqXHR, textStatus, errorThrown) {
                    // 显示错误信息
                    $('#warehouse_id').html('<option value="">获取仓库失败</option>');

                    // 记录错误到控制台，方便调试
                    console.error('获取仓库失败:', jqXHR.responseJSON || errorThrown);
                })
                .always(function() {
                    // 隐藏加载遮罩
                    $('#loading-overlay').hide();
                });
        });

        // 处理仓库选择
        $('#warehouse_id').change(function() {
            var warehouseId = $(this).val();
            if (!warehouseId) {
                // 清空所有分类的食材列表
                $('.tab-pane tbody').empty().html('<tr><td colspan="5" class="text-center">请先选择仓库</td></tr>');
                return;
            }

            // 显示加载遮罩
            $('#loading-overlay').show();

            // 获取该仓库的库存食材
            $.getJSON('{{ url_for("consumption_plan.get_warehouse_inventory", warehouse_id=0) }}'.replace('0', warehouseId))
                .done(function(data) {
                    // 清空所有分类的食材列表
                    $('.tab-pane tbody').empty();

                    // 检查是否有警告信息（自动创建仓库的情况）
                    if (data.warning) {
                        // 显示警告信息
                        $('.tab-pane tbody').each(function() {
                            $(this).html(`
                                <tr>
                                    <td colspan="5" class="text-center">
                                        <div class="alert alert-success">
                                            <p><i class="fas fa-check-circle"></i> ${data.warning}</p>
                                            <p>${data.message}</p>
                                            <a href="{{ url_for('stock_in.index') }}" class="btn btn-primary btn-sm" target="_blank">
                                                <i class="fas fa-plus"></i> 前往入库管理添加库存
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            `);
                        });
                        return;
                    }

                    // 填充食材列表
                    $.each(data, function(categoryId, categoryData) {
                        var tbody = $('#category-' + categoryId + '-body');

                        if (categoryData.inventories.length === 0) {
                            tbody.html('<tr><td colspan="5" class="text-center">该分类下暂无库存食材</td></tr>');
                            return;
                        }

                        $.each(categoryData.inventories, function(index, inventory) {
                            var row = `
                                <tr>
                                    <td>
                                        <div class="icheck-primary">
                                            <input type="checkbox"
                                                   id="ingredient-${inventory.ingredient_id}"
                                                   name="selected_ingredients"
                                                   value="${inventory.ingredient_id}"
                                                   class="ingredient-checkbox">
                                            <label for="ingredient-${inventory.ingredient_id}"></label>
                                        </div>
                                    </td>
                                    <td>${inventory.ingredient_name}</td>
                                    <td>${inventory.total_quantity}</td>
                                    <td>${inventory.unit}</td>
                                    <td>
                                        <input type="number"
                                               name="quantity_${inventory.ingredient_id}"
                                               class="form-control ingredient-quantity"
                                               step="0.01"
                                               min="0"
                                               max="${inventory.total_quantity}"
                                               disabled>
                                    </td>
                                </tr>
                            `;
                            tbody.append(row);
                        });
                    });

                    // 重新绑定食材选择事件
                    bindIngredientCheckboxes();
                })
                .fail(function(jqXHR, textStatus, errorThrown) {
                    // 清空所有分类的食材列表
                    $('.tab-pane tbody').empty();

                    // 显示错误信息
                    if (jqXHR.status === 404) {
                        // 如果是404错误，说明没有找到仓库
                        $('.tab-pane tbody').each(function() {
                            $(this).html(`
                                <tr>
                                    <td colspan="5" class="text-center text-danger">
                                        <div class="alert alert-warning">
                                            <p><i class="fas fa-exclamation-triangle"></i> ${jqXHR.responseJSON ? jqXHR.responseJSON.error : '该区域未设置仓库'}</p>
                                            <p>${jqXHR.responseJSON ? jqXHR.responseJSON.message : '请先在仓库管理中为该区域创建仓库'}</p>
                                            <a href="{{ url_for('warehouse.index') }}" class="btn btn-primary btn-sm" target="_blank">
                                                <i class="fas fa-plus"></i> 前往仓库管理
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            `);
                        });
                    } else {
                        // 其他错误
                        $('.tab-pane tbody').each(function() {
                            $(this).html(`
                                <tr>
                                    <td colspan="5" class="text-center text-danger">
                                        <div class="alert alert-danger">
                                            <p><i class="fas fa-exclamation-circle"></i> 获取库存食材失败</p>
                                            <p>${jqXHR.responseJSON ? jqXHR.responseJSON.error : errorThrown}</p>
                                            <p>${jqXHR.responseJSON && jqXHR.responseJSON.message ? jqXHR.responseJSON.message : ''}</p>
                                        </div>
                                    </td>
                                </tr>
                            `);
                        });

                        // 记录错误到控制台，方便调试
                        console.error('获取库存食材失败:', jqXHR.responseJSON || errorThrown);
                    }
                })
                .always(function() {
                    // 隐藏加载遮罩
                    $('#loading-overlay').hide();
                });
        });

        // 绑定食材选择事件
        function bindIngredientCheckboxes() {
            $('.ingredient-checkbox').change(function() {
                var ingredientId = $(this).val();
                var quantityInput = $('input[name="quantity_' + ingredientId + '"]');

                if ($(this).is(':checked')) {
                    quantityInput.prop('disabled', false).focus();
                } else {
                    quantityInput.prop('disabled', true).val('');
                }
            });
        }

        // 添加调味品行
        $('#add-condiment').click(function() {
            var newRow = `
                <div class="row condiment-row mb-2 highlight-row">
                    <div class="col-md-4">
                        <input type="text" name="condiment_name[]" class="form-control" placeholder="名称">
                    </div>
                    <div class="col-md-3">
                        <input type="number" name="condiment_quantity[]" class="form-control" placeholder="数量" step="0.01" min="0">
                    </div>
                    <div class="col-md-3">
                        <input type="text" name="condiment_unit[]" class="form-control" placeholder="单位">
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-danger remove-condiment">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;

            $('#condiments-container').append(newRow);

            // 添加后移除高亮效果
            setTimeout(function() {
                $('.highlight-row').removeClass('highlight-row');
            }, 1000);
        });

        // 删除调味品行
        $(document).on('click', '.remove-condiment', function() {
            $(this).closest('.condiment-row').fadeOut(300, function() {
                $(this).remove();
            });
        });

        // 表单提交前验证
        $('#consumption-form').submit(function(e) {
            // 检查是否选择了区域
            var areaId = $('#area_id').val();
            if (!areaId) {
                e.preventDefault();
                alert('请选择区域');
                return false;
            }

            // 检查是否选择了仓库
            var warehouseId = $('#warehouse_id').val();
            if (!warehouseId) {
                e.preventDefault();
                alert('请选择仓库');
                return false;
            }

            var hasSelectedIngredients = false;

            // 检查是否选择了至少一个食材
            $('.ingredient-checkbox:checked').each(function() {
                var ingredientId = $(this).val();
                var quantity = $('input[name="quantity_' + ingredientId + '"]').val();

                if (quantity && quantity > 0) {
                    hasSelectedIngredients = true;
                    return false; // 跳出循环
                }
            });

            // 检查是否添加了至少一个调味品
            var hasCondiments = false;
            $('input[name="condiment_name[]"]').each(function(index) {
                var name = $(this).val();
                var quantity = $('input[name="condiment_quantity[]"]').eq(index).val();

                if (name && quantity && quantity > 0) {
                    hasCondiments = true;
                    return false; // 跳出循环
                }
            });

            if (!hasSelectedIngredients && !hasCondiments) {
                e.preventDefault();
                alert('请至少选择一个食材或添加一个调味品');
                return false;
            }

            return true;
        });
    });
</script>
{% endblock %}
