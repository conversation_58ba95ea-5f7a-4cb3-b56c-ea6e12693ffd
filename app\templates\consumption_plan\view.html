{% extends 'base.html' %}

{% block title %}查看消耗计划{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">消耗计划详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 30%">ID</th>
                                    <td>{{ consumption_plan.id }}</td>
                                </tr>
                                <tr>
                                    <th>消耗日期</th>
                                    <td>
                                        {% if consumption_plan.consumption_date %}
                                            {{ consumption_plan.consumption_date|format_datetime('%Y-%m-%d') }}
                                        {% elif menu_plan %}
                                            {{ menu_plan.plan_date|format_datetime('%Y-%m-%d') }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>餐次</th>
                                    <td>
                                        {% if consumption_plan.meal_type %}
                                            {{ consumption_plan.meal_type }}
                                        {% elif menu_plan %}
                                            {{ menu_plan.meal_type }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>用餐人数</th>
                                    <td>
                                        {% if consumption_plan.diners_count %}
                                            {{ consumption_plan.diners_count }}
                                        {% elif menu_plan and menu_plan.expected_diners %}
                                            {{ menu_plan.expected_diners }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if menu_plan %}
                                <tr>
                                    <th>菜单计划</th>
                                    <td>
                                        <a href="{{ url_for('menu_plan.view', id=menu_plan.id) }}">
                                            {{ menu_plan.plan_date|format_datetime('%Y-%m-%d') }} {{ menu_plan.meal_type }}
                                        </a>
                                    </td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th>区域</th>
                                    <td>
                                        {% if menu_plan %}
                                            {{ menu_plan.area.name }}
                                        {% else %}
                                            {% if area_name %}
                                                {{ area_name }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        {% if consumption_plan.status == '计划中' %}
                                        <span class="badge badge-warning">计划中</span>
                                        {% elif consumption_plan.status == '已审核' %}
                                        <span class="badge badge-info">已审核</span>
                                        {% elif consumption_plan.status == '已执行' %}
                                        <span class="badge badge-success">已执行</span>
                                        {% elif consumption_plan.status == '已取消' %}
                                        <span class="badge badge-danger">已取消</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 30%">创建人</th>
                                    <td>{{ consumption_plan.creator.real_name or consumption_plan.creator.username }}</td>
                                </tr>
                                <tr>
                                    <th>审批人</th>
                                    <td>{{ consumption_plan.approver.real_name or consumption_plan.approver.username if consumption_plan.approver else '-' }}</td>
                                </tr>
                                <tr>
                                    <th>创建时间</th>
                                    <td>{{ consumption_plan.created_at }}</td>
                                </tr>
                                <tr>
                                    <th>备注</th>
                                    <td>{{ consumption_plan.notes or '-' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- 消耗明细列表 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">消耗明细列表</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>食材名称</th>
                                            <th>计划消耗量</th>
                                            <th>实际消耗量</th>
                                            <th>单位</th>
                                            <th>当前库存</th>
                                            <th>库存状态</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for detail in consumption_details %}
                                        <tr>
                                            <td>{{ detail.ingredient.name }}</td>
                                            <td>{{ detail.planned_quantity }}</td>
                                            <td>{{ detail.actual_quantity or '-' }}</td>
                                            <td>{{ detail.unit }}</td>
                                            <td>{{ inventory_status[detail.id].total }}</td>
                                            <td>
                                                {% if inventory_status[detail.id].sufficient %}
                                                <span class="badge badge-success">库存充足</span>
                                                {% else %}
                                                <span class="badge badge-danger">库存不足</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if detail.status == '待出库' %}
                                                <span class="badge badge-warning">待出库</span>
                                                {% elif detail.status == '已出库' %}
                                                <span class="badge badge-success">已出库</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center">暂无消耗明细</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 出库单信息 -->
                    {% if consumption_plan.status == '已执行' and stock_out %}
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">出库单信息</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">出库单号</th>
                                            <td>{{ stock_out.stock_out_number }}</td>
                                        </tr>
                                        <tr>
                                            <th>仓库</th>
                                            <td>{{ stock_out.warehouse.name if stock_out and stock_out.warehouse else '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>出库日期</th>
                                            <td>{{ stock_out.stock_out_date.strftime('%Y-%m-%d %H:%M:%S') if stock_out and stock_out.stock_out_date else '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>出库类型</th>
                                            <td>{{ stock_out.stock_out_type if stock_out else '-' }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">操作人</th>
                                            <td>{{ stock_out.operator.real_name or stock_out.operator.username if stock_out and stock_out.operator else '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>状态</th>
                                            <td>
                                                <span class="badge badge-success">{{ stock_out.status if stock_out else '-' }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>备注</th>
                                            <td>{{ stock_out.notes or '-' if stock_out else '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>操作</th>
                                            <td>
                                                <a href="{{ url_for('stock_out.view', id=stock_out.id) }}" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i> 查看详情
                                                </a>
                                                <button type="button" class="btn btn-primary btn-sm" onclick="printStockOut({{ stock_out.id }})">
                                                    <i class="fas fa-print"></i> 打印出库单
                                                </button>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- 出库明细 -->
                            <div class="table-responsive mt-3">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>食材名称</th>
                                            <th>批次号</th>
                                            <th>出库数量</th>
                                            <th>单位</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if stock_out and stock_out.stock_out_items.all() %}
                                            {% for item in stock_out.stock_out_items.all() %}
                                            <tr>
                                                <td>{{ item.ingredient.name }}</td>
                                                <td>{{ item.batch_number }}</td>
                                                <td>{{ item.quantity }}</td>
                                                <td>{{ item.unit }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="4" class="text-center">暂无出库明细</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 操作按钮 -->
                    <div class="row mt-4">
                        <div class="col-12 text-center">
                            {% if consumption_plan.status == '计划中' %}
                            <button type="button" class="btn btn-info" data-toggle="modal" data-target="#approveModal">
                                <i class="fas fa-check"></i> 审核
                            </button>
                            <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#cancelModal">
                                <i class="fas fa-times"></i> 取消计划
                            </button>
                            {% elif consumption_plan.status == '已审核' %}
                            <button type="button" class="btn btn-success" data-toggle="modal" data-target="#executeModal">
                                <i class="fas fa-play"></i> 执行计划
                            </button>
                            <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#cancelModal">
                                <i class="fas fa-times"></i> 取消计划
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 审核确认模态框 -->
<div class="modal fade" id="approveModal" tabindex="-1" role="dialog" aria-labelledby="approveModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approveModalLabel">确认审核</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                确定要审核该消耗计划吗？
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form action="{{ url_for('consumption_plan.approve', id=consumption_plan.id) }}" method="post"><button type="submit" class="btn btn-info">确认审核</button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
            </div>
        </div>
    </div>
</div>

<!-- 取消确认模态框 -->
<div class="modal fade" id="cancelModal" tabindex="-1" role="dialog" aria-labelledby="cancelModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cancelModalLabel">确认取消</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                确定要取消该消耗计划吗？此操作不可撤销。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form action="{{ url_for('consumption_plan.cancel', id=consumption_plan.id) }}" method="post"><button type="submit" class="btn btn-danger">确认取消</button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
            </div>
        </div>
    </div>
</div>

<!-- 执行确认模态框 -->
<div class="modal fade" id="executeModal" tabindex="-1" role="dialog" aria-labelledby="executeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="executeModalLabel">执行消耗计划</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ url_for('consumption_plan.execute', id=consumption_plan.id) }}" method="post"><div class="modal-body">
                    <p>请确认以下食材的实际消耗量：</p>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>食材名称</th>
                                    <th>计划消耗量</th>
                                    <th>实际消耗量</th>
                                    <th>单位</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for detail in consumption_details %}
                                <tr data-detail-id="{{ detail.id }}" data-ingredient-id="{{ detail.ingredient_id }}">
                                    <td>{{ detail.ingredient.name }}</td>
                                    <td>{{ detail.planned_quantity }}</td>
                                    <td>
                                        <input type="number" name="actual_quantity_{{ detail.id }}" class="form-control" min="0" step="0.01" value="{{ detail.planned_quantity }}">
                                    </td>
                                    <td>{{ detail.unit }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success" onclick="return validateExecuteForm()">确认执行</button>
                </div>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    // 验证执行消耗计划表单
    function validateExecuteForm() {
        // 检查所有输入值是否有效
        const inputs = document.querySelectorAll('#executeModal input[type="number"]');
        let isValid = true;

        inputs.forEach(input => {
            const value = parseFloat(input.value);
            if (isNaN(value) || value < 0) {
                alert('请输入有效的消耗量（不能为负数）');
                input.focus();
                isValid = false;
                return false;
            }
        });

        // 如果基本验证通过，检查是否有重复食材ID但数量不同的情况
        if (isValid) {
            // 使用食材ID作为键，而不是食材名称
            const ingredientMap = new Map();

            document.querySelectorAll('#executeModal tbody tr').forEach(row => {
                const ingredientId = row.getAttribute('data-ingredient-id');
                const detailId = row.getAttribute('data-detail-id');
                const ingredientName = row.cells[0].textContent.trim();
                const plannedQuantity = row.cells[1].textContent.trim();
                const actualQuantity = row.querySelector('input').value;

                // 如果这个食材ID已经处理过，不需要报错，因为同一种食材可以有多个不同的消耗记录
                // 只需记录下来用于调试
                if (ingredientMap.has(ingredientId)) {
                    console.log(`食材ID ${ingredientId} (${ingredientName}) 有多个消耗记录，数量分别为 ${ingredientMap.get(ingredientId).quantity} 和 ${actualQuantity}`);
                }

                // 记录这个食材的信息
                ingredientMap.set(ingredientId, {
                    name: ingredientName,
                    quantity: actualQuantity,
                    detailId: detailId
                });
            });
        }

        return isValid;
    }

    function printConsumptionPlan() {
        // 创建打印窗口
        let printWindow = window.open('', '_blank', 'height=600,width=800');

        // 构建打印内容
        let printContent = `
            <html>
            <head>
                <title>消耗计划详情</title>
                <style nonce="{{ csp_nonce }}">
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 20px;
                    }
                    .header h2 {
                        margin-bottom: 5px;
                    }
                    .info-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 20px;
                    }
                    .info-table th, .info-table td {
                        border: 1px solid #ddd;
                        padding: 8px;
                    }
                    .info-table th {
                        width: 25%;
                        text-align: right;
                        background-color: #f2f2f2;
                    }
                    .items-table {
                        width: 100%;
                        border-collapse: collapse;
                    }
                    .items-table th, .items-table td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: center;
                    }
                    .items-table th {
                        background-color: #f2f2f2;
                    }
                    .footer {
                        margin-top: 30px;
                        display: flex;
                        justify-content: space-between;
                    }
                    .signature {
                        width: 45%;
                    }
                    @media print {
                        .no-print {
                            display: none;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>消耗计划详情</h2>
                    <p>计划编号：{{ consumption_plan.id }}</p>
                </div>

                <table class="info-table">
                    <tr>
                        <th>消耗日期</th>
                        <td>
                            {% if consumption_plan.consumption_date %}
                                {{ consumption_plan.consumption_date|format_datetime('%Y-%m-%d') }}
                            {% elif menu_plan %}
                                {{ menu_plan.plan_date|format_datetime('%Y-%m-%d') }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <th>餐次</th>
                        <td>
                            {% if consumption_plan.meal_type %}
                                {{ consumption_plan.meal_type }}
                            {% elif menu_plan %}
                                {{ menu_plan.meal_type }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>用餐人数</th>
                        <td>
                            {% if consumption_plan.diners_count %}
                                {{ consumption_plan.diners_count }}
                            {% elif menu_plan and menu_plan.expected_diners %}
                                {{ menu_plan.expected_diners }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <th>区域</th>
                        <td>
                            {% if menu_plan %}
                                {{ menu_plan.area.name }}
                            {% else %}
                                {% if area_name %}
                                    {{ area_name }}
                                {% else %}
                                    -
                                {% endif %}
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>状态</th>
                        <td>{{ consumption_plan.status }}</td>
                        <th>创建人</th>
                        <td>{{ consumption_plan.creator.real_name or consumption_plan.creator.username }}</td>
                    </tr>
                    <tr>
                        <th>创建时间</th>
                        <td>{{ consumption_plan.created_at }}</td>
                        <th>审批人</th>
                        <td>{{ consumption_plan.approver.real_name or consumption_plan.approver.username if consumption_plan.approver else '-' }}</td>
                    </tr>
                    {% if menu_plan %}
                    <tr>
                        <th>菜单计划</th>
                        <td colspan="3">
                            {{ menu_plan.plan_date|format_datetime('%Y-%m-%d') }} {{ menu_plan.meal_type }}
                        </td>
                    </tr>
                    {% endif %}
                    <tr>
                        <th>备注</th>
                        <td colspan="3">{{ consumption_plan.notes or '-' }}</td>
                    </tr>
                </table>

                <h3>消耗明细列表</h3>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>食材名称</th>
                            <th>计划消耗量</th>
                            <th>实际消耗量</th>
                            <th>单位</th>
                            <th>当前库存</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for detail in consumption_details %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ detail.ingredient.name }}</td>
                            <td>{{ detail.planned_quantity }}</td>
                            <td>{{ detail.actual_quantity or '-' }}</td>
                            <td>{{ detail.unit }}</td>
                            <td>{{ inventory_status[detail.id].total }}</td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="6" class="text-center">暂无消耗明细</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>

                {% if consumption_plan.status == '已执行' and stock_out %}
                <h3>出库单信息</h3>
                <table class="info-table">
                    <tr>
                        <th>出库单号</th>
                        <td>{{ stock_out.stock_out_number }}</td>
                        <th>仓库</th>
                        <td>{{ stock_out.warehouse.name if stock_out and stock_out.warehouse else '-' }}</td>
                    </tr>
                    <tr>
                        <th>出库日期</th>
                        <td>{{ stock_out.stock_out_date.strftime('%Y-%m-%d %H:%M:%S') if stock_out and stock_out.stock_out_date else '-' }}</td>
                        <th>出库类型</th>
                        <td>{{ stock_out.stock_out_type if stock_out else '-' }}</td>
                    </tr>
                    <tr>
                        <th>操作人</th>
                        <td>{{ stock_out.operator.real_name or stock_out.operator.username if stock_out and stock_out.operator else '-' }}</td>
                        <th>状态</th>
                        <td>{{ stock_out.status if stock_out else '-' }}</td>
                    </tr>
                </table>
                {% endif %}

                <div class="footer">
                    <div class="signature">
                        <p>制表人：{{ consumption_plan.creator.real_name or consumption_plan.creator.username }}</p>
                        <p>签名：________________</p>
                    </div>
                    <div class="signature">
                        <p>审核人：{{ consumption_plan.approver.real_name or consumption_plan.approver.username if consumption_plan.approver else '________________' }}</p>
                        <p>签名：________________</p>
                    </div>
                </div>

                <div class="no-print" style="text-align: center; margin-top: 20px;">
                    <button onclick="window.print()">打印</button>
                    <button onclick="window.close()">关闭</button>
                </div>
            </body>
            </html>
        `;

        // 写入打印内容
        printWindow.document.write(printContent);
        printWindow.document.close();

        // 等待页面加载完成后自动打印
        printWindow.onload = function() {
            printWindow.focus();
            // 如果需要自动打印，取消下面这行的注释
            // printWindow.print();
        };
    }

    function printStockOut(stockOutId) {
        // 创建打印窗口
        let printWindow = window.open('', '_blank', 'height=600,width=800');

        // 构建打印内容
        let printContent = `
            <html>
            <head>
                <title>出库单打印</title>
                <style nonce="{{ csp_nonce }}">
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 20px;
                    }
                    .header h2 {
                        margin-bottom: 5px;
                    }
                    .info-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 20px;
                    }
                    .info-table th, .info-table td {
                        border: 1px solid #ddd;
                        padding: 8px;
                    }
                    .info-table th {
                        width: 25%;
                        text-align: right;
                        background-color: #f2f2f2;
                    }
                    .items-table {
                        width: 100%;
                        border-collapse: collapse;
                    }
                    .items-table th, .items-table td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: center;
                    }
                    .items-table th {
                        background-color: #f2f2f2;
                    }
                    .footer {
                        margin-top: 30px;
                        display: flex;
                        justify-content: space-between;
                    }
                    .signature {
                        width: 45%;
                    }
                    @media print {
                        .no-print {
                            display: none;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>出库单</h2>
                    <p>单号：{{ stock_out.stock_out_number }}</p>
                </div>

                <table class="info-table">
                    <tr>
                        <th>仓库</th>
                        <td>{{ stock_out.warehouse.name if stock_out and stock_out.warehouse else '-' }}</td>
                        <th>出库日期</th>
                        <td>{{ stock_out.stock_out_date.strftime('%Y-%m-%d %H:%M:%S') if stock_out and stock_out.stock_out_date else '-' }}</td>
                    </tr>
                    <tr>
                        <th>出库类型</th>
                        <td>{{ stock_out.stock_out_type if stock_out else '-' }}</td>
                        <th>操作人</th>
                        <td>{{ stock_out.operator.real_name or stock_out.operator.username if stock_out and stock_out.operator else '-' }}</td>
                    </tr>
                    <tr>
                        <th>关联消耗计划</th>
                        <td colspan="3">{{ consumption_plan.id }}
                            {% if menu_plan %}
                                ({{ menu_plan.plan_date|format_datetime('%Y-%m-%d') }} {{ menu_plan.meal_type }})
                            {% elif consumption_plan.consumption_date %}
                                ({{ consumption_plan.consumption_date|format_datetime('%Y-%m-%d') }} {{ consumption_plan.meal_type }})
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>备注</th>
                        <td colspan="3">{{ stock_out.notes or '-' if stock_out else '-' }}</td>
                    </tr>
                </table>

                <table class="items-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>食材名称</th>
                            <th>批次号</th>
                            <th>出库数量</th>
                            <th>单位</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if stock_out and stock_out.stock_out_items.all() %}
                            {% for item in stock_out.stock_out_items.all() %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ item.ingredient.name }}</td>
                                <td>{{ item.batch_number }}</td>
                                <td>{{ item.quantity }}</td>
                                <td>{{ item.unit }}</td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="5" class="text-center">暂无出库明细</td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>

                <div class="footer">
                    <div class="signature">
                        <p>出库人：{{ stock_out.operator.real_name or stock_out.operator.username if stock_out and stock_out.operator else '-' }}</p>
                        <p>签名：________________</p>
                    </div>
                    <div class="signature">
                        <p>领用人：________________</p>
                        <p>签名：________________</p>
                    </div>
                </div>

                <div class="no-print" style="text-align: center; margin-top: 20px;">
                    <button onclick="window.print()">打印</button>
                    <button onclick="window.close()">关闭</button>
                </div>
            </body>
            </html>
        `;

        // 写入打印内容
        printWindow.document.write(printContent);
        printWindow.document.close();

        // 等待页面加载完成后自动打印
        printWindow.onload = function() {
            printWindow.focus();
            // 如果需要自动打印，取消下面这行的注释
            // printWindow.print();
        };
    }
</script>
{% endblock %}