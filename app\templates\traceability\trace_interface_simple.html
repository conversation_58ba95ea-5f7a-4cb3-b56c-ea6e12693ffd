{% extends 'base.html' %}

{% block title %}食材溯源查询{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .trace-chain {
        position: relative;
        padding: 20px 0;
    }
    .trace-step {
        display: flex;
        margin-bottom: 30px;
        position: relative;
    }
    .trace-step:not(:last-child):after {
        content: '';
        position: absolute;
        left: 25px;
        top: 50px;
        height: calc(100% - 25px);
        width: 2px;
        background-color: #dee2e6;
    }
    .trace-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        z-index: 1;
    }
    .trace-icon i {
        color: white;
        font-size: 20px;
    }
    .trace-content {
        flex: 1;
    }
    .trace-content h5 {
        margin-bottom: 10px;
    }
    .trace-card {
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        padding: 15px;
        margin-bottom: 15px;
        background-color: #fff;
    }
    .trace-detail {
        margin-top: 10px;
        padding-left: 15px;
        border-left: 2px solid #f0f0f0;
    }
    .trace-detail p {
        margin-bottom: 5px;
    }
    .trace-badge {
        font-size: 0.8em;
        padding: 3px 8px;
        border-radius: 10px;
        margin-left: 5px;
    }
    .trace-table {
        width: 100%;
        margin-top: 10px;
    }
    .trace-table th {
        background-color: #f8f9fa;
        padding: 8px;
    }
    .trace-table td {
        padding: 8px;
        border-top: 1px solid #dee2e6;
    }
    .trace-search-form {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .trace-result {
        display: none;
    }
    .loading {
        text-align: center;
        padding: 20px;
        display: none;
    }
    .loading i {
        font-size: 30px;
        color: #007bff;
    }
    .school-info {
        margin-bottom: 15px;
        font-size: 16px;
    }
    .school-name {
        font-weight: bold;
        color: #007bff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">食材溯源查询</h3>
                </div>
                <div class="card-body">
                    <!-- 当前学校信息 -->
                    <div class="school-info">
                        当前学校：<span class="school-name">{{ current_school_name }}</span>
                    </div>

                    <!-- 溯源查询表单 -->
                    <div class="trace-search-form" id="traceSearchForm">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>溯源类型</label>
                                    <select class="form-control" id="traceType" name="trace_type" required>
                                        <option value="">请选择溯源类型</option>
                                        <option value="menu_plan">菜单计划</option>
                                        <option value="consumption_plan">消耗计划</option>
                                        <option value="stock_out">出库单</option>
                                        <option value="ingredient">食材</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label id="idSelectorLabel">选择ID</label>
                                    <select class="form-control" id="idSelector" name="trace_id" required disabled>
                                        <option value="">请先选择溯源类型</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="button" id="searchButton" class="btn btn-primary btn-block" disabled>
                                        <i class="fas fa-search"></i> 查询溯源信息
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 加载中提示 -->
                    <div class="loading" id="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>正在查询溯源信息，请稍候...</p>
                    </div>

                    <!-- 溯源结果 -->
                    <div class="trace-result" id="traceResult">
                        <div class="alert alert-info">
                            <h4><i class="fas fa-info-circle"></i> 溯源结果</h4>
                            <p>以下是溯源查询的结果，展示了从<span id="resultTypeText"></span>到食材供应商的完整溯源链。</p>
                            <button type="button" id="backToSearch" class="btn btn-secondary btn-sm mt-2">
                                <i class="fas fa-arrow-left"></i> 返回查询
                            </button>
                        </div>

                        <div class="trace-chain" id="traceChain">
                            <!-- 溯源链将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/traceability.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 溯源类型选择变化时加载对应的ID选项
        $('#traceType').on('change', function() {
            var traceType = $(this).val();

            // 清空ID选择器
            $('#idSelector').empty().append('<option value="">请选择</option>').prop('disabled', true);

            // 禁用查询按钮
            $('#searchButton').prop('disabled', true);

            if (!traceType) {
                return;
            }

            // 设置标签文本
            var traceTypeText = $('#traceType option:selected').text();
            $('#idSelectorLabel').text('选择' + traceTypeText);

            // 显示加载中
            $('#loading').show();

            // 获取API URL
            var apiUrl = '';
            switch(traceType) {
                case 'menu_plan':
                    apiUrl = '/api/menu-plans';
                    break;
                case 'consumption_plan':
                    apiUrl = '/api/consumption-plans';
                    break;
                case 'stock_out':
                    apiUrl = '/api/stock-outs';
                    break;
                case 'ingredient':
                    apiUrl = '/api/ingredients';
                    break;
            }

            // 发送AJAX请求获取ID选项
            $.ajax({
                url: apiUrl,
                type: 'GET',
                success: function(options) {
                    // 隐藏加载中
                    $('#loading').hide();

                    // 启用ID选择器
                    $('#idSelector').prop('disabled', false);

                    // 添加选项
                    if (options && options.length > 0) {
                        options.forEach(function(option) {
                            $('#idSelector').append(
                                $('<option></option>').val(option.id).text(option.text)
                            );
                        });
                    } else {
                        // 如果没有数据，显示提示
                        $('#idSelector').append(
                            $('<option></option>').val('').text('没有可用数据')
                        );
                    }
                },
                error: function(xhr) {
                    // 隐藏加载中
                    $('#loading').hide();

                    // 显示错误信息
                    var errorMsg = '加载数据失败';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMsg = xhr.responseJSON.error;
                    }

                    alert('加载数据失败: ' + errorMsg);

                    // 添加一个错误选项
                    $('#idSelector').prop('disabled', false).append(
                        $('<option></option>').val('').text('加载失败，请重试')
                    );
                }
            });
        });

        // ID选择变化时启用查询按钮
        $('#idSelector').on('change', function() {
            $('#searchButton').prop('disabled', !$(this).val());
        });

        // 查询按钮点击事件
        $('#searchButton').on('click', function() {
            var traceType = $('#traceType').val();
            var traceId = $('#idSelector').val();

            if (!traceType || !traceId) {
                alert('请选择溯源类型和ID');
                return;
            }

            // 显示加载中
            $('#loading').show();
            $('#traceSearchForm').hide();

            // 设置结果类型文本
            var resultTypeText = $('#traceType option:selected').text();
            $('#resultTypeText').text(resultTypeText);

            // 发送AJAX请求
            $.ajax({
                url: '/api/trace',
                type: 'GET',
                data: {
                    trace_type: traceType,
                    trace_id: traceId
                },
                success: function(response) {
                    // 隐藏加载中
                    $('#loading').hide();

                    // 处理溯源结果
                    processTraceResult(response, traceType);

                    // 显示结果
                    $('#traceResult').show();
                },
                error: function(xhr) {
                    // 隐藏加载中
                    $('#loading').hide();
                    $('#traceSearchForm').show();

                    // 显示错误信息
                    var errorMsg = '查询失败';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMsg = xhr.responseJSON.error;
                    }

                    alert('溯源查询失败: ' + errorMsg);
                }
            });
        });

        // 返回查询按钮点击事件
        $('#backToSearch').on('click', function() {
            $('#traceResult').hide();
            $('#traceSearchForm').show();
        });
    });
</script>
{% endblock %}
