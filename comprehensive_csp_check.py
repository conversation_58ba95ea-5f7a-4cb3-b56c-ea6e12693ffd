#!/usr/bin/env python3
"""
全面 CSP 检查脚本
深入检查所有可能的 CSP 问题并提供详细的修复建议
"""

import os
import re
import glob
from collections import defaultdict, Counter
import json

def comprehensive_csp_analysis():
    """全面的 CSP 分析"""
    print("🔍 全面 CSP 问题检查")
    print("=" * 60)
    
    analysis_result = {
        'critical_issues': [],
        'major_issues': [],
        'minor_issues': [],
        'fixed_issues': [],
        'file_analysis': {},
        'pattern_analysis': {},
        'recommendations': []
    }
    
    # 查找所有 HTML 文件
    html_files = glob.glob('app/templates/**/*.html', recursive=True)
    
    print(f"📁 分析 {len(html_files)} 个文件...")
    
    for file_path in html_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            file_analysis = analyze_single_file(file_path, content)
            analysis_result['file_analysis'][file_path] = file_analysis
            
            # 分类问题
            analysis_result['critical_issues'].extend(file_analysis['critical'])
            analysis_result['major_issues'].extend(file_analysis['major'])
            analysis_result['minor_issues'].extend(file_analysis['minor'])
            analysis_result['fixed_issues'].extend(file_analysis['fixed'])
            
        except Exception as e:
            print(f"❌ 分析文件失败 {file_path}: {e}")
    
    # 生成模式分析
    generate_pattern_analysis(analysis_result)
    
    # 生成详细报告
    generate_comprehensive_report(analysis_result)
    
    return analysis_result

def analyze_single_file(file_path, content):
    """分析单个文件"""
    result = {
        'critical': [],
        'major': [],
        'minor': [],
        'fixed': []
    }
    
    # 1. 检查严重问题 - 缺少 nonce 的脚本和样式
    script_without_nonce = re.findall(r'<script(?![^>]*nonce=)(?![^>]*src=)([^>]*?)>(.*?)</script>', 
                                     content, re.IGNORECASE | re.DOTALL)
    for script in script_without_nonce:
        if script[1].strip():  # 非空脚本
            result['critical'].append({
                'file': file_path,
                'type': '缺少nonce的内联脚本',
                'content': script[1][:100] + '...' if len(script[1]) > 100 else script[1],
                'severity': 'CRITICAL',
                'impact': '脚本完全无法执行'
            })
    
    style_without_nonce = re.findall(r'<style(?![^>]*nonce=)([^>]*?)>(.*?)</style>', 
                                    content, re.IGNORECASE | re.DOTALL)
    for style in style_without_nonce:
        if style[1].strip():  # 非空样式
            result['critical'].append({
                'file': file_path,
                'type': '缺少nonce的内联样式',
                'content': style[1][:100] + '...' if len(style[1]) > 100 else style[1],
                'severity': 'CRITICAL',
                'impact': '样式完全无法应用'
            })
    
    # 2. 检查重大问题 - 关键的事件处理器
    critical_events = [
        (r'onclick\s*=\s*["\'][^"\']*confirm\s*\([^)]*删除[^)]*\)[^"\']*["\']', '删除确认'),
        (r'onclick\s*=\s*["\'][^"\']*confirm\s*\([^)]*delete[^)]*\)[^"\']*["\']', '删除确认'),
        (r'onclick\s*=\s*["\']return\s+confirm\s*\([^)]*\)[^"\']*["\']', '确认操作'),
        (r'onsubmit\s*=\s*["\']return\s+[^"\']*validate[^"\']*["\']', '表单验证'),
        (r'onsubmit\s*=\s*["\']return\s+[^"\']*check[^"\']*["\']', '表单检查'),
        (r'onclick\s*=\s*["\'][^"\']*delete[^"\']*\([^)]*\)[^"\']*["\']', '删除操作'),
        (r'onclick\s*=\s*["\'][^"\']*remove[^"\']*\([^)]*\)[^"\']*["\']', '移除操作'),
    ]
    
    for pattern, event_type in critical_events:
        matches = re.findall(pattern, content, re.IGNORECASE)
        for match in matches:
            result['major'].append({
                'file': file_path,
                'type': f'关键{event_type}事件',
                'content': match,
                'severity': 'MAJOR',
                'impact': '关键功能可能失效'
            })
    
    # 3. 检查轻微问题 - 一般的内联事件
    minor_events = [
        (r'onclick\s*=\s*["\']window\.print\(\)["\']', '打印功能'),
        (r'onclick\s*=\s*["\']history\.back\(\)["\']', '返回功能'),
        (r'onclick\s*=\s*["\']location\.reload\(\)["\']', '刷新功能'),
        (r'onclick\s*=\s*["\'][^"\']*\(\)[^"\']*["\']', '一般点击事件'),
        (r'onchange\s*=\s*["\'][^"\']*["\']', '变更事件'),
        (r'onload\s*=\s*["\'][^"\']*["\']', '加载事件'),
    ]
    
    for pattern, event_type in minor_events:
        matches = re.findall(pattern, content, re.IGNORECASE)
        for match in matches:
            result['minor'].append({
                'file': file_path,
                'type': f'{event_type}',
                'content': match,
                'severity': 'MINOR',
                'impact': '用户体验可能受影响'
            })
    
    # 4. 检查已修复的问题
    fixed_patterns = [
        (r'data-action="critical-confirm"', '关键确认已修复'),
        (r'data-action="delete-confirm"', '删除确认已修复'),
        (r'data-validation="critical"', '关键验证已修复'),
        (r'data-validation="true"', '表单验证已修复'),
        (r'nonce="\{\{\s*csp_nonce\s*\}\}"', 'nonce已添加'),
        (r'class="print-button"', '打印按钮已修复'),
        (r'class="back-button"', '返回按钮已修复'),
    ]
    
    for pattern, fix_type in fixed_patterns:
        matches = len(re.findall(pattern, content))
        if matches > 0:
            result['fixed'].append({
                'file': file_path,
                'type': fix_type,
                'count': matches,
                'severity': 'FIXED'
            })
    
    return result

def generate_pattern_analysis(analysis_result):
    """生成模式分析"""
    print(f"\n📊 问题模式分析...")
    
    # 统计问题类型
    critical_types = Counter([issue['type'] for issue in analysis_result['critical_issues']])
    major_types = Counter([issue['type'] for issue in analysis_result['major_issues']])
    minor_types = Counter([issue['type'] for issue in analysis_result['minor_issues']])
    fixed_types = Counter([issue['type'] for issue in analysis_result['fixed_issues']])
    
    analysis_result['pattern_analysis'] = {
        'critical_patterns': dict(critical_types),
        'major_patterns': dict(major_types),
        'minor_patterns': dict(minor_types),
        'fixed_patterns': dict(fixed_types)
    }
    
    print(f"🔴 严重问题模式 ({len(analysis_result['critical_issues'])} 个):")
    for pattern, count in critical_types.most_common(5):
        print(f"   {pattern}: {count} 个")
    
    print(f"\n🟠 重大问题模式 ({len(analysis_result['major_issues'])} 个):")
    for pattern, count in major_types.most_common(5):
        print(f"   {pattern}: {count} 个")
    
    print(f"\n🟡 轻微问题模式 ({len(analysis_result['minor_issues'])} 个):")
    for pattern, count in minor_types.most_common(5):
        print(f"   {pattern}: {count} 个")
    
    print(f"\n✅ 已修复模式 ({len(analysis_result['fixed_issues'])} 个):")
    for pattern, count in fixed_types.most_common(5):
        print(f"   {pattern}: {count} 个")

def generate_comprehensive_report(analysis_result):
    """生成全面报告"""
    print(f"\n📋 全面 CSP 检查报告")
    print("=" * 60)
    
    total_critical = len(analysis_result['critical_issues'])
    total_major = len(analysis_result['major_issues'])
    total_minor = len(analysis_result['minor_issues'])
    total_fixed = len(analysis_result['fixed_issues'])
    
    total_issues = total_critical + total_major + total_minor
    
    print(f"📊 问题统计:")
    print(f"   🔴 严重问题: {total_critical} 个")
    print(f"   🟠 重大问题: {total_major} 个")
    print(f"   🟡 轻微问题: {total_minor} 个")
    print(f"   ✅ 已修复: {total_fixed} 个")
    print(f"   📈 总问题数: {total_issues} 个")
    
    # 计算修复率
    if total_issues + total_fixed > 0:
        fix_rate = total_fixed / (total_issues + total_fixed) * 100
        print(f"   🎯 修复率: {fix_rate:.1f}%")
    
    # 安全等级评估
    print(f"\n🔒 安全等级评估:")
    if total_critical == 0 and total_major == 0:
        security_level = "高"
        print("✅ 高安全等级 - 无严重和重大安全问题")
    elif total_critical == 0 and total_major < 10:
        security_level = "较高"
        print("👍 较高安全等级 - 无严重问题，少量重大问题")
    elif total_critical < 5 and total_major < 20:
        security_level = "中等"
        print("⚠️ 中等安全等级 - 少量严重问题")
    else:
        security_level = "低"
        print("🚨 低安全等级 - 存在较多严重问题")
    
    # 优先级建议
    generate_priority_recommendations(analysis_result, total_critical, total_major, total_minor)
    
    # 保存详细报告
    save_detailed_report(analysis_result)

def generate_priority_recommendations(analysis_result, critical, major, minor):
    """生成优先级建议"""
    print(f"\n🚀 修复优先级建议:")
    print("=" * 40)
    
    if critical > 0:
        print(f"🔥 最高优先级 - 立即修复 ({critical} 个严重问题):")
        print("   1. 为所有内联脚本添加 nonce")
        print("   2. 为所有内联样式添加 nonce")
        print("   3. 这些问题会完全阻止功能运行")
        print(f"   ⏰ 预计修复时间: 30-60 分钟")
        
        # 显示具体的严重问题
        critical_files = set([issue['file'] for issue in analysis_result['critical_issues']])
        print(f"   📁 涉及文件: {len(critical_files)} 个")
        for file_path in list(critical_files)[:5]:
            print(f"      - {file_path}")
        if len(critical_files) > 5:
            print(f"      ... 还有 {len(critical_files) - 5} 个文件")
    
    if major > 0:
        print(f"\n⚡ 高优先级 - 优先修复 ({major} 个重大问题):")
        print("   1. 修复删除确认功能")
        print("   2. 修复表单验证功能")
        print("   3. 修复其他关键安全操作")
        print(f"   ⏰ 预计修复时间: 1-2 小时")
        
        # 显示具体的重大问题类型
        major_types = Counter([issue['type'] for issue in analysis_result['major_issues']])
        print(f"   🎯 主要问题类型:")
        for issue_type, count in major_types.most_common(3):
            print(f"      - {issue_type}: {count} 个")
    
    if minor > 0:
        print(f"\n📅 中优先级 - 计划修复 ({minor} 个轻微问题):")
        print("   1. 优化用户体验相关的事件")
        print("   2. 处理非关键的交互功能")
        print("   3. 统一代码风格")
        print(f"   ⏰ 可以逐步进行")
    
    # 生成具体的修复命令
    print(f"\n🛠️ 推荐的修复命令:")
    if critical > 0:
        print("   python fix_csp_violations.py      # 修复 nonce 问题")
    if major > 0:
        print("   python fix_critical_simple.py     # 修复关键事件")
        print("   python fix_remaining_critical.py  # 修复剩余关键问题")
    if minor > 0:
        print("   python advanced_csp_fix.py        # 修复一般问题")
        print("   python fix_inline_styles.py       # 修复样式问题")

def save_detailed_report(analysis_result):
    """保存详细报告"""
    print(f"\n💾 保存详细报告...")
    
    # 保存 JSON 格式的详细数据
    report_data = {
        'summary': {
            'critical_count': len(analysis_result['critical_issues']),
            'major_count': len(analysis_result['major_issues']),
            'minor_count': len(analysis_result['minor_issues']),
            'fixed_count': len(analysis_result['fixed_issues']),
            'total_files': len(analysis_result['file_analysis'])
        },
        'pattern_analysis': analysis_result['pattern_analysis'],
        'issues_by_severity': {
            'critical': analysis_result['critical_issues'][:10],  # 只保存前10个
            'major': analysis_result['major_issues'][:10],
            'minor': analysis_result['minor_issues'][:10]
        }
    }
    
    with open('csp_detailed_report.json', 'w', encoding='utf-8') as f:
        json.dump(report_data, f, ensure_ascii=False, indent=2)
    
    # 创建可读的 Markdown 报告
    markdown_report = generate_markdown_report(analysis_result)
    with open('CSP全面检查报告.md', 'w', encoding='utf-8') as f:
        f.write(markdown_report)
    
    print("✅ 详细报告已保存:")
    print("   - csp_detailed_report.json (数据格式)")
    print("   - CSP全面检查报告.md (可读格式)")

def generate_markdown_report(analysis_result):
    """生成 Markdown 格式的报告"""
    total_critical = len(analysis_result['critical_issues'])
    total_major = len(analysis_result['major_issues'])
    total_minor = len(analysis_result['minor_issues'])
    total_fixed = len(analysis_result['fixed_issues'])
    
    report = f"""# CSP 全面检查报告

## 📊 问题统计

| 严重程度 | 数量 | 说明 |
|---------|------|------|
| 🔴 严重问题 | {total_critical} | 完全阻止功能运行 |
| 🟠 重大问题 | {total_major} | 影响关键功能 |
| 🟡 轻微问题 | {total_minor} | 影响用户体验 |
| ✅ 已修复 | {total_fixed} | 已成功修复 |

## 🎯 修复优先级

### 🔥 最高优先级 ({total_critical} 个)
{generate_issue_list(analysis_result['critical_issues'][:5])}

### ⚡ 高优先级 ({total_major} 个)
{generate_issue_list(analysis_result['major_issues'][:5])}

### 📅 中优先级 ({total_minor} 个)
{generate_issue_list(analysis_result['minor_issues'][:5])}

## 🛠️ 修复建议

1. **立即修复严重问题**
   - 运行 `python fix_csp_violations.py`
   - 为所有内联脚本和样式添加 nonce

2. **优先修复重大问题**
   - 运行 `python fix_critical_simple.py`
   - 运行 `python fix_remaining_critical.py`

3. **计划修复轻微问题**
   - 运行 `python advanced_csp_fix.py`
   - 运行 `python fix_inline_styles.py`

## 📈 修复进度

- 已修复问题: {total_fixed} 个
- 待修复问题: {total_critical + total_major + total_minor} 个
- 修复率: {total_fixed / (total_fixed + total_critical + total_major + total_minor) * 100:.1f}%

---
*报告生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    return report

def generate_issue_list(issues):
    """生成问题列表"""
    if not issues:
        return "无问题"
    
    result = []
    for issue in issues:
        result.append(f"- **{issue['type']}** ({issue['file']})")
    
    return '\n'.join(result)

if __name__ == '__main__':
    print("🔍 全面 CSP 问题检查工具")
    print("=" * 60)
    
    # 执行全面分析
    analysis_result = comprehensive_csp_analysis()
    
    print("\n" + "=" * 60)
    print("🎉 全面检查完成！")
    print("📖 查看 'CSP全面检查报告.md' 了解详细信息")
    print("📊 查看 'csp_detailed_report.json' 了解数据详情")
    
    # 给出总体建议
    total_critical = len(analysis_result['critical_issues'])
    total_major = len(analysis_result['major_issues'])
    
    if total_critical == 0 and total_major == 0:
        print("\n🎉 恭喜！没有发现严重或重大问题！")
        print("✅ 系统可以安全运行")
    elif total_critical == 0:
        print(f"\n👍 良好！无严重问题，{total_major} 个重大问题需要处理")
        print("🔧 建议优先修复重大问题")
    else:
        print(f"\n⚠️ 发现 {total_critical} 个严重问题，需要立即修复！")
        print("🚨 这些问题会阻止功能正常运行")
    
    print("\n💡 下一步:")
    print("1. 查看详细报告了解具体问题")
    print("2. 按优先级执行修复命令")
    print("3. 重新运行检查验证修复效果")
