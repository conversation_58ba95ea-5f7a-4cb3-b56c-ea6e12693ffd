#!/usr/bin/env python3
"""
CSP 问题分析脚本
分析 CSP 错误的来源、类型和分布
"""

import os
import re
import glob
from collections import defaultdict, Counter
from pathlib import Path

def analyze_csp_issues():
    """分析 CSP 问题"""
    print("🔍 分析 CSP 问题来源...")
    print("=" * 60)
    
    # 统计数据
    stats = {
        'total_files': 0,
        'files_with_issues': 0,
        'inline_events': defaultdict(list),
        'inline_styles': [],
        'missing_nonce_scripts': [],
        'missing_nonce_styles': [],
        'module_distribution': defaultdict(int),
        'issue_types': Counter()
    }
    
    # 查找所有 HTML 文件
    html_files = glob.glob('app/templates/**/*.html', recursive=True)
    stats['total_files'] = len(html_files)
    
    print(f"📁 扫描 {len(html_files)} 个 HTML 文件...")
    
    for file_path in html_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            file_issues = analyze_file(file_path, content, stats)
            
            if file_issues > 0:
                stats['files_with_issues'] += 1
                
                # 按模块分类
                module = get_module_name(file_path)
                stats['module_distribution'][module] += file_issues
                
        except Exception as e:
            print(f"❌ 无法读取文件 {file_path}: {e}")
    
    # 生成分析报告
    generate_analysis_report(stats)

def analyze_file(file_path, content, stats):
    """分析单个文件"""
    issues_count = 0
    
    # 1. 检查内联事件处理器
    event_patterns = {
        'onclick': r'onclick\s*=\s*["\']([^"\']*)["\']',
        'onload': r'onload\s*=\s*["\']([^"\']*)["\']',
        'onchange': r'onchange\s*=\s*["\']([^"\']*)["\']',
        'onsubmit': r'onsubmit\s*=\s*["\']([^"\']*)["\']',
        'onerror': r'onerror\s*=\s*["\']([^"\']*)["\']',
        'onkeyup': r'onkeyup\s*=\s*["\']([^"\']*)["\']',
        'onfocus': r'onfocus\s*=\s*["\']([^"\']*)["\']',
        'onblur': r'onblur\s*=\s*["\']([^"\']*)["\']'
    }
    
    for event_type, pattern in event_patterns.items():
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            stats['inline_events'][event_type].extend([
                {'file': file_path, 'code': match[:50] + '...' if len(match) > 50 else match}
                for match in matches
            ])
            issues_count += len(matches)
            stats['issue_types']['内联事件处理器'] += len(matches)
    
    # 2. 检查内联样式
    style_matches = re.findall(r'style\s*=\s*["\']([^"\']*)["\']', content, re.IGNORECASE)
    if style_matches:
        stats['inline_styles'].extend([
            {'file': file_path, 'style': style[:50] + '...' if len(style) > 50 else style}
            for style in style_matches
        ])
        issues_count += len(style_matches)
        stats['issue_types']['内联样式'] += len(style_matches)
    
    # 3. 检查缺少 nonce 的脚本标签
    script_pattern = r'<script(?![^>]*nonce=)(?![^>]*src=)([^>]*)>'
    script_matches = re.findall(script_pattern, content, re.IGNORECASE)
    if script_matches:
        stats['missing_nonce_scripts'].extend([
            {'file': file_path, 'count': len(script_matches)}
        ])
        issues_count += len(script_matches)
        stats['issue_types']['缺少nonce的脚本'] += len(script_matches)
    
    # 4. 检查缺少 nonce 的样式标签
    style_tag_pattern = r'<style(?![^>]*nonce=)([^>]*)>'
    style_tag_matches = re.findall(style_tag_pattern, content, re.IGNORECASE)
    if style_tag_matches:
        stats['missing_nonce_styles'].extend([
            {'file': file_path, 'count': len(style_tag_matches)}
        ])
        issues_count += len(style_tag_matches)
        stats['issue_types']['缺少nonce的样式标签'] += len(style_tag_matches)
    
    return issues_count

def get_module_name(file_path):
    """从文件路径提取模块名"""
    path_parts = Path(file_path).parts
    if len(path_parts) >= 3:
        return path_parts[2]  # app/templates/模块名/...
    return 'root'

def generate_analysis_report(stats):
    """生成分析报告"""
    print("\n📊 CSP 问题分析报告")
    print("=" * 60)
    
    # 总体统计
    print(f"📁 总文件数: {stats['total_files']}")
    print(f"⚠️ 有问题的文件: {stats['files_with_issues']}")
    print(f"📈 问题覆盖率: {stats['files_with_issues']/stats['total_files']*100:.1f}%")
    
    # 问题类型分布
    print(f"\n🔍 问题类型分布:")
    for issue_type, count in stats['issue_types'].most_common():
        print(f"   {issue_type}: {count} 个")
    
    # 模块分布
    print(f"\n📦 问题模块分布 (Top 10):")
    for module, count in sorted(stats['module_distribution'].items(), 
                               key=lambda x: x[1], reverse=True)[:10]:
        print(f"   {module}: {count} 个问题")
    
    # 内联事件详情
    print(f"\n🎯 内联事件处理器详情:")
    for event_type, events in stats['inline_events'].items():
        if events:
            print(f"   {event_type}: {len(events)} 个")
            # 显示最常见的几个
            event_codes = [e['code'] for e in events]
            common_events = Counter(event_codes).most_common(3)
            for code, count in common_events:
                print(f"     - '{code}' ({count} 次)")
    
    # 内联样式统计
    if stats['inline_styles']:
        print(f"\n🎨 内联样式统计:")
        print(f"   总数: {len(stats['inline_styles'])}")
        
        # 分析常见的内联样式
        style_codes = [s['style'] for s in stats['inline_styles']]
        common_styles = Counter(style_codes).most_common(5)
        print(f"   最常见的样式:")
        for style, count in common_styles:
            print(f"     - '{style}' ({count} 次)")
    
    # 生成修复建议
    generate_fix_recommendations(stats)

def generate_fix_recommendations(stats):
    """生成修复建议"""
    print(f"\n💡 修复建议:")
    print("=" * 40)
    
    total_issues = sum(stats['issue_types'].values())
    
    if total_issues == 0:
        print("🎉 恭喜！没有发现 CSP 问题！")
        return
    
    print(f"📊 总问题数: {total_issues}")
    
    # 按优先级给出建议
    recommendations = []
    
    if stats['issue_types']['内联事件处理器'] > 0:
        recommendations.append({
            'priority': 1,
            'title': '修复内联事件处理器',
            'count': stats['issue_types']['内联事件处理器'],
            'action': '运行 advanced_csp_fix.py 自动修复大部分内联事件'
        })
    
    if stats['issue_types']['缺少nonce的脚本'] > 0:
        recommendations.append({
            'priority': 2,
            'title': '添加脚本 nonce',
            'count': stats['issue_types']['缺少nonce的脚本'],
            'action': '为所有内联 <script> 标签添加 nonce="{{ csp_nonce }}"'
        })
    
    if stats['issue_types']['缺少nonce的样式标签'] > 0:
        recommendations.append({
            'priority': 3,
            'title': '添加样式 nonce',
            'count': stats['issue_types']['缺少nonce的样式标签'],
            'action': '为所有内联 <style> 标签添加 nonce="{{ csp_nonce }}"'
        })
    
    if stats['issue_types']['内联样式'] > 0:
        recommendations.append({
            'priority': 4,
            'title': '优化内联样式',
            'count': stats['issue_types']['内联样式'],
            'action': '将常用内联样式转换为 CSS 类或使用 Bootstrap 工具类'
        })
    
    # 按优先级排序并显示
    for rec in sorted(recommendations, key=lambda x: x['priority']):
        print(f"\n{rec['priority']}. {rec['title']} ({rec['count']} 个)")
        print(f"   建议: {rec['action']}")
    
    # 自动化修复命令
    print(f"\n🤖 自动化修复命令:")
    print("   python fix_csp_violations.py      # 基础修复")
    print("   python advanced_csp_fix.py        # 高级修复")
    print("   python verify_csp_fix.py          # 验证修复效果")

def analyze_why_so_many():
    """分析为什么有这么多错误"""
    print(f"\n🤔 为什么有这么多 CSP 错误？")
    print("=" * 50)
    
    reasons = [
        {
            'reason': '历史遗留代码',
            'description': '项目早期没有 CSP 策略，开发者使用内联代码',
            'examples': ['onclick="func()"', 'style="color: red"', '<script>alert()</script>']
        },
        {
            'reason': '快速开发习惯',
            'description': '为了快速实现功能，直接在 HTML 中写代码',
            'examples': ['onclick="window.print()"', 'onload="init()"', 'style="display:none"']
        },
        {
            'reason': '复制粘贴代码',
            'description': '从网上或其他项目复制代码时带来的问题',
            'examples': ['网上教程的示例代码', '第三方组件的内联代码']
        },
        {
            'reason': '模板继承传播',
            'description': '一个基础模板有问题，所有继承的模板都有问题',
            'examples': ['base.html 的问题影响所有页面']
        },
        {
            'reason': 'CSP 策略后加入',
            'description': 'CSP 是为了安全性后来添加的，现有代码不符合规范',
            'examples': ['原来可以运行的代码现在违反 CSP']
        }
    ]
    
    for i, reason in enumerate(reasons, 1):
        print(f"{i}. {reason['reason']}")
        print(f"   说明: {reason['description']}")
        if 'examples' in reason:
            print(f"   示例: {', '.join(reason['examples'])}")
        print()
    
    print("💡 解决方案:")
    print("   1. 使用自动化工具批量修复")
    print("   2. 建立代码规范，避免新增违规代码")
    print("   3. 在 CI/CD 中添加 CSP 检查")
    print("   4. 团队培训，提高安全意识")

if __name__ == '__main__':
    print("🔍 CSP 问题深度分析工具")
    print("=" * 60)
    
    # 分析 CSP 问题
    analyze_csp_issues()
    
    # 分析原因
    analyze_why_so_many()
    
    print("\n" + "=" * 60)
    print("📝 总结:")
    print("CSP 错误多是正常现象，特别是在:")
    print("- 大型项目中")
    print("- 历史项目中")
    print("- 后加入 CSP 策略的项目中")
    print("\n✅ 好消息是这些错误大部分可以自动修复！")
    print("🚀 继续使用我们的修复工具即可解决大部分问题。")
