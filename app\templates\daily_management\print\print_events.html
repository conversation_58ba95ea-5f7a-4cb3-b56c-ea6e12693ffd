{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/print-styles.css') }}">
<style nonce="{{ csp_nonce }}">
    body {
        font-size: 14pt;
    }
    
    .section-title {
        font-size: 16pt;
        font-weight: bold;
        margin-top: 20px;
        margin-bottom: 10px;
        border-bottom: 1px solid #4e73df;
        padding-bottom: 5px;
        color: #4e73df;
    }
    
    .info-row {
        margin-bottom: 10px;
    }
    
    .info-label {
        font-weight: bold;
    }
    
    .print-page {
        padding: 20mm;
        margin-bottom: 20mm;
        border: 1px solid #ddd;
        background: #fff;
    }
    
    @media print {
        .no-print {
            display: none !important;
        }
        
        body {
            font-size: 12pt;
        }
        
        .section-title {
            font-size: 14pt;
        }
        
        .print-page {
            padding: 0;
            margin: 0;
            border: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 打印控制按钮 -->
    <div class="no-print mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">打印预览 - 特殊事件</h6>
                <div>
                    <a href="{{ url_for('daily_management.events', log_id=log.id) }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left mr-1"></i> 返回特殊事件
                    </a>
                    <button onclick="window.print()" class="btn btn-primary btn-sm">
                        <i class="fas fa-print mr-1"></i> 打印
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle mr-1"></i> 打印预览模式。点击"打印"按钮开始打印，或使用浏览器的打印功能（Ctrl+P）。
                </div>
            </div>
        </div>
    </div>

    <!-- 打印内容 -->
    <div class="print-preview">
        <!-- 第一页：特殊事件列表 -->
        <div class="print-page avoid-break">
            <div class="print-page-indicator no-print">第1页</div>
            
            <!-- 页眉 -->
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 食堂特殊事件</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <!-- 特殊事件列表 -->
            <div class="section-title">特殊事件列表</div>
            {% if events %}
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th width="5%">序号</th>
                        <th width="20%">事件类型</th>
                        <th width="15%">事件时间</th>
                        <th width="15%">影响范围</th>
                        <th width="15%">处理状态</th>
                        <th width="30%">事件描述</th>
                    </tr>
                </thead>
                <tbody>
                    {% for event in events %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ event.event_type }}</td>
                        <td>{{ event.event_time|safe_datetime('%H:%M') }}</td>
                        <td>{{ event.impact_scope }}</td>
                        <td>{{ '已处理' if event.is_resolved else '未处理' }}</td>
                        <td>{{ event.description|truncate(50) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p class="text-muted">暂无特殊事件记录</p>
            {% endif %}
        </div>
        
        <!-- 特殊事件详情页 -->
        {% for event in events %}
        <div class="print-page avoid-break page-break">
            <div class="print-page-indicator no-print">特殊事件 {{ loop.index }}</div>
            
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 特殊事件详情</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <div class="section-title">{{ event.event_type }}</div>
            
            <div class="row info-row">
                <div class="col-md-4">
                    <span class="info-label">事件类型：</span> {{ event.event_type }}
                </div>
                <div class="col-md-4">
                    <span class="info-label">事件时间：</span> {{ event.event_time|safe_datetime('%Y-%m-%d %H:%M') }}
                </div>
                <div class="col-md-4">
                    <span class="info-label">影响范围：</span> {{ event.impact_scope }}
                </div>
            </div>
            
            <div class="row info-row">
                <div class="col-md-4">
                    <span class="info-label">处理状态：</span> {{ '已处理' if event.is_resolved else '未处理' }}
                </div>
                <div class="col-md-4">
                    <span class="info-label">记录人：</span> {{ event.recorder_name or '未知' }}
                </div>
                <div class="col-md-4">
                    <span class="info-label">记录时间：</span> {{ event.created_at|safe_datetime('%Y-%m-%d %H:%M') }}
                </div>
            </div>
            
            <div class="section-title">事件描述</div>
            <div class="row info-row">
                <div class="col-md-12">
                    <p>{{ event.description or '无' }}</p>
                </div>
            </div>
            
            <div class="section-title">处理措施</div>
            <div class="row info-row">
                <div class="col-md-12">
                    <p>{{ event.resolution_measures or '无' }}</p>
                </div>
            </div>
            
            <div class="section-title">后续跟进</div>
            <div class="row info-row">
                <div class="col-md-12">
                    <p>{{ event.follow_up or '无' }}</p>
                </div>
            </div>
            
            {% if event.photo_paths %}
            <div class="section-title">事件照片</div>
            <div class="row">
                {% for path in event.photo_paths.split(';') %}
                <div class="col-md-4 mb-3">
                    <img src="{{ path }}" alt="事件照片" class="img-fluid img-thumbnail">
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        {% endfor %}
        
        <!-- 签名区域 -->
        <div class="print-page avoid-break page-break">
            <div class="print-page-indicator no-print">签名页</div>
            
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 食堂特殊事件</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <div class="section-title">签名确认</div>
            <div class="row mt-5">
                <div class="col-md-4">
                    <div class="text-center">
                        <p>记录人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <p>食堂负责人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <p>学校负责人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-5">
                <p>（本文档由系统自动生成，打印后有效）</p>
            </div>
        </div>
    </div>
</div>

<!-- 打印控制按钮（固定在右下角） -->
<div class="print-controls no-print">
    <button onclick="window.print()"><i class="fas fa-print mr-1"></i> 打印</button>
    <button onclick="window.location.href='{{ url_for('daily_management.events', log_id=log.id) }}'"><i class="fas fa-times mr-1"></i> 取消</button>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 打印预览初始化
        $('.print-page').each(function(index) {
            $(this).find('.print-page-indicator').text('第' + (index + 1) + '页');
        });
    });
</script>
{% endblock %}
