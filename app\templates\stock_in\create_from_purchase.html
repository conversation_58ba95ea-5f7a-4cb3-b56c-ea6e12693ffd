{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row mb-4">
    <div class="col-md-8">
      <h2>{{ title }}</h2>
      <p class="text-muted">一键入库流程 - 从采购订单直接创建入库单并完成入库</p>
    </div>
    <div class="col-md-4 text-right">
      <a href="{{ url_for('purchase_order.view', id=purchase_order.id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回采购订单
      </a>
    </div>
  </div>

  <div class="card shadow mb-4">
    <div class="card-header py-3">
      <h6 class="m-0 font-weight-bold text-primary">采购订单信息</h6>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-4">
          <p><strong>订单编号：</strong> {{ purchase_order.order_number }}</p>
        </div>
        <div class="col-md-4">
          <p><strong>供应商：</strong> {{ purchase_order.supplier.name if purchase_order.supplier else '自购' }}</p>
        </div>
        <div class="col-md-4">
          <p><strong>订单日期：</strong> {{ purchase_order.order_date|format_datetime('%Y-%m-%d') if purchase_order.order_date else '-' }}</p>
        </div>
      </div>
      <div class="row">
        <div class="col-md-4">
          <p><strong>状态：</strong> {{ purchase_order.status }}</p>
        </div>
        <div class="col-md-4">
          <p><strong>总金额：</strong> {{ purchase_order.total_amount }}</p>
        </div>
        <div class="col-md-4">
          <p><strong>预计交付日期：</strong> {{ purchase_order.expected_delivery_date|format_datetime('%Y-%m-%d') if purchase_order.expected_delivery_date else '未设置' }}</p>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <p><strong>备注：</strong> {{ purchase_order.notes }}</p>
        </div>
      </div>
    </div>
  </div>

  <div class="card shadow mb-4">
    <div class="card-header py-3">
      <h6 class="m-0 font-weight-bold text-primary">采购订单明细</h6>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th>序号</th>
              <th>食材名称</th>
              <th>数量</th>
              <th>单位</th>
              <th>单价</th>
              <th>总价</th>
            </tr>
          </thead>
          <tbody>
            {% for item in purchase_order.order_items %}
            <tr>
              <td>{{ loop.index }}</td>
              <td>{{ item.ingredient.name }}</td>
              <td>{{ item.quantity }}</td>
              <td>{{ item.unit }}</td>
              <td>{{ item.unit_price }}</td>
              <td>{{ item.total_price }}</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <div class="card shadow mb-4">
    <div class="card-header py-3">
      <h6 class="m-0 font-weight-bold text-primary">创建入库单</h6>
    </div>
    <div class="card-body">
      <form method="post" action="{{ url_for('stock_in.create_from_purchase', purchase_order_id=purchase_order.id) }}">
        <!-- 不需要CSRF令牌，因为已在全局配置中禁用CSRF保护 -->
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="warehouse">仓库</label>
              <input type="text" class="form-control" id="warehouse" value="{{ warehouse.name }}" readonly>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="stock_in_date">入库日期 <span class="text-danger">*</span></label>
              <input type="date" class="form-control" id="stock_in_date" name="stock_in_date" value="{{ today }}" required>
            </div>
          </div>
        </div>
        <div class="form-group">
          <label for="notes">备注</label>
          <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
        </div>
        <div class="alert alert-info">
          <i class="fas fa-info-circle"></i> 点击"一键入库"按钮后，系统将自动创建入库单并完成入库流程，无需额外的审核和确认步骤。系统将使用标准格式的入库单号，不允许使用自定义令牌。
        </div>
        <div class="text-center">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-dolly"></i> 一键入库
          </button>
          <a href="{{ url_for('purchase_order.view', id=purchase_order.id) }}" class="btn btn-secondary">
            <i class="fas fa-times"></i> 取消
          </a>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
  $(document).ready(function() {
    // 设置默认日期为今天
    var today = new Date().toISOString().split('T')[0];
    $('#stock_in_date').val(today);
  });
</script>
{% endblock %}
