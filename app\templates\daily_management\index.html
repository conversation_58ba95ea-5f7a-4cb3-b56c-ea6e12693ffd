{% extends 'base.html' %}

{% block title %}食堂日常管理{% endblock %}

{% from 'daily_management/components/enhanced_image_uploader.html' import enhanced_image_uploader %}
{% from 'daily_management/components/data_visualization.html' import data_visualization_cards %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">食堂日常管理</h1>

    <div class="row">
        <!-- 日期选择区 -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                日期选择</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <input type="date" class="form-control" id="date-picker" value="{{ today.strftime('%Y-%m-%d') }}">
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <button class="btn btn-primary btn-sm" id="go-to-date">查看日志</button>
                        <button class="btn btn-success btn-sm" id="create-log">创建/编辑日志</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 今日工作概览 -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                今日工作概览</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if today_log %}
                                <p class="mb-1">管理员: {{ today_log.manager or '未设置' }}</p>
                                <p class="mb-0">就餐人数: {{ (today_log.student_count or 0) + (today_log.teacher_count or 0) + (today_log.other_count or 0) }}</p>
                                <!-- 检查记录数量 -->
                                <p class="mb-0">检查记录: 0</p>
                                {% else %}
                                <p class="text-muted">今日尚未创建工作日志</p>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 待处理事项 -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                待处理事项</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if pending_issues %}
                                <p>{{ pending_issues|length }} 个待处理问题</p>
                                {% else %}
                                <p class="text-muted">暂无待处理事项</p>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 快速操作区 -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">快速操作</h6>
                </div>
                <div class="card-body">
                    {% if today_log %}
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <a href="{{ url_for('daily_management.edit_log', date_str=today.strftime('%Y-%m-%d')) }}" class="btn btn-primary btn-block">
                                <i class="fas fa-edit mr-1"></i> 编辑今日日志
                            </a>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="{{ url_for('daily_management.inspections', log_id=today_log.id) }}" class="btn btn-info btn-block">
                                <i class="fas fa-clipboard-check mr-1"></i> 检查记录
                            </a>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="{{ url_for('daily_management.companions', log_id=today_log.id) }}" class="btn btn-success btn-block">
                                <i class="fas fa-users mr-1"></i> 陪餐记录
                            </a>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="{{ url_for('daily_management.trainings', log_id=today_log.id) }}" class="btn btn-primary btn-block">
                                <i class="fas fa-chalkboard-teacher mr-1"></i> 培训记录
                            </a>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="{{ url_for('daily_management.events', log_id=today_log.id) }}" class="btn btn-secondary btn-block">
                                <i class="fas fa-calendar-day mr-1"></i> 特殊事件
                            </a>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="{{ url_for('daily_management.issues', log_id=today_log.id) }}" class="btn btn-warning btn-block">
                                <i class="fas fa-exclamation-circle mr-1"></i> 问题记录
                            </a>
                        </div>
                    </div>
                    {% else %}
                    <p class="text-center">
                        <a href="{{ url_for('daily_management.edit_log', date_str=today.strftime('%Y-%m-%d')) }}" class="btn btn-primary">
                            <i class="fas fa-plus-circle mr-1"></i> 创建今日工作日志
                        </a>
                    </p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 最近工作日志 -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">最近工作日志</h6>
                </div>
                <div class="card-body">
                    {% if recent_logs %}
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>管理员</th>
                                    <th>就餐人数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in recent_logs %}
                                <tr>
                                    <td>{{ log.log_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ log.manager or '未设置' }}</td>
                                    <td>{{ (log.student_count or 0) + (log.teacher_count or 0) + (log.other_count or 0) }}</td>
                                    <td>
                                        <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date.strftime('%Y-%m-%d')) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('daily_management.edit_log', date_str=today.strftime('%Y-%m-%d')) }}" class="btn btn-link">查看今日日志</a>
                    </div>
                    {% else %}
                    <p class="text-center text-muted">暂无工作日志记录</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 数据可视化区域 -->
    {{ data_visualization_cards() }}

    <!-- 今日检查记录 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">今日检查记录</h6>
                    {% if today_log %}
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                            <a class="dropdown-item" href="{{ url_for('daily_management.inspections', log_id=today_log.id) }}">
                                <i class="fas fa-th-large fa-sm fa-fw mr-2 text-gray-400"></i>原卡片视图
                            </a>
                            <a class="dropdown-item" href="{{ url_for('daily_management.inspections_category_cards', log_id=today_log.id) }}">
                                <i class="fas fa-th-large fa-sm fa-fw mr-2 text-gray-400"></i>类别卡片
                            </a>
                            <a class="dropdown-item" href="{{ url_for('daily_management.inspections_card_layout', log_id=today_log.id) }}">
                                <i class="fas fa-th fa-sm fa-fw mr-2 text-gray-400"></i>卡片布局
                            </a>
                            <a class="dropdown-item" href="{{ url_for('daily_management.inspections_table', log_id=today_log.id) }}">
                                <i class="fas fa-table fa-sm fa-fw mr-2 text-gray-400"></i>表格视图
                            </a>
                            <a class="dropdown-item" href="{{ url_for('daily_management.inspections_simple_table', log_id=today_log.id) }}">
                                <i class="fas fa-border-all fa-sm fa-fw mr-2 text-gray-400"></i>简单表格
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ url_for('daily_management.edit_inspection', log_id=today_log.id, inspection_type='morning') }}">
                                <i class="fas fa-sun fa-sm fa-fw mr-2 text-warning"></i>编辑晨检
                            </a>
                            <a class="dropdown-item" href="{{ url_for('daily_management.edit_inspection', log_id=today_log.id, inspection_type='noon') }}">
                                <i class="fas fa-utensils fa-sm fa-fw mr-2 text-primary"></i>编辑午检
                            </a>
                            <a class="dropdown-item" href="{{ url_for('daily_management.edit_inspection', log_id=today_log.id, inspection_type='evening') }}">
                                <i class="fas fa-moon fa-sm fa-fw mr-2 text-info"></i>编辑晚检
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if today_log %}
                        {% with widget_id='today', log_id=today_log.id %}
                            {% include 'daily_management/inspection_table_widget.html' with context %}
                        {% endwith %}
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">今日尚未创建工作日志</p>
                            <a href="{{ url_for('daily_management.edit_log', date_str=today.strftime('%Y-%m-%d')) }}" class="btn btn-primary mt-3">
                                <i class="fas fa-plus-circle mr-1"></i> 创建今日工作日志
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 照片管理区域 -->
    {% if today_log %}
    <div class="row">
        <div class="col-12">
            {{ enhanced_image_uploader('daily_log', today_log.id, title='今日照片管理', max_files=20, max_file_size=10, show_rating=True) }}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 日期选择跳转
        $('#go-to-date').click(function() {
            var selectedDate = $('#date-picker').val();
            if (selectedDate) {
                window.location.href = "{{ url_for('daily_management.edit_log', date_str='') }}" + selectedDate;
            }
        });

        // 创建/编辑日志
        $('#create-log').click(function() {
            var selectedDate = $('#date-picker').val();
            if (selectedDate) {
                window.location.href = "{{ url_for('daily_management.edit_log', date_str='') }}" + selectedDate;
            }
        });
    });
</script>
{% endblock %}
