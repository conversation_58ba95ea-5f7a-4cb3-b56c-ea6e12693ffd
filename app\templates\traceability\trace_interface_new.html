{% extends 'base.html' %}

{% block title %}食材溯源查询{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .trace-chain {
        position: relative;
        padding: 20px 0;
    }
    .trace-step {
        display: flex;
        margin-bottom: 30px;
        position: relative;
    }
    .trace-step:not(:last-child):after {
        content: '';
        position: absolute;
        left: 25px;
        top: 50px;
        height: calc(100% - 25px);
        width: 2px;
        background-color: #dee2e6;
    }
    .trace-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        z-index: 1;
    }
    .trace-icon i {
        color: white;
        font-size: 20px;
    }
    .trace-content {
        flex: 1;
    }
    .trace-content h5 {
        margin-bottom: 10px;
    }
    .trace-card {
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        padding: 15px;
        margin-bottom: 15px;
        background-color: #fff;
    }
    .trace-detail {
        margin-top: 10px;
        padding-left: 15px;
        border-left: 2px solid #f0f0f0;
    }
    .trace-detail p {
        margin-bottom: 5px;
    }
    .trace-badge {
        font-size: 0.8em;
        padding: 3px 8px;
        border-radius: 10px;
        margin-left: 5px;
    }
    .trace-table {
        width: 100%;
        margin-top: 10px;
    }
    .trace-table th {
        background-color: #f8f9fa;
        padding: 8px;
    }
    .trace-table td {
        padding: 8px;
        border-top: 1px solid #dee2e6;
    }
    .trace-search-form {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .trace-result {
        display: none;
    }
    .loading {
        text-align: center;
        padding: 20px;
        display: none;
    }
    .loading i {
        font-size: 30px;
        color: #007bff;
    }
    .step-2, .step-3 {
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">食材溯源查询</h3>
                </div>
                <div class="card-body">
                    <!-- 溯源查询表单 - 步骤1：选择学校/区域 -->
                    <div class="trace-search-form step-1">
                        <h4 class="mb-3">第一步：选择学校</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>学校/区域</label>
                                    <select class="form-control" id="areaSelector">
                                        <option value="">请选择学校/区域</option>
                                        <!-- 区域选项将通过AJAX动态加载 -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="button" id="nextStep1" class="btn btn-primary btn-block">下一步</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 溯源查询表单 - 步骤2：选择溯源类型 -->
                    <div class="trace-search-form step-2">
                        <h4 class="mb-3">第二步：选择溯源类型</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>溯源类型</label>
                                    <select class="form-control" id="traceType">
                                        <option value="">请选择溯源类型</option>
                                        <option value="menu_plan">菜单计划</option>
                                        <option value="consumption_plan">消耗计划</option>
                                        <option value="stock_out">出库单</option>
                                        <option value="ingredient">食材</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div class="btn-group btn-block">
                                        <button type="button" id="prevStep2" class="btn btn-secondary">上一步</button>
                                        <button type="button" id="nextStep2" class="btn btn-primary">下一步</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 溯源查询表单 - 步骤2：选择具体ID -->
                    <div class="trace-search-form step-2">
                        <h4 class="mb-3">第二步：选择<span id="traceTypeText"></span></h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label id="idSelectorLabel">选择ID</label>
                                    <select class="form-control" id="idSelector">
                                        <!-- 动态加载选项 -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div class="btn-group btn-block">
                                        <button type="button" id="prevStep2" class="btn btn-secondary">上一步</button>
                                        <button type="button" id="nextStep2" class="btn btn-primary">查询溯源信息</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 加载中提示 -->
                    <div class="loading" id="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>正在查询溯源信息，请稍候...</p>
                    </div>

                    <!-- 溯源结果 - 步骤3：显示溯源信息 -->
                    <div class="step-3" id="traceResult">
                        <div class="alert alert-info">
                            <h4><i class="fas fa-info-circle"></i> 溯源结果</h4>
                            <p>以下是溯源查询的结果，展示了从<span id="resultTypeText"></span>到食材供应商的完整溯源链。</p>
                            <button type="button" id="backToSearch" class="btn btn-secondary btn-sm mt-2">
                                <i class="fas fa-arrow-left"></i> 返回查询
                            </button>
                        </div>

                        <div class="trace-chain" id="traceChain">
                            <!-- 溯源链将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 步骤1：选择溯源类型
        $('#nextStep1').on('click', function() {
            var traceType = $('#traceType').val();
            if (!traceType) {
                alert('请选择溯源类型');
                return;
            }

            // 设置标题文本
            var traceTypeText = $('#traceType option:selected').text();
            $('#traceTypeText').text(traceTypeText);
            $('#idSelectorLabel').text('选择' + traceTypeText);

            // 加载ID选项
            loadIdOptions(traceType);

            // 显示步骤2
            $('.step-1').hide();
            $('.step-2').show();
        });

        // 步骤2：返回上一步
        $('#prevStep2').on('click', function() {
            $('.step-2').hide();
            $('.step-1').show();
        });

        // 步骤2：查询溯源信息
        $('#nextStep2').on('click', function() {
            var traceType = $('#traceType').val();
            var traceId = $('#idSelector').val();

            if (!traceId) {
                alert('请选择ID');
                return;
            }

            // 显示加载中
            $('#loading').show();

            // 设置结果类型文本
            var resultTypeText = $('#traceType option:selected').text();
            $('#resultTypeText').text(resultTypeText);

            // 发送AJAX请求
            $.ajax({
                url: '/api/trace',
                type: 'GET',
                data: {
                    trace_type: traceType,
                    trace_id: traceId
                },
                success: function(response) {
                    // 隐藏加载中
                    $('#loading').hide();

                    // 处理溯源结果
                    processTraceResult(response, traceType);

                    // 显示步骤3
                    $('.step-2').hide();
                    $('.step-3').show();
                },
                error: function(xhr) {
                    // 隐藏加载中
                    $('#loading').hide();

                    // 显示错误信息
                    var errorMsg = '查询失败';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMsg = xhr.responseJSON.error;
                    }

                    alert('溯源查询失败: ' + errorMsg);
                }
            });
        });

        // 加载ID选项
        function loadIdOptions(traceType) {
            $('#idSelector').empty().append('<option value="">请选择</option>');
            $('#loading').show();

            var apiUrl = '';
            switch(traceType) {
                case 'menu_plan':
                    apiUrl = '/api/menu-plans';
                    break;
                case 'consumption_plan':
                    apiUrl = '/api/consumption-plans';
                    break;
                case 'stock_out':
                    apiUrl = '/api/stock-outs';
                    break;
                case 'ingredient':
                    apiUrl = '/api/ingredients';
                    break;
            }

            // 调用后端API获取选项数据
            $.ajax({
                url: apiUrl,
                type: 'GET',
                success: function(options) {
                    $('#loading').hide();

                    // 添加选项
                    if (options && options.length > 0) {
                        options.forEach(function(option) {
                            $('#idSelector').append(
                                $('<option></option>').val(option.id).text(option.text)
                            );
                        });
                    } else {
                        // 如果没有数据，显示提示
                        $('#idSelector').append(
                            $('<option></option>').val('').text('没有可用数据')
                        );
                    }
                },
                error: function(xhr) {
                    $('#loading').hide();

                    // 显示错误信息
                    var errorMsg = '加载数据失败';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMsg = xhr.responseJSON.error;
                    }

                    alert('加载数据失败: ' + errorMsg);

                    // 添加一个错误选项
                    $('#idSelector').append(
                        $('<option></option>').val('').text('加载失败，请重试')
                    );
                }
            });



        }

        // 处理溯源结果
        function processTraceResult(data, traceType) {
            var traceChain = $('#traceChain');
            traceChain.empty();

            // 检查数据是否为空
            if (!data || Object.keys(data).length === 0) {
                traceChain.append(`
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> 溯源数据不完整</h5>
                        <p>未能获取到任何溯源数据，请检查所选ID是否正确。</p>
                    </div>
                `);
                return;
            }

            // 根据溯源类型生成不同的溯源链
            var hasData = false;
            switch(traceType) {
                case 'menu_plan':
                    hasData = generateMenuPlanTrace(data, traceChain);
                    break;
                case 'consumption_plan':
                    hasData = generateConsumptionPlanTrace(data, traceChain);
                    break;
                case 'stock_out':
                    hasData = generateStockOutTrace(data, traceChain);
                    break;
                case 'ingredient':
                    hasData = generateIngredientTrace(data, traceChain);
                    break;
            }

            // 如果没有生成任何溯源链，显示提示
            if (!hasData) {
                traceChain.append(`
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> 溯源链断开</h5>
                        <p>溯源链不完整，部分数据缺失或无法关联。这可能是因为：</p>
                        <ul>
                            <li>相关记录已被删除</li>
                            <li>数据录入不完整</li>
                            <li>系统中存在数据不一致</li>
                        </ul>
                        <p>建议检查相关记录是否完整，或联系系统管理员。</p>
                    </div>
                `);
            }
        }

        // 生成消耗计划溯源链
        function generateConsumptionPlanTrace(data, container) {
            // 跟踪是否生成了任何溯源链节点
            var hasGeneratedChain = false;
            var hasUpstreamData = false;
            var hasDownstreamData = false;

            // 1. 消耗计划（起点）
            if (data.consumption_plan) {
                hasGeneratedChain = true;
                var html = `
                    <div class="trace-step">
                        <div class="trace-icon bg-info">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="trace-content">
                            <h5>消耗计划 <span class="badge badge-${getStatusClass(data.consumption_plan.status)}">${data.consumption_plan.status}</span></h5>
                            <div class="trace-card">
                                <p><strong>ID:</strong> ${data.consumption_plan.id}</p>
                                <p><strong>创建时间:</strong> ${data.consumption_plan.created_at}</p>
                                <p><strong>创建人:</strong> ${data.consumption_plan.creator || '未知'}</p>
                            </div>
                        </div>
                    </div>
                `;
                container.append(html);
            } else {
                // 如果没有消耗计划数据，显示断链提示
                container.append(`
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-circle"></i> 溯源起点缺失</h5>
                        <p>未能获取到消耗计划数据，溯源链无法建立。</p>
                    </div>
                `);
                return false;
            }

            // 2. 菜单计划（上游）
            if (data.menu_plan) {
                hasUpstreamData = true;
                var html = `
                    <div class="trace-step">
                        <div class="trace-icon bg-primary">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="trace-content">
                            <h5>菜单计划 <span class="badge badge-${getStatusClass(data.menu_plan.status)}">${data.menu_plan.status}</span></h5>
                            <div class="trace-card">
                                <p><strong>ID:</strong> ${data.menu_plan.id}</p>
                                <p><strong>日期:</strong> ${data.menu_plan.plan_date}</p>
                                <p><strong>餐次:</strong> ${data.menu_plan.meal_type}</p>
                                <p><strong>区域:</strong> ${data.menu_plan.area_name || '未知'}</p>
                            </div>
                        </div>
                    </div>
                `;
                container.append(html);
            } else {
                // 如果没有菜单计划数据，显示上游断链提示
                container.append(`
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> 上游溯源断链</h5>
                        <p>未能获取到菜单计划数据，上游溯源链不完整。</p>
                        <p>这可能是因为该消耗计划不是基于菜单计划创建的。</p>
                    </div>
                `);
            }

            // 3. 出库单（下游）
            if (data.stock_outs && data.stock_outs.length > 0) {
                hasDownstreamData = true;
                var html = `
                    <div class="trace-step">
                        <div class="trace-icon bg-warning">
                            <i class="fas fa-dolly"></i>
                        </div>
                        <div class="trace-content">
                            <h5>出库单</h5>
                `;

                data.stock_outs.forEach(function(stockOut) {
                    html += `
                        <div class="trace-card">
                            <p><strong>ID:</strong> ${stockOut.id}</p>
                            <p><strong>出库单号:</strong> ${stockOut.stock_out_number}</p>
                            <p><strong>出库日期:</strong> ${stockOut.stock_out_date}</p>
                            <p><strong>类型:</strong> ${stockOut.stock_out_type}</p>
                            <p><strong>状态:</strong> <span class="badge badge-${getStatusClass(stockOut.status)}">${stockOut.status}</span></p>
                            <p><strong>操作人:</strong> ${stockOut.operator || '未知'}</p>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
                container.append(html);
            } else {
                // 如果没有出库单数据，显示下游断链提示
                container.append(`
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> 下游溯源断链</h5>
                        <p>未能获取到出库单数据，下游溯源链不完整。</p>
                        <p>这可能是因为该消耗计划尚未执行出库操作。</p>
                    </div>
                `);
            }

            // 4. 库存和入库信息（最终来源）
            if (data.inventory_data && data.inventory_data.length > 0) {
                var html = `
                    <div class="trace-step">
                        <div class="trace-icon bg-success">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="trace-content">
                            <h5>库存和入库信息</h5>
                            <div class="trace-card">
                                <table class="trace-table">
                                    <thead>
                                        <tr>
                                            <th>批次号</th>
                                            <th>食材</th>
                                            <th>数量</th>
                                            <th>入库单号</th>
                                            <th>入库日期</th>
                                            <th>供应商</th>
                                            <th>生产日期</th>
                                            <th>过期日期</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                `;

                data.inventory_data.forEach(function(item) {
                    html += `
                        <tr>
                            <td>${item.batch_number || '未知'}</td>
                            <td>${item.ingredient_name || '未知'}</td>
                            <td>${item.quantity} ${item.unit}</td>
                            <td>${item.stock_in_number || '未知'}</td>
                            <td>${item.stock_in_date || '未知'}</td>
                            <td>${item.supplier_name || '未知'}</td>
                            <td>${item.production_date || '未知'}</td>
                            <td>${item.expiry_date || '未知'}</td>
                        </tr>
                    `;
                });

                html += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
                container.append(html);
            } else if (hasDownstreamData) {
                // 如果有出库单但没有库存数据，显示最终来源断链提示
                container.append(`
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> 最终来源溯源断链</h5>
                        <p>未能获取到库存和入库信息，无法追溯到食材的最终来源。</p>
                        <p>这可能是因为相关库存记录已被删除或数据不完整。</p>
                    </div>
                `);
            }

            // 返回是否成功生成溯源链
            return hasGeneratedChain && (hasUpstreamData || hasDownstreamData);
        }

        // 根据状态获取对应的Bootstrap样式类
        function getStatusClass(status) {
            switch(status) {
                case '计划中':
                    return 'secondary';
                case '已审核':
                    return 'info';
                case '已执行':
                    return 'success';
                case '已取消':
                    return 'danger';
                case '已发布':
                    return 'primary';
                case '待审核':
                    return 'warning';
                case '已出库':
                    return 'success';
                default:
                    return 'secondary';
            }
        }

        // 返回查询按钮点击事件
        $('#backToSearch').on('click', function() {
            $('.step-3').hide();
            $('.step-1').show();
            // 清空ID选择器
            $('#idSelector').empty();
            // 重置溯源类型选择器
            $('#traceType').val('');
        });

        // 生成菜单计划溯源链
        function generateMenuPlanTrace(data, container) {
            // 跟踪是否生成了任何溯源链节点
            var hasGeneratedChain = false;
            var hasDownstreamData = false;

            // 1. 菜单计划（起点）
            if (data.menu_plan) {
                hasGeneratedChain = true;
                var html = `
                    <div class="trace-step">
                        <div class="trace-icon bg-primary">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="trace-content">
                            <h5>菜单计划 <span class="badge badge-${getStatusClass(data.menu_plan.status)}">${data.menu_plan.status}</span></h5>
                            <div class="trace-card">
                                <p><strong>ID:</strong> ${data.menu_plan.id}</p>
                                <p><strong>日期:</strong> ${data.menu_plan.plan_date}</p>
                                <p><strong>餐次:</strong> ${data.menu_plan.meal_type}</p>
                                <p><strong>区域:</strong> ${data.menu_plan.area_name || '未知'}</p>
                                <p><strong>创建人:</strong> ${data.menu_plan.creator || '未知'}</p>
                            </div>
                        </div>
                    </div>
                `;
                container.append(html);
            } else {
                // 如果没有菜单计划数据，显示断链提示
                container.append(`
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-circle"></i> 溯源起点缺失</h5>
                        <p>未能获取到菜单计划数据，溯源链无法建立。</p>
                    </div>
                `);
                return false;
            }

            // 2. 消耗计划（下游）
            if (data.consumption_plans && data.consumption_plans.length > 0) {
                hasDownstreamData = true;
                var html = `
                    <div class="trace-step">
                        <div class="trace-icon bg-info">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="trace-content">
                            <h5>消耗计划</h5>
                `;

                data.consumption_plans.forEach(function(plan) {
                    html += `
                        <div class="trace-card">
                            <p><strong>ID:</strong> ${plan.id}</p>
                            <p><strong>创建时间:</strong> ${plan.created_at}</p>
                            <p><strong>状态:</strong> <span class="badge badge-${getStatusClass(plan.status)}">${plan.status}</span></p>
                            <p><strong>创建人:</strong> ${plan.creator || '未知'}</p>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
                container.append(html);
            } else {
                // 如果没有消耗计划数据，显示下游断链提示
                container.append(`
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> 下游溯源断链</h5>
                        <p>未能获取到消耗计划数据，下游溯源链不完整。</p>
                        <p>这可能是因为该菜单计划尚未生成消耗计划。</p>
                    </div>
                `);
            }

            // 返回是否成功生成溯源链
            return hasGeneratedChain && hasDownstreamData;
        }

        // 生成出库单溯源链
        function generateStockOutTrace(data, container) {
            // 跟踪是否生成了任何溯源链节点
            var hasGeneratedChain = false;
            var hasUpstreamData = false;

            // 1. 出库单（起点）
            if (data.stock_out) {
                hasGeneratedChain = true;
                var html = `
                    <div class="trace-step">
                        <div class="trace-icon bg-warning">
                            <i class="fas fa-dolly"></i>
                        </div>
                        <div class="trace-content">
                            <h5>出库单 <span class="badge badge-${getStatusClass(data.stock_out.status)}">${data.stock_out.status}</span></h5>
                            <div class="trace-card">
                                <p><strong>ID:</strong> ${data.stock_out.id}</p>
                                <p><strong>出库单号:</strong> ${data.stock_out.stock_out_number}</p>
                                <p><strong>出库日期:</strong> ${data.stock_out.stock_out_date}</p>
                                <p><strong>类型:</strong> ${data.stock_out.stock_out_type}</p>
                                <p><strong>操作人:</strong> ${data.stock_out.operator || '未知'}</p>
                            </div>
                        </div>
                    </div>
                `;
                container.append(html);
            } else {
                // 如果没有出库单数据，显示断链提示
                container.append(`
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-circle"></i> 溯源起点缺失</h5>
                        <p>未能获取到出库单数据，溯源链无法建立。</p>
                    </div>
                `);
                return false;
            }

            // 2. 消耗计划（上游）
            if (data.consumption_plan) {
                hasUpstreamData = true;
                var html = `
                    <div class="trace-step">
                        <div class="trace-icon bg-info">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="trace-content">
                            <h5>消耗计划 <span class="badge badge-${getStatusClass(data.consumption_plan.status)}">${data.consumption_plan.status}</span></h5>
                            <div class="trace-card">
                                <p><strong>ID:</strong> ${data.consumption_plan.id}</p>
                                <p><strong>创建时间:</strong> ${data.consumption_plan.created_at}</p>
                                <p><strong>创建人:</strong> ${data.consumption_plan.creator || '未知'}</p>
                            </div>
                        </div>
                    </div>
                `;
                container.append(html);

                // 3. 菜单计划（更上游）
                if (data.menu_plan) {
                    var html = `
                        <div class="trace-step">
                            <div class="trace-icon bg-primary">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="trace-content">
                                <h5>菜单计划 <span class="badge badge-${getStatusClass(data.menu_plan.status)}">${data.menu_plan.status}</span></h5>
                                <div class="trace-card">
                                    <p><strong>ID:</strong> ${data.menu_plan.id}</p>
                                    <p><strong>日期:</strong> ${data.menu_plan.plan_date}</p>
                                    <p><strong>餐次:</strong> ${data.menu_plan.meal_type}</p>
                                    <p><strong>区域:</strong> ${data.menu_plan.area_name || '未知'}</p>
                                </div>
                            </div>
                        </div>
                    `;
                    container.append(html);
                } else {
                    // 如果没有菜单计划数据，显示上游断链提示
                    container.append(`
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle"></i> 上游溯源断链</h5>
                            <p>未能获取到菜单计划数据，上游溯源链不完整。</p>
                            <p>这可能是因为该消耗计划不是基于菜单计划创建的。</p>
                        </div>
                    `);
                }
            } else {
                // 如果没有消耗计划数据，显示上游断链提示
                container.append(`
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> 上游溯源断链</h5>
                        <p>未能获取到消耗计划数据，上游溯源链不完整。</p>
                        <p>这可能是因为该出库单不是基于消耗计划创建的。</p>
                    </div>
                `);
            }

            // 4. 库存和入库信息（最终来源）
            if (data.inventory_data && data.inventory_data.length > 0) {
                var html = `
                    <div class="trace-step">
                        <div class="trace-icon bg-success">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="trace-content">
                            <h5>库存和入库信息</h5>
                            <div class="trace-card">
                                <table class="trace-table">
                                    <thead>
                                        <tr>
                                            <th>批次号</th>
                                            <th>食材</th>
                                            <th>数量</th>
                                            <th>入库单号</th>
                                            <th>入库日期</th>
                                            <th>供应商</th>
                                            <th>生产日期</th>
                                            <th>过期日期</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                `;

                data.inventory_data.forEach(function(item) {
                    html += `
                        <tr>
                            <td>${item.batch_number || '未知'}</td>
                            <td>${item.ingredient_name || '未知'}</td>
                            <td>${item.quantity} ${item.unit}</td>
                            <td>${item.stock_in_number || '未知'}</td>
                            <td>${item.stock_in_date || '未知'}</td>
                            <td>${item.supplier_name || '未知'}</td>
                            <td>${item.production_date || '未知'}</td>
                            <td>${item.expiry_date || '未知'}</td>
                        </tr>
                    `;
                });

                html += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
                container.append(html);
            } else {
                // 如果没有库存数据，显示最终来源断链提示
                container.append(`
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> 最终来源溯源断链</h5>
                        <p>未能获取到库存和入库信息，无法追溯到食材的最终来源。</p>
                        <p>这可能是因为相关库存记录已被删除或数据不完整。</p>
                    </div>
                `);
            }

            // 返回是否成功生成溯源链
            return hasGeneratedChain && hasUpstreamData;
        }

        // 生成食材溯源链
        function generateIngredientTrace(data, container) {
            // 跟踪是否生成了任何溯源链节点
            var hasGeneratedChain = false;

            // 1. 食材信息（起点）
            if (data.ingredient) {
                hasGeneratedChain = true;
                var html = `
                    <div class="trace-step">
                        <div class="trace-icon bg-success">
                            <i class="fas fa-carrot"></i>
                        </div>
                        <div class="trace-content">
                            <h5>食材信息</h5>
                            <div class="trace-card">
                                <p><strong>ID:</strong> ${data.ingredient.id}</p>
                                <p><strong>名称:</strong> ${data.ingredient.name}</p>
                                <p><strong>类别:</strong> ${data.ingredient.category_name || '未知'}</p>
                                <p><strong>单位:</strong> ${data.ingredient.unit}</p>
                            </div>
                        </div>
                    </div>
                `;
                container.append(html);
            } else {
                // 如果没有食材数据，显示断链提示
                container.append(`
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-circle"></i> 溯源起点缺失</h5>
                        <p>未能获取到食材数据，溯源链无法建立。</p>
                    </div>
                `);
                return false;
            }

            // 2. 库存和入库信息
            if (data.inventory_data && data.inventory_data.length > 0) {
                var html = `
                    <div class="trace-step">
                        <div class="trace-icon bg-info">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="trace-content">
                            <h5>库存和入库信息</h5>
                            <div class="trace-card">
                                <table class="trace-table">
                                    <thead>
                                        <tr>
                                            <th>批次号</th>
                                            <th>数量</th>
                                            <th>入库单号</th>
                                            <th>入库日期</th>
                                            <th>供应商</th>
                                            <th>生产日期</th>
                                            <th>过期日期</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                `;

                data.inventory_data.forEach(function(item) {
                    html += `
                        <tr>
                            <td>${item.batch_number || '未知'}</td>
                            <td>${item.quantity} ${item.unit}</td>
                            <td>${item.stock_in_number || '未知'}</td>
                            <td>${item.stock_in_date || '未知'}</td>
                            <td>${item.supplier_name || '未知'}</td>
                            <td>${item.production_date || '未知'}</td>
                            <td>${item.expiry_date || '未知'}</td>
                        </tr>
                    `;
                });

                html += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
                container.append(html);
            } else {
                // 如果没有库存数据，显示断链提示
                container.append(`
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> 库存溯源断链</h5>
                        <p>未能获取到该食材的库存和入库信息。</p>
                        <p>这可能是因为该食材当前没有库存，或者相关记录已被删除。</p>
                    </div>
                `);
            }

            // 3. 消耗记录
            if (data.consumption_records && data.consumption_records.length > 0) {
                var html = `
                    <div class="trace-step">
                        <div class="trace-icon bg-warning">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="trace-content">
                            <h5>消耗记录</h5>
                            <div class="trace-card">
                                <table class="trace-table">
                                    <thead>
                                        <tr>
                                            <th>消耗计划ID</th>
                                            <th>日期</th>
                                            <th>餐次</th>
                                            <th>数量</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                `;

                data.consumption_records.forEach(function(record) {
                    html += `
                        <tr>
                            <td>${record.consumption_plan_id}</td>
                            <td>${record.date || '未知'}</td>
                            <td>${record.meal_type || '未知'}</td>
                            <td>${record.quantity} ${record.unit}</td>
                            <td><span class="badge badge-${getStatusClass(record.status)}">${record.status}</span></td>
                        </tr>
                    `;
                });

                html += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
                container.append(html);
            } else {
                // 如果没有消耗记录，显示提示
                container.append(`
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> 无消耗记录</h5>
                        <p>未找到该食材的消耗记录。</p>
                        <p>这可能是因为该食材尚未被消耗，或者相关记录已被删除。</p>
                    </div>
                `);
            }

            // 返回是否成功生成溯源链
            return hasGeneratedChain;
        }
    });
</script>
{% endblock %}
