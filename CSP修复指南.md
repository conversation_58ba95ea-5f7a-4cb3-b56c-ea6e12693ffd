# CSP 违规快速修复指南

## 🚀 自动修复

运行以下命令自动修复大部分 CSP 违规：

```bash
python fix_csp_violations.py
```

## 🔧 手动修复常见情况

### 1. 内联脚本
**修复前：**
```html
<script>
    console.log('Hello');
</script>
```

**修复后：**
```html
<script nonce="{{ csp_nonce }}">
    console.log('Hello');
</script>
```

### 2. 内联样式
**修复前：**
```html
<style>
    .custom { color: red; }
</style>
```

**修复后：**
```html
<style nonce="{{ csp_nonce }}">
    .custom { color: red; }
</style>
```

### 3. 事件处理器
**修复前：**
```html
<button onclick="doSomething()">Click</button>
```

**修复后：**
```html
<button id="myButton">Click</button>
<script nonce="{{ csp_nonce }}">
    document.getElementById('myButton').addEventListener('click', doSomething);
</script>
```

### 4. 动态脚本创建
**修复前：**
```javascript
const script = document.createElement('script');
script.textContent = 'console.log("test")';
document.head.appendChild(script);
```

**修复后：**
```javascript
const script = createScriptWithNonce(null, 'console.log("test")');
document.head.appendChild(script);
```

## 🛠️ 使用 CSP 辅助工具

加载 CSP 辅助脚本后，可以使用以下函数：

- `createScriptWithNonce(src, content)` - 创建带 nonce 的脚本
- `createStyleWithNonce(content)` - 创建带 nonce 的样式
- `safeEval(code)` - 安全执行代码

## 📋 检查清单

- [ ] 所有 `<script>` 标签都有 nonce 或 src
- [ ] 所有 `<style>` 标签都有 nonce
- [ ] 移除所有内联事件处理器（onclick 等）
- [ ] 动态创建的脚本使用辅助函数
- [ ] 测试页面功能正常

## 🔍 验证修复

在浏览器控制台检查是否还有 CSP 错误：
1. 打开开发者工具
2. 查看 Console 面板
3. 刷新页面
4. 确认没有 CSP 违规错误
