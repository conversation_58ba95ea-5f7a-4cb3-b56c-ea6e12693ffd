#!/usr/bin/env python3
"""
CSP 修复验证脚本
快速验证 CSP 违规修复效果
"""

import requests
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def verify_csp_fixes():
    """验证 CSP 修复效果"""
    print("🔍 验证 CSP 修复效果...")
    print("=" * 50)
    
    # 测试服务器连接
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code != 200:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
        print("✅ 服务器连接正常")
    except Exception as e:
        print(f"❌ 无法连接服务器: {e}")
        print("💡 请确保服务器正在运行: python app.py")
        return False
    
    # 设置浏览器选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)
        
        print("\n🌐 测试主要页面...")
        
        # 测试页面列表
        test_pages = [
            ('主页', 'http://localhost:5000'),
            ('仪表盘', 'http://localhost:5000/dashboard'),
            ('食材管理', 'http://localhost:5000/ingredient'),
            ('菜谱管理', 'http://localhost:5000/recipe'),
            ('入库管理', 'http://localhost:5000/stock-in'),
        ]
        
        total_csp_errors = 0
        total_pages_tested = 0
        
        for page_name, url in test_pages:
            try:
                print(f"\n📄 测试 {page_name} ({url})...")
                driver.get(url)
                
                # 等待页面加载
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                
                # 检查控制台错误
                logs = driver.get_log('browser')
                csp_errors = 0
                other_errors = 0
                
                for log in logs:
                    message = log['message'].lower()
                    if 'content security policy' in message or 'csp' in message:
                        csp_errors += 1
                        print(f"  🚨 CSP 错误: {log['message']}")
                    elif log['level'] == 'SEVERE':
                        other_errors += 1
                        print(f"  ⚠️ 其他错误: {log['message']}")
                
                if csp_errors == 0:
                    print(f"  ✅ {page_name} - 无 CSP 错误")
                else:
                    print(f"  ❌ {page_name} - 发现 {csp_errors} 个 CSP 错误")
                
                if other_errors > 0:
                    print(f"  ⚠️ {page_name} - 发现 {other_errors} 个其他错误")
                
                total_csp_errors += csp_errors
                total_pages_tested += 1
                
                # 检查 CSP 辅助脚本是否加载
                csp_helper_loaded = driver.execute_script("""
                    return typeof createScriptWithNonce !== 'undefined' && 
                           typeof createStyleWithNonce !== 'undefined';
                """)
                
                if csp_helper_loaded:
                    print(f"  ✅ CSP 辅助脚本已加载")
                else:
                    print(f"  ⚠️ CSP 辅助脚本未加载")
                
                time.sleep(1)  # 避免请求过快
                
            except Exception as e:
                print(f"  ❌ 测试 {page_name} 失败: {e}")
        
        # 生成报告
        print(f"\n📊 测试结果总结:")
        print(f"   测试页面数: {total_pages_tested}")
        print(f"   CSP 错误总数: {total_csp_errors}")
        
        if total_csp_errors == 0:
            print("🎉 恭喜！所有测试页面都没有 CSP 错误！")
            success = True
        elif total_csp_errors < 10:
            print("✅ 大部分 CSP 错误已修复，还有少量需要手动处理")
            success = True
        else:
            print("⚠️ 还有较多 CSP 错误需要进一步修复")
            success = False
        
        return success
        
    except Exception as e:
        print(f"❌ 浏览器测试失败: {e}")
        return False
        
    finally:
        if 'driver' in locals():
            driver.quit()

def check_template_fixes():
    """检查模板文件修复情况"""
    print("\n🔍 检查模板文件修复情况...")
    
    import glob
    import re
    
    # 统计修复情况
    html_files = glob.glob('app/templates/**/*.html', recursive=True)
    
    total_files = len(html_files)
    files_with_nonce = 0
    files_with_inline_events = 0
    
    for file_path in html_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有 nonce
            if 'nonce="{{ csp_nonce }}"' in content:
                files_with_nonce += 1
            
            # 检查是否还有内联事件
            event_attrs = ['onclick=', 'onload=', 'onchange=', 'onsubmit=', 'onerror=']
            if any(attr in content.lower() for attr in event_attrs):
                files_with_inline_events += 1
                
        except Exception:
            continue
    
    print(f"📊 模板文件统计:")
    print(f"   总文件数: {total_files}")
    print(f"   使用 nonce 的文件: {files_with_nonce}")
    print(f"   仍有内联事件的文件: {files_with_inline_events}")
    
    if files_with_nonce > total_files * 0.8:
        print("✅ 大部分文件已正确使用 nonce")
    else:
        print("⚠️ 还有文件需要添加 nonce")
    
    if files_with_inline_events > 0:
        print(f"⚠️ 还有 {files_with_inline_events} 个文件包含内联事件，建议手动迁移")
    else:
        print("✅ 所有内联事件已清理")

if __name__ == '__main__':
    print("🚀 CSP 修复验证工具")
    print("=" * 50)
    
    # 检查模板文件修复情况
    check_template_fixes()
    
    # 验证浏览器中的效果
    success = verify_csp_fixes()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 CSP 修复验证通过！")
        print("✅ 大部分 CSP 违规问题已解决")
        print("\n📝 后续建议:")
        print("1. 继续手动迁移剩余的内联事件处理器")
        print("2. 定期检查新添加的代码是否符合 CSP 规范")
        print("3. 使用 CSP 辅助脚本创建动态内容")
    else:
        print("⚠️ 还需要进一步修复")
        print("📖 请查看 'CSP修复指南.md' 了解详细修复方法")
    
    print("\n💡 调试提示:")
    print("- 在浏览器控制台查看具体的 CSP 错误")
    print("- 使用 createScriptWithNonce() 和 createStyleWithNonce() 创建动态内容")
    print("- 将内联事件处理器迁移到外部脚本文件")
