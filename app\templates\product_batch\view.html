{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">批次详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('product_batch.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h5><i class="icon fas fa-info"></i> 批次信息</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>批次名称：</strong>{{ batch.name }}</p>
                                        <p><strong>分类：</strong>{{ batch.category.name if batch.category else '' }}</p>
                                        <p><strong>供应商：</strong>{{ batch.supplier.name if batch.supplier else '' }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>状态：</strong>
                                            {% if batch.status == 'pending' %}
                                            <span class="badge badge-warning">待审核</span>
                                            {% elif batch.status == 'approved' %}
                                            <span class="badge badge-success">已审核</span>
                                            {% elif batch.status == 'shelved' %}
                                            <span class="badge badge-info">已上架</span>
                                            {% elif batch.status == 'rejected' %}
                                            <span class="badge badge-danger">已拒绝</span>
                                            {% endif %}
                                        </p>
                                        <p><strong>创建时间：</strong>{% if batch.created_at is defined and batch.created_at is not string and batch.created_at is not none %}{{ batch.created_at.strftime('%Y-%m-%d %H:%M:%S') }}{% else %}{{ batch.created_at }}{% endif %}</p>
                                        <p><strong>创建者：</strong>{{ batch.creator.real_name or batch.creator.username if batch.creator else '' }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">产品列表 ({{ products|length }})</h3>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>产品名称</th>
                                                    <th>价格(元)</th>
                                                    <th>规格</th>
                                                    <th>质量认证</th>
                                                    <th>状态</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for product in products %}
                                                <tr>
                                                    <td>{{ product.product_name or product.ingredient.name if product.ingredient else '' }}</td>
                                                    <td>{{ product.price }}</td>
                                                    <td>{{ product.specification }}</td>
                                                    <td>{{ product.quality_cert }}</td>
                                                    <td>
                                                        {% if product.shelf_status == 0 %}
                                                        <span class="badge badge-warning">待审核</span>
                                                        {% elif product.shelf_status == 1 %}
                                                            {% if product.is_available == 1 %}
                                                            <span class="badge badge-info">已上架</span>
                                                            {% else %}
                                                            <span class="badge badge-success">已审核</span>
                                                            {% endif %}
                                                        {% elif product.shelf_status == 2 %}
                                                        <span class="badge badge-danger">已拒绝</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <a href="{{ url_for('supplier_product.view', id=product.id) }}" class="btn btn-info btn-sm">
                                                            <i class="fas fa-eye"></i> 查看
                                                        </a>
                                                    </td>
                                                </tr>
                                                {% else %}
                                                <tr>
                                                    <td colspan="6" class="text-center">暂无产品</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-3">
                                {% if batch.status == 'pending' and current_user.is_admin() %}
                                <a href="{{ url_for('product_batch.approve', id=batch.id) }}" class="btn btn-success">
                                    <i class="fas fa-check"></i> 审核批次
                                </a>
                                {% endif %}

                                {% if batch.status == 'approved' %}
                                <button type="button" class="btn btn-primary shelf-batch" data-id="{{ batch.id }}">
                                    <i class="fas fa-upload"></i> 上架批次
                                </button>
                                {% endif %}

                                {% if batch.status == 'shelved' %}
                                <button type="button" class="btn btn-warning unshelf-batch" data-id="{{ batch.id }}">
                                    <i class="fas fa-download"></i> 下架批次
                                </button>
                                {% endif %}

                                {% if batch.status != 'shelved' %}
                                <button type="button" class="btn btn-danger delete-batch" data-id="{{ batch.id }}">
                                    <i class="fas fa-trash"></i> 删除批次
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 上架批次
    $('.shelf-batch').click(function() {
        var batchId = $(this).data('id');
        if (confirm('确定要上架此批次吗？')) {
            $.ajax({
                url: '/product-batch/' + batchId + '/shelf',
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert('操作失败，请重试');
                }
            });
        }
    });

    // 下架批次
    $('.unshelf-batch').click(function() {
        var batchId = $(this).data('id');
        if (confirm('确定要下架此批次吗？')) {
            $.ajax({
                url: '/product-batch/' + batchId + '/unshelf',
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert('操作失败，请重试');
                }
            });
        }
    });

    // 删除批次
    $('.delete-batch').click(function() {
        var batchId = $(this).data('id');
        if (confirm('确定要删除此批次吗？此操作将删除批次及其所有产品，且不可恢复！')) {
            $.ajax({
                url: '/product-batch/' + batchId + '/delete',
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        window.location.href = '/product-batch/';
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert('操作失败，请重试');
                }
            });
        }
    });
});
</script>
{% endblock %}
