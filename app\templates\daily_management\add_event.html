{% extends 'base.html' %}

{% block title %}添加特殊事件{% endblock %}

{% block styles %}
<style>
    .photo-preview {
        max-width: 100%;
        max-height: 200px;
        margin-top: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">添加特殊事件 - {{ log.log_date }}</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">事件信息</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="event_type">事件类型 <span class="text-danger">*</span></label>
                            <select class="form-control" id="event_type" name="event_type" required>
                                <option value="">请选择事件类型</option>
                                <option value="突发事件">突发事件</option>
                                <option value="参观访问">参观访问</option>
                                <option value="检查督导">检查督导</option>
                                <option value="食品安全培训">食品安全培训</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="event_date">事件日期 <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="event_date" name="event_date" value="{{ log.log_date }}" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="event_time">事件时间 <span class="text-danger">*</span></label>
                            <input type="time" class="form-control" id="event_time" name="event_time" value="09:00" required>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">事件描述 <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                </div>

                <div class="form-group">
                    <label for="participants">参与人员</label>
                    <textarea class="form-control" id="participants" name="participants" rows="2"></textarea>
                    <small class="form-text text-muted">多个人员请用逗号分隔</small>
                </div>

                <div class="form-group">
                    <label for="handling_measures">处理措施</label>
                    <textarea class="form-control" id="handling_measures" name="handling_measures" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <label for="event_summary">事件总结</label>
                    <textarea class="form-control" id="event_summary" name="event_summary" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <label for="photos">照片上传</label>
                    <input type="file" class="form-control-file" id="photos" name="photos" multiple accept="image/*">
                    <small class="form-text text-muted">可以选择多张照片上传，支持jpg、jpeg、png格式</small>
                    <div id="photo-previews" class="mt-2 d-flex flex-wrap"></div>
                </div>

                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <a href="{{ url_for('daily_management.events', log_id=log.id) }}" class="btn btn-secondary">取消</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 照片预览
    document.getElementById('photos').addEventListener('change', function(e) {
        const previewsDiv = document.getElementById('photo-previews');
        previewsDiv.innerHTML = '';

        for (const file of this.files) {
            const reader = new FileReader();
            reader.onload = function(event) {
                const img = document.createElement('img');
                img.src = event.target.result;
                img.className = 'photo-preview mr-2 mb-2';
                previewsDiv.appendChild(img);
            }
            reader.readAsDataURL(file);
        }
    });
</script>
{% endblock %}
