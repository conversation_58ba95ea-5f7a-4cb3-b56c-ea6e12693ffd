{% extends 'base.html' %}

{% block title %}查看菜单计划{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">菜单计划详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                        <a href="{{ url_for('menu_plan.print_menu', id=menu_plan.id) }}" class="btn btn-secondary btn-sm" target="_blank">
                            <i class="fas fa-print"></i> 打印菜单
                        </a>
                        {% if menu_plan.status == '计划中' %}
                        <a href="{{ url_for('menu_plan.edit', id=menu_plan.id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> 编辑
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 30%">ID</th>
                                    <td>{{ menu_plan.id }}</td>
                                </tr>
                                <tr>
                                    <th>区域</th>
                                    <td>{{ menu_plan.area.name }}</td>
                                </tr>
                                <tr>
                                    <th>计划日期</th>
                                    <td>{{ menu_plan.plan_date }}</td>
                                </tr>
                                <tr>
                                    <th>餐次</th>
                                    <td>{{ menu_plan.meal_type }}</td>
                                </tr>
                                <tr>
                                    <th>预计用餐人数</th>
                                    <td>{{ menu_plan.expected_diners or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>实际用餐人数</th>
                                    <td>{{ menu_plan.actual_diners or '-' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 30%">状态</th>
                                    <td>
                                        {% if menu_plan.status == '计划中' %}
                                        <span class="badge badge-warning">计划中</span>
                                        {% elif menu_plan.status == '已发布' %}
                                        <span class="badge badge-info">已发布</span>
                                        {% elif menu_plan.status == '已执行' %}
                                        <span class="badge badge-success">已执行</span>
                                        {% elif menu_plan.status == '已取消' %}
                                        <span class="badge badge-danger">已取消</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>创建人</th>
                                    <td>{{ menu_plan.creator.real_name or menu_plan.creator.username }}</td>
                                </tr>
                                <tr>
                                    <th>审批人</th>
                                    <td>{{ menu_plan.approver.real_name or menu_plan.approver.username if menu_plan.approver else '-' }}</td>
                                </tr>
                                <tr>
                                    <th>创建时间</th>
                                    <td>{{ menu_plan.created_at }}</td>
                                </tr>
                                <tr>
                                    <th>更新时间</th>
                                    <td>{{ menu_plan.updated_at }}</td>
                                </tr>
                                <tr>
                                    <th>备注</th>
                                    <td>{{ menu_plan.notes or '-' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- 菜单食谱列表 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">菜单食谱列表</h4>
                            <div class="card-tools">
                                <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#editRecipesModal">
                                    <i class="fas fa-edit"></i> 编辑菜品
                                </button>
                                <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#addRecipeModal">
                                    <i class="fas fa-plus"></i> 添加菜品
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>食谱名称</th>
                                            <th>计划数量</th>
                                            <th>实际数量</th>
                                            <th>备注</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for menu_recipe in menu_recipes %}
                                        <tr>
                                            <td>{{ menu_recipe.recipe.name }}</td>
                                            <td>{{ menu_recipe.planned_quantity }}</td>
                                            <td>{{ menu_recipe.actual_quantity or '-' }}</td>
                                            <td>{{ menu_recipe.notes or '-' }}</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-primary btn-sm edit-recipe-btn"
                                                            data-id="{{ menu_recipe.id }}"
                                                            data-recipe-id="{{ menu_recipe.recipe_id }}"
                                                            data-recipe-name="{{ menu_recipe.recipe.name }}"
                                                            data-planned-quantity="{{ menu_recipe.planned_quantity }}"
                                                            data-actual-quantity="{{ menu_recipe.actual_quantity or '' }}"
                                                            data-notes="{{ menu_recipe.notes or '' }}">
                                                        <i class="fas fa-edit"></i> 编辑
                                                    </button>
                                                    <button type="button" class="btn btn-danger btn-sm delete-recipe-btn" data-id="{{ menu_recipe.id }}">
                                                        <i class="fas fa-trash"></i> 删除
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="5" class="text-center">暂无食谱</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 留样记录列表 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">留样记录列表</h4>
                            <div class="card-tools">
                                <a href="{{ url_for('food_sample.create') }}?menu_plan_id={{ menu_plan.id }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> 添加留样记录
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>留样编号</th>
                                            <th>食谱名称</th>
                                            <th>留样图片</th>
                                            <th>留样时间</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for food_sample in food_samples %}
                                        <tr>
                                            <td>{{ food_sample.sample_number }}</td>
                                            <td>{{ food_sample.recipe.name }}</td>
                                            <td>
                                                {% if food_sample.sample_image %}
                                                <a href="{{ url_for('static', filename=food_sample.sample_image) }}" target="_blank">
                                                    <img src="{{ url_for('static', filename=food_sample.sample_image) }}" alt="留样图片" style="max-height: 50px;">
                                                </a>
                                                {% else %}
                                                无图片
                                                {% endif %}
                                            </td>
                                            <td>{{  food_sample.start_time|format_datetime('%Y-%m-%d %H:%M')  }}</td>
                                            <td>
                                                {% if food_sample.status == '已留样' %}
                                                <span class="badge badge-info">已留样</span>
                                                {% elif food_sample.status == '已销毁' %}
                                                <span class="badge badge-secondary">已销毁</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('food_sample.view', id=food_sample.id) }}" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i> 查看
                                                </a>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="6" class="text-center">暂无留样记录</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="row mt-4">
                        <div class="col-12 text-center">
                            {% if menu_plan.status == '计划中' %}
                            <button type="button" class="btn btn-info" data-toggle="modal" data-target="#approveModal">
                                <i class="fas fa-check"></i> 审核发布
                            </button>
                            <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#cancelModal">
                                <i class="fas fa-times"></i> 取消计划
                            </button>
                            {% elif menu_plan.status == '已发布' %}
                            <button type="button" class="btn btn-success" data-toggle="modal" data-target="#executeModal">
                                <i class="fas fa-play"></i> 执行计划
                            </button>
                            <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#cancelModal">
                                <i class="fas fa-times"></i> 取消计划
                            </button>
                            {% if not menu_plan.consumption_plans.first() %}
                            <a href="{{ url_for('consumption_plan.create', menu_plan_id=menu_plan.id) }}" class="btn btn-primary">
                                <i class="fas fa-list-alt"></i> 创建消耗计划
                            </a>
                            {% else %}
                            <a href="{{ url_for('consumption_plan.view', id=menu_plan.consumption_plans.first().id) }}" class="btn btn-primary">
                                <i class="fas fa-list-alt"></i> 查看消耗计划
                            </a>
                            {% endif %}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 审核确认模态框 -->
<div class="modal fade" id="approveModal" tabindex="-1" role="dialog" aria-labelledby="approveModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approveModalLabel">确认审核</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                确定要审核并发布该菜单计划吗？
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form action="{{ url_for('menu_plan.approve', id=menu_plan.id) }}" method="post"><button type="submit" class="btn btn-info">确认审核</button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
            </div>
        </div>
    </div>
</div>

<!-- 取消确认模态框 -->
<div class="modal fade" id="cancelModal" tabindex="-1" role="dialog" aria-labelledby="cancelModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cancelModalLabel">确认取消</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                确定要取消该菜单计划吗？此操作不可撤销。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form action="{{ url_for('menu_plan.cancel', id=menu_plan.id) }}" method="post"><button type="submit" class="btn btn-danger">确认取消</button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
            </div>
        </div>
    </div>
</div>

<!-- 执行确认模态框 -->
<div class="modal fade" id="executeModal" tabindex="-1" role="dialog" aria-labelledby="executeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="executeModalLabel">执行菜单计划</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ url_for('menu_plan.execute', id=menu_plan.id) }}" method="post"><div class="modal-body">
                    <div class="form-group">
                        <label for="actual_diners">实际用餐人数</label>
                        <input type="number" name="actual_diners" id="actual_diners" class="form-control" min="1" value="{{ menu_plan.expected_diners }}">
                    </div>

                    <h5>食谱实际数量</h5>
                    {% for menu_recipe in menu_recipes %}
                    <div class="form-group">
                        <label for="actual_quantity_{{ menu_recipe.id }}">{{ menu_recipe.recipe.name }}</label>
                        <input type="number" name="actual_quantity_{{ menu_recipe.id }}" id="actual_quantity_{{ menu_recipe.id }}" class="form-control" min="0" step="0.1" value="{{ menu_recipe.planned_quantity }}">
                    </div>
                    {% endfor %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success">确认执行</button>
                </div>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
        </div>
    </div>
</div>

<!-- 添加菜品模态框 -->
<div class="modal fade" id="addRecipeModal" tabindex="-1" role="dialog" aria-labelledby="addRecipeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRecipeModalLabel">添加菜品</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addRecipeForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="add_recipe_id">选择食谱 <span class="text-danger">*</span></label>
                        <select id="add_recipe_id" class="form-control select2" required>
                            <option value="">请选择食谱</option>
                            {% for recipe in recipes %}
                            <option value="{{ recipe.id }}">{{ recipe.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="add_planned_quantity">计划数量 <span class="text-danger">*</span></label>
                        <input type="number" id="add_planned_quantity" class="form-control" min="0.1" step="0.1" value="1" required>
                    </div>
                    {% if menu_plan.status == '已执行' %}
                    <div class="form-group">
                        <label for="add_actual_quantity">实际数量</label>
                        <input type="number" id="add_actual_quantity" class="form-control" min="0" step="0.1" value="1">
                    </div>
                    {% endif %}
                    <div class="form-group">
                        <label for="add_notes">备注</label>
                        <textarea id="add_notes" class="form-control" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success">添加</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑菜品模态框 -->
<div class="modal fade" id="editRecipeModal" tabindex="-1" role="dialog" aria-labelledby="editRecipeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editRecipeModalLabel">编辑菜品</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="editRecipeForm">
                <input type="hidden" id="edit_recipe_id">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="edit_recipe_name">食谱名称</label>
                        <input type="text" id="edit_recipe_name" class="form-control" readonly>
                    </div>
                    <div class="form-group">
                        <label for="edit_planned_quantity">计划数量 <span class="text-danger">*</span></label>
                        <input type="number" id="edit_planned_quantity" class="form-control" min="0.1" step="0.1" required>
                    </div>
                    {% if menu_plan.status == '已执行' %}
                    <div class="form-group">
                        <label for="edit_actual_quantity">实际数量</label>
                        <input type="number" id="edit_actual_quantity" class="form-control" min="0" step="0.1">
                    </div>
                    {% endif %}
                    <div class="form-group">
                        <label for="edit_notes">备注</label>
                        <textarea id="edit_notes" class="form-control" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 批量编辑菜品模态框 -->
<div class="modal fade" id="editRecipesModal" tabindex="-1" role="dialog" aria-labelledby="editRecipesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editRecipesModalLabel">批量编辑菜品</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="editRecipesForm">
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>食谱名称</th>
                                    <th>计划数量</th>
                                    {% if menu_plan.status == '已执行' %}
                                    <th>实际数量</th>
                                    {% endif %}
                                    <th>备注</th>
                                </tr>
                            </thead>
                            <tbody id="editRecipesTableBody">
                                {% for menu_recipe in menu_recipes %}
                                <tr data-id="{{ menu_recipe.id }}">
                                    <td>{{ menu_recipe.recipe.name }}</td>
                                    <td>
                                        <input type="number" name="planned_quantity_{{ menu_recipe.id }}" class="form-control form-control-sm" min="0.1" step="0.1" value="{{ menu_recipe.planned_quantity }}" required>
                                    </td>
                                    {% if menu_plan.status == '已执行' %}
                                    <td>
                                        <input type="number" name="actual_quantity_{{ menu_recipe.id }}" class="form-control form-control-sm" min="0" step="0.1" value="{{ menu_recipe.actual_quantity or '' }}">
                                    </td>
                                    {% endif %}
                                    <td>
                                        <input type="text" name="notes_{{ menu_recipe.id }}" class="form-control form-control-sm" value="{{ menu_recipe.notes or '' }}">
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存所有更改</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteRecipeModal" tabindex="-1" role="dialog" aria-labelledby="deleteRecipeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteRecipeModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                确定要删除这个菜品吗？此操作不可撤销。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<link href="{{ url_for('static', filename='vendor/select2/css/select2.min.css') }}" rel="stylesheet" />
<link href="{{ url_for('static', filename='vendor/select2/css/select2-bootstrap4.min.css') }}" rel="stylesheet" />
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/select2/select2.min.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 初始化Select2
        $('.select2').select2({
            theme: 'bootstrap4'
        });

        // 编辑菜品按钮点击事件
        $('.edit-recipe-btn').click(function() {
            var id = $(this).data('id');
            var recipeName = $(this).data('recipe-name');
            var plannedQuantity = $(this).data('planned-quantity');
            var actualQuantity = $(this).data('actual-quantity');
            var notes = $(this).data('notes');

            $('#edit_recipe_id').val(id);
            $('#edit_recipe_name').val(recipeName);
            $('#edit_planned_quantity').val(plannedQuantity);
            if ($('#edit_actual_quantity').length) {
                $('#edit_actual_quantity').val(actualQuantity);
            }
            $('#edit_notes').val(notes);

            $('#editRecipeModal').modal('show');
        });

        // 删除菜品按钮点击事件
        $('.delete-recipe-btn').click(function() {
            var id = $(this).data('id');
            $('#confirmDeleteBtn').data('id', id);
            $('#deleteRecipeModal').modal('show');
        });

        // 确认删除按钮点击事件
        $('#confirmDeleteBtn').click(function() {
            var id = $(this).data('id');

            // 发送AJAX请求删除菜品
            $.ajax({
                url: '{{ url_for("menu_plan.delete_recipe", menu_plan_id=menu_plan.id) }}',
                type: 'POST',
                data: {
                    recipe_id: id
                },
                success: function(response) {
                    if (response.success) {
                        // 刷新页面
                        location.reload();
                    } else {
                        alert('删除失败: ' + response.message);
                    }
                },
                error: function() {
                    alert('删除请求失败，请重试');
                }
            });

            $('#deleteRecipeModal').modal('hide');
        });

        // 添加菜品表单提交事件
        $('#addRecipeForm').submit(function(e) {
            e.preventDefault();

            var recipeId = $('#add_recipe_id').val();
            var plannedQuantity = $('#add_planned_quantity').val();
            var actualQuantity = $('#add_actual_quantity').length ? $('#add_actual_quantity').val() : '';
            var notes = $('#add_notes').val();

            // 验证表单
            if (!recipeId || !plannedQuantity) {
                alert('请填写所有必填字段');
                return;
            }

            // 发送AJAX请求添加菜品
            $.ajax({
                url: '{{ url_for("menu_plan.add_recipe", menu_plan_id=menu_plan.id) }}',
                type: 'POST',
                data: {
                    recipe_id: recipeId,
                    planned_quantity: plannedQuantity,
                    actual_quantity: actualQuantity,
                    notes: notes
                },
                success: function(response) {
                    if (response.success) {
                        // 刷新页面
                        location.reload();
                    } else {
                        alert('添加失败: ' + response.message);
                    }
                },
                error: function() {
                    alert('添加请求失败，请重试');
                }
            });

            $('#addRecipeModal').modal('hide');
        });

        // 编辑菜品表单提交事件
        $('#editRecipeForm').submit(function(e) {
            e.preventDefault();

            var id = $('#edit_recipe_id').val();
            var plannedQuantity = $('#edit_planned_quantity').val();
            var actualQuantity = $('#edit_actual_quantity').length ? $('#edit_actual_quantity').val() : '';
            var notes = $('#edit_notes').val();

            // 验证表单
            if (!plannedQuantity) {
                alert('请填写所有必填字段');
                return;
            }

            // 发送AJAX请求更新菜品
            $.ajax({
                url: '{{ url_for("menu_plan.update_recipe", menu_plan_id=menu_plan.id) }}',
                type: 'POST',
                data: {
                    recipe_id: id,
                    planned_quantity: plannedQuantity,
                    actual_quantity: actualQuantity,
                    notes: notes
                },
                success: function(response) {
                    if (response.success) {
                        // 刷新页面
                        location.reload();
                    } else {
                        alert('更新失败: ' + response.message);
                    }
                },
                error: function() {
                    alert('更新请求失败，请重试');
                }
            });

            $('#editRecipeModal').modal('hide');
        });

        // 批量编辑菜品表单提交事件
        $('#editRecipesForm').submit(function(e) {
            e.preventDefault();

            var formData = {};

            // 收集所有菜品的数据
            $('#editRecipesTableBody tr').each(function() {
                var id = $(this).data('id');
                var plannedQuantity = $('input[name="planned_quantity_' + id + '"]').val();
                var actualQuantity = $('input[name="actual_quantity_' + id + '"]').length ? $('input[name="actual_quantity_' + id + '"]').val() : '';
                var notes = $('input[name="notes_' + id + '"]').val();

                formData['recipe_' + id] = {
                    id: id,
                    planned_quantity: plannedQuantity,
                    actual_quantity: actualQuantity,
                    notes: notes
                };
            });

            // 发送AJAX请求批量更新菜品
            $.ajax({
                url: '{{ url_for("menu_plan.update_recipes", menu_plan_id=menu_plan.id) }}',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                success: function(response) {
                    if (response.success) {
                        // 刷新页面
                        location.reload();
                    } else {
                        alert('更新失败: ' + response.message);
                    }
                },
                error: function() {
                    alert('更新请求失败，请重试');
                }
            });

            $('#editRecipesModal').modal('hide');
        });
    });
</script>
{% endblock %}
