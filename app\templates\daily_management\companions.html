{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/datatables/css/dataTables.bootstrap4.min.css') }}">
<style>
    .action-buttons .btn {
        margin-right: 5px;
    }

    .rating-stars {
        color: #f6c23e;
    }

    .meal-badge {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .meal-breakfast {
        background-color: #4e73df;
        color: white;
    }

    .meal-lunch {
        background-color: #1cc88a;
        color: white;
    }

    .meal-dinner {
        background-color: #f6c23e;
        color: white;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter {
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 导入导航宏 -->
    {% from 'daily_management/components/navigation.html' import daily_management_header %}

    <!-- 显示导航和学校信息 -->
    {{ daily_management_header(title, school, log, 'companions') }}

    <!-- 功能按钮 -->
    <div class="mb-4">
        <a href="{{ url_for('daily_management.add_companion', log_id=log.id) }}" class="btn btn-primary">
            <i class="fas fa-plus mr-1"></i> 添加陪餐记录
        </a>
        <a href="{{ url_for('daily_management.generate_companion_qrcode_view', log_id=log.id) }}" class="btn btn-info">
            <i class="fas fa-qrcode mr-1"></i> 生成二维码
        </a>
        <a href="{{ url_for('daily_management.school_qrcode') }}" class="btn btn-warning">
            <i class="fas fa-qrcode mr-1"></i> 学校固定二维码
        </a>
        <a href="{{ url_for('daily_management.print_companion_by_id', log_id=log.id) }}" class="btn btn-success">
            <i class="fas fa-print mr-1"></i> 打印陪餐记录
        </a>
        <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-1"></i> 返回日志
        </a>
    </div>

    <!-- 陪餐记录列表 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">陪餐记录列表</h6>
        </div>
        <div class="card-body">
            {% if companions %}
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>陪餐人</th>
                            <th>角色</th>
                            <th>餐次</th>
                            <th>陪餐时间</th>
                            <th>口味评分</th>
                            <th>卫生评分</th>
                            <th>服务评分</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for companion in companions %}
                        <tr>
                            <td>{{ companion.companion_name }}</td>
                            <td>{{ companion.companion_role }}</td>
                            <td>
                                {% if companion.meal_type == 'breakfast' %}
                                <span class="meal-badge meal-breakfast">早餐</span>
                                {% elif companion.meal_type == 'lunch' %}
                                <span class="meal-badge meal-lunch">午餐</span>
                                {% elif companion.meal_type == 'dinner' %}
                                <span class="meal-badge meal-dinner">晚餐</span>
                                {% endif %}
                            </td>
                            <td>{{ companion.dining_time }}</td>
                            <td>
                                {% if companion.taste_rating %}
                                <span class="rating-stars">
                                    {% for i in range(companion.taste_rating) %}★{% endfor %}
                                    {% for i in range(5 - companion.taste_rating) %}☆{% endfor %}
                                </span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if companion.hygiene_rating %}
                                <span class="rating-stars">
                                    {% for i in range(companion.hygiene_rating) %}★{% endfor %}
                                    {% for i in range(5 - companion.hygiene_rating) %}☆{% endfor %}
                                </span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if companion.service_rating %}
                                <span class="rating-stars">
                                    {% for i in range(companion.service_rating) %}★{% endfor %}
                                    {% for i in range(5 - companion.service_rating) %}☆{% endfor %}
                                </span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('daily_management.view_companion', companion_id=companion.id) }}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i> 查看
                                </a>
                                <a href="{{ url_for('daily_management.edit_companion', companion_id=companion.id) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-edit"></i> 编辑
                                </a>
                                <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#deleteModal{{ companion.id }}">
                                    <i class="fas fa-trash"></i> 删除
                                </button>

                                <!-- 删除确认模态框 -->
                                <div class="modal fade" id="deleteModal{{ companion.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                确定要删除 {{ companion.companion_name }} 的陪餐记录吗？此操作不可恢复。
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                                                <form action="{{ url_for('daily_management.delete_companion', companion_id=companion.id) }}" method="post">
                                                    {{ form.csrf_token }}
                                                    <button type="submit" class="btn btn-danger">确认删除</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4">
                <p class="text-muted">暂无陪餐记录</p>
                <a href="{{ url_for('daily_management.add_companion', log_id=log.id) }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 添加陪餐记录
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/datatables/js/jquery.dataTables.min.js') }}"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/datatables/js/dataTables.bootstrap4.min.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "language": {
                "url": "{{ url_for('static', filename='vendor/datatables/js/Chinese.json') }}"
            },
            "order": [[3, "desc"]]  // 按陪餐时间降序排序
        });
    });
</script>
{% endblock %}
