{% extends 'base.html' %}

{% block title %}区域管理 - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>区域管理</h2>
        {% if current_area %}
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb bg-light">
                {% for area in area_path %}
                <li class="breadcrumb-item {% if loop.last %}active{% endif %}">
                    {% if not loop.last %}
                    <a href="{{ url_for('area.view_area', id=area.id) }}">{{ area.name }}</a>
                    {% else %}
                    {{ area.name }}
                    {% endif %}
                </li>
                {% endfor %}
            </ol>
        </nav>
        {% endif %}
    </div>
    <div class="col-md-4 text-right">
        {% if current_user.is_admin() %}
        <a href="{{ url_for('area.add_area') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加区域
        </a>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">区域树形结构</h5>
            </div>
            <div class="card-body">
                <div id="area-tree" class="area-tree">
                    <ul class="list-group list-group-flush">
                        {% for area in top_level_areas %}
                        <li class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <a href="{{ url_for('area.view_area', id=area.id) }}">
                                    {{ area.name }}
                                    <span class="badge badge-info">{{ area.get_level_name() }}</span>
                                </a>
                                {% if area.children %}
                                <button class="btn btn-sm btn-link" type="button" data-toggle="collapse" data-target="#area-{{ area.id }}">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                                {% endif %}
                            </div>
                            {% if area.children %}
                            <div class="collapse" id="area-{{ area.id }}">
                                <ul class="list-group list-group-flush mt-2">
                                    {% for child in area.children %}
                                    <li class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <a href="{{ url_for('area.view_area', id=child.id) }}" class="ml-3">
                                                {{ child.name }}
                                                <span class="badge badge-info">{{ child.get_level_name() }}</span>
                                            </a>
                                            {% if child.children %}
                                            <button class="btn btn-sm btn-link" type="button" data-toggle="collapse" data-target="#area-{{ child.id }}">
                                                <i class="fas fa-chevron-down"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                        {% if child.children %}
                                        <div class="collapse" id="area-{{ child.id }}">
                                            <ul class="list-group list-group-flush mt-2">
                                                {% for grandchild in child.children %}
                                                <li class="list-group-item">
                                                    <a href="{{ url_for('area.view_area', id=grandchild.id) }}" class="ml-5">
                                                        {{ grandchild.name }}
                                                        <span class="badge badge-info">{{ grandchild.get_level_name() }}</span>
                                                        {% if grandchild.level == 3 and grandchild.is_township_school %}
                                                        <span class="badge badge-primary">乡镇学校</span>
                                                        {% endif %}
                                                    </a>
                                                </li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                        {% endif %}
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}
                        </li>
                        {% else %}
                        <li class="list-group-item text-center">
                            <span class="text-muted">暂无区域数据</span>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        {% if current_area %}
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">当前区域: {{ current_area.name }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <th>区域名称</th>
                                <td>{{ current_area.name }}</td>
                            </tr>
                            <tr>
                                <th>区域代码</th>
                                <td>{{ current_area.code }}</td>
                            </tr>
                            <tr>
                                <th>区域级别</th>
                                <td>{{ current_area.get_level_name() }}</td>
                            </tr>
                            <tr>
                                <th>上级区域</th>
                                <td>
                                    {% if current_area.parent %}
                                    <a href="{{ url_for('area.view_area', id=current_area.parent.id) }}">
                                        {{ current_area.parent.name }}
                                    </a>
                                    {% else %}
                                    <span class="text-muted">无</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>描述</th>
                                <td>{{ current_area.description or '无' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">区域统计</h5>
                                <p class="card-text">
                                    <strong>下级区域数:</strong> {{  current_area.children|length  }}<br>
                                    <strong>用户数:</strong> {{ current_area.users.count() }}<br>
                                </p>
                                <div class="btn-group">
                                    <a href="{{ url_for('area.view_area', id=current_area.id) }}" class="btn btn-primary">
                                        <i class="fas fa-eye"></i> 查看详情
                                    </a>
                                    {% if current_user.is_admin() %}
                                    <a href="{{ url_for('area.edit_area', id=current_area.id) }}" class="btn btn-secondary">
                                        <i class="fas fa-edit"></i> 编辑
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% if current_area.children %}
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">下级区域</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>名称</th>
                                <th>代码</th>
                                <th>级别</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for child in current_area.children %}
                            <tr>
                                <td>
                                    {{ child.name }}
                                    {% if child.level == 3 and child.is_township_school %}
                                    <span class="badge badge-primary">乡镇学校</span>
                                    {% endif %}
                                </td>
                                <td>{{ child.code }}</td>
                                <td>{{ child.get_level_name() }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('area.view_area', id=child.id) }}" class="btn btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('area.switch_area', id=child.id) }}" class="btn btn-primary">
                                            <i class="fas fa-exchange-alt"></i> 切换
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
        {% else %}
        <div class="alert alert-info">
            <h4 class="alert-heading">欢迎使用区域管理!</h4>
            <p>请从左侧区域树中选择一个区域，或者点击"添加区域"按钮创建新区域。</p>
            <hr>
            <p class="mb-0">区域管理是系统的核心功能，用于管理县市区/乡镇/学校/食堂的层级结构。</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 自动展开当前区域的父级节点
    {% if current_area %}
    {% for area in area_path %}
    $('#area-{{ area.id }}').addClass('show');
    {% endfor %}
    {% endif %}
});
</script>
{% endblock %}
