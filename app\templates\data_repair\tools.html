{% extends "base.html" %}

{% block title %}数据修复工具{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">数据修复工具</h3>
          <div class="card-tools">
            <a href="{{ url_for('data_repair.index') }}" class="btn btn-sm btn-default">
              <i class="fas fa-arrow-left"></i> 返回
            </a>
          </div>
        </div>
        <div class="card-body">
          <p class="text-muted">使用专门的工具修复特定问题。</p>
          
          <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i> 警告：这些工具会直接修改数据库数据，请谨慎使用。建议在使用前备份数据库。
          </div>
          
          <div class="row">
            <div class="col-md-4">
              <div class="card">
                <div class="card-header bg-primary">
                  <h5 class="card-title mb-0 text-white">周菜单修复工具</h5>
                </div>
                <div class="card-body">
                  <p>修复周菜单相关的数据问题。</p>
                  <div class="form-group">
                    <label>选择操作：</label>
                    <select id="weeklyMenuOperation" class="form-control">
                      <option value="clean_orphaned">清理孤立的周菜单食谱记录</option>
                      <option value="sync_names">同步食谱名称</option>
                    </select>
                  </div>
                  <button id="runWeeklyMenuTool" class="btn btn-primary btn-block">
                    <i class="fas fa-play"></i> 运行工具
                  </button>
                </div>
              </div>
            </div>
            
            <div class="col-md-4">
              <div class="card">
                <div class="card-header bg-success">
                  <h5 class="card-title mb-0 text-white">食谱食材修复工具</h5>
                </div>
                <div class="card-body">
                  <p>修复食谱和食材关联的数据问题。</p>
                  <div class="form-group">
                    <label>选择操作：</label>
                    <select id="recipeIngredientsOperation" class="form-control">
                      <option value="clean_orphaned">清理孤立的食谱食材记录</option>
                    </select>
                  </div>
                  <button id="runRecipeIngredientsTool" class="btn btn-success btn-block">
                    <i class="fas fa-play"></i> 运行工具
                  </button>
                </div>
              </div>
            </div>
            
            <div class="col-md-4">
              <div class="card">
                <div class="card-header bg-warning">
                  <h5 class="card-title mb-0 text-white">采购订单修复工具</h5>
                </div>
                <div class="card-body">
                  <p>修复采购订单相关的数据问题。</p>
                  <div class="form-group">
                    <label>选择操作：</label>
                    <select id="purchaseOrdersOperation" class="form-control">
                      <option value="clean_orphaned_items">清理孤立的采购订单项</option>
                    </select>
                  </div>
                  <button id="runPurchaseOrdersTool" class="btn btn-warning btn-block">
                    <i class="fas fa-play"></i> 运行工具
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 结果区域 -->
          <div id="toolResultArea" class="mt-4" style="display: none;">
            <div class="card">
              <div class="card-header bg-info text-white">
                <h5 class="mb-0">工具执行结果</h5>
              </div>
              <div class="card-body">
                <pre id="toolResultContent" class="bg-light p-3 rounded"></pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
  $(document).ready(function() {
    // 运行周菜单修复工具
    $('#runWeeklyMenuTool').click(function() {
      const operation = $('#weeklyMenuOperation').val();
      runTool('fix_weekly_menu', { operation: operation }, '周菜单修复工具');
    });
    
    // 运行食谱食材修复工具
    $('#runRecipeIngredientsTool').click(function() {
      const operation = $('#recipeIngredientsOperation').val();
      runTool('fix_recipe_ingredients', { operation: operation }, '食谱食材修复工具');
    });
    
    // 运行采购订单修复工具
    $('#runPurchaseOrdersTool').click(function() {
      const operation = $('#purchaseOrdersOperation').val();
      runTool('fix_purchase_orders', { operation: operation }, '采购订单修复工具');
    });
    
    // 运行工具
    function runTool(tool, params, toolName) {
      if (!confirm(`确定要运行${toolName}吗？此操作可能会修改数据库数据。`)) {
        return;
      }
      
      const button = $(`#run${tool.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('')}Tool`);
      const originalText = button.html();
      
      button.prop('disabled', true);
      button.html('<i class="fas fa-spinner fa-spin"></i> 运行中...');
      
      $.ajax({
        url: '{{ url_for("data_repair.run_tool") }}',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
          tool: tool,
          params: params
        }),
        dataType: 'json',
        success: function(response) {
          if (response.success) {
            toastr.success(`${toolName}运行成功`);
            displayToolResult(response.data);
          } else {
            toastr.error(response.message || '工具运行失败');
          }
        },
        error: function(xhr, status, error) {
          toastr.error('请求失败: ' + error);
        },
        complete: function() {
          button.prop('disabled', false);
          button.html(originalText);
        }
      });
    }
    
    // 显示工具执行结果
    function displayToolResult(data) {
      $('#toolResultArea').show();
      $('#toolResultContent').text(JSON.stringify(data, null, 2));
    }
  });
</script>
{% endblock %}
