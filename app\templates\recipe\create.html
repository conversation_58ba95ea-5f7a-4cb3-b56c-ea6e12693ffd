{% extends 'base.html' %}

{% block title %}
{% if recipe %}编辑食谱{% else %}添加食谱{% endif %}
{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .user-defined-box {
        border: 2px solid #ffc107;
        padding: 15px;
        border-radius: 5px;
        background-color: #fff8e1;
        margin-bottom: 15px;
    }
    .ingredient-list {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
    }
    .ingredient-item {
        padding: 8px;
        margin-bottom: 5px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    .ingredient-item:hover {
        background-color: #f0f0f0;
    }
    .selected-ingredients {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        min-height: 200px;
    }
    .selected-ingredient {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 10px;
        position: relative;
    }
    .remove-ingredient {
        position: absolute;
        right: 10px;
        top: 10px;
        cursor: pointer;
        color: #dc3545;
    }
    .preview-image {
        max-height: 200px;
        max-width: 100%;
        margin-top: 10px;
    }
    .form-error {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    .ingredient-category-badge {
        display: inline-block;
        font-size: 0.75rem;
        padding: 0.2rem 0.5rem;
        margin-left: 0.5rem;
        border-radius: 10px;
        background-color: #e9ecef;
        color: #495057;
    }
    .ingredient-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% if recipe %}编辑食谱{% else %}添加食谱{% endif %}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('recipe.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" id="recipeForm">
                        {{ form.csrf_token }}

                        <!-- 基本信息 -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h4 class="mb-0">基本信息</h4>
                            </div>
                            <div class="card-body">
                                <!-- 用户自定义食谱选项 -->
                                <div class="user-defined-box">
                                    <div class="form-check">
                                        {{ form.is_user_defined(class="form-check-input") }}
                                        {{ form.is_user_defined.label(class="form-check-label") }}
                                    </div>
                                    <small class="form-text text-muted">用户自定义食谱将在菜单规划中优先显示</small>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            {{ form.name.label(class="form-label") }}
                                            {{ form.name(class="form-control") }}
                                            {% if form.name.errors %}
                                                <div class="form-error">{{ form.name.errors[0] }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="form-group">
                                            {{ form.category_id.label(class="form-label") }}
                                            {{ form.category_id(class="form-control") }}
                                            {% if form.category_id.errors %}
                                                <div class="form-error">{{ form.category_id.errors[0] }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="form-group">
                                            {{ form.meal_type.label(class="form-label") }}
                                            {{ form.meal_type(class="form-control") }}
                                            {% if form.meal_type.errors %}
                                                <div class="form-error">{{ form.meal_type.errors[0] }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            {{ form.main_image.label(class="form-label") }}
                                            {{ form.main_image(class="form-control") }}
                                            {% if form.main_image.errors %}
                                                <div class="form-error">{{ form.main_image.errors[0] }}</div>
                                            {% endif %}
                                            {% if recipe and recipe.main_image %}
                                                <div class="mt-2">
                                                    <img src="{{ url_for('static', filename=recipe.main_image) }}" alt="{{ recipe.name }}" class="preview-image" id="imagePreview">
                                                </div>
                                            {% else %}
                                                <div class="mt-2" id="imagePreviewContainer" style="display: none;">
                                                    <img src="" alt="预览" class="preview-image" id="imagePreview">
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="form-group">
                                            {{ form.status.label(class="form-label") }}
                                            {{ form.status(class="form-control") }}
                                            {% if form.status.errors %}
                                                <div class="form-error">{{ form.status.errors[0] }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            {{ form.description.label(class="form-label") }}
                                            {{ form.description(class="form-control", rows=3) }}
                                            {% if form.description.errors %}
                                                <div class="form-error">{{ form.description.errors[0] }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 食材配比 -->
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h4 class="mb-0">食材配比</h4>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <input type="text" id="ingredientSearch" class="form-control" placeholder="搜索食材...">
                                    </div>
                                    <div class="col-md-4">
                                        <select id="categoryFilter" class="form-control">
                                            <option value="">全部分类</option>
                                            <!-- 分类选项将通过AJAX加载 -->
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>可选食材</h5>
                                        <div class="ingredient-list" id="ingredientList">
                                            <!-- 食材列表将通过AJAX加载 -->
                                            <div class="text-center py-3">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="sr-only">加载中...</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>已选食材</h5>
                                        <div class="selected-ingredients" id="selectedIngredients">
                                            <!-- 已选食材将在这里显示 -->
                                            {% if recipe_ingredients %}
                                                {% for ri in recipe_ingredients %}
                                                <div class="selected-ingredient" data-id="{{ ri.ingredient_id }}">
                                                    <input type="hidden" name="ingredient_ids[]" value="{{ ri.ingredient_id }}">
                                                    <div class="row">
                                                        <div class="col-5">{{ ri.ingredient.name }}</div>
                                                        <div class="col-3">
                                                            <input type="number" name="ingredient_quantities[]" class="form-control form-control-sm" value="{{ ri.quantity }}" min="0.1" step="0.1">
                                                        </div>
                                                        <div class="col-3">
                                                            <input type="text" name="ingredient_units[]" class="form-control form-control-sm" value="{{ ri.unit }}">
                                                        </div>
                                                        <div class="col-1">
                                                            <span class="remove-ingredient"><i class="fas fa-times-circle"></i></span>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">保存</button>
                            <a href="{{ url_for('recipe.index') }}" class="btn btn-secondary">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 加载食材分类
        loadCategories();

        // 初始化食材选择器
        loadIngredients();

        // 图片预览
        $('#main_image').change(function() {
            if (this.files && this.files[0]) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    $('#imagePreview').attr('src', e.target.result);
                    $('#imagePreviewContainer').show();
                }
                reader.readAsDataURL(this.files[0]);
            }
        });

        // 搜索食材
        $('#ingredientSearch').on('input', function() {
            filterIngredients();
        });

        // 分类筛选
        $('#categoryFilter').on('change', function() {
            filterIngredients();
        });

        // 删除已选食材
        $(document).on('click', '.remove-ingredient', function() {
            var ingredient = $(this).closest('.selected-ingredient');
            var id = ingredient.data('id');
            ingredient.remove();

            // 重新启用可选列表中的对应食材
            $('#ingredient-' + id).removeClass('disabled').prop('disabled', false);
        });

        // 表单提交前验证
        $('#recipeForm').submit(function(e) {
            var isValid = true;

            // 检查必填字段
            if (!$('#name').val()) {
                isValid = false;
                $('#name').addClass('is-invalid');
            } else {
                $('#name').removeClass('is-invalid');
            }

            if (!isValid) {
                e.preventDefault();
                toastr.error('请填写所有必填字段');
            }
        });
    });

    // 加载食材分类
    function loadCategories() {
        $.ajax({
            url: '/ingredient-category/api',
            type: 'GET',
            success: function(data) {
                var categoryFilter = $('#categoryFilter');

                if (data.length === 0) {
                    return;
                }

                $.each(data, function(index, category) {
                    categoryFilter.append('<option value="' + category.id + '">' + category.name + '</option>');
                });
            },
            error: function() {
                console.error('加载食材分类失败');
            }
        });
    }

    // 加载食材列表
    function loadIngredients() {
        $.ajax({
            url: '/ingredient/api',
            type: 'GET',
            success: function(data) {
                var ingredientList = $('#ingredientList');
                ingredientList.empty();

                if (data.length === 0) {
                    ingredientList.html('<div class="alert alert-info">没有找到食材</div>');
                    return;
                }

                // 获取已选食材ID
                var selectedIds = [];
                $('.selected-ingredient').each(function() {
                    selectedIds.push($(this).data('id'));
                });

                // 存储所有食材数据，用于筛选
                window.allIngredients = data;

                // 获取分类名称映射
                var categoryNames = {};
                $('#categoryFilter option').each(function() {
                    var value = $(this).val();
                    if (value) {
                        categoryNames[value] = $(this).text();
                    }
                });

                $.each(data, function(index, ingredient) {
                    var isDisabled = selectedIds.includes(ingredient.id);
                    var categoryName = categoryNames[ingredient.category_id] || '未分类';

                    var item = $('<div class="ingredient-item' + (isDisabled ? ' disabled' : '') + '" id="ingredient-' + ingredient.id + '" data-id="' + ingredient.id + '" data-name="' + ingredient.name + '" data-unit="' + ingredient.unit + '" data-category-id="' + ingredient.category_id + '"' + (isDisabled ? ' disabled' : '') + '></div>');

                    // 添加食材名称和分类标签
                    var nameSpan = $('<span class="ingredient-name">' + ingredient.name + '</span>');
                    var categoryBadge = $('<span class="ingredient-category-badge">' + categoryName + '</span>');

                    item.append(nameSpan).append(categoryBadge);

                    if (!isDisabled) {
                        item.click(function() {
                            addIngredient(ingredient.id, ingredient.name, ingredient.unit);
                            $(this).addClass('disabled').prop('disabled', true);
                        });
                    }

                    ingredientList.append(item);
                });
            },
            error: function() {
                $('#ingredientList').html('<div class="alert alert-danger">加载食材失败</div>');
            }
        });
    }

    // 筛选食材
    function filterIngredients() {
        var searchTerm = $('#ingredientSearch').val().toLowerCase();
        var categoryId = $('#categoryFilter').val();

        $('.ingredient-item').each(function() {
            var ingredientName = $(this).find('.ingredient-name').text().toLowerCase();
            var ingredientCategoryId = $(this).data('category-id');

            // 同时满足搜索条件和分类条件
            var matchesSearch = ingredientName.indexOf(searchTerm) > -1;
            var matchesCategory = !categoryId || ingredientCategoryId == categoryId;

            if (matchesSearch && matchesCategory) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });

        // 显示筛选结果数量
        var visibleCount = $('.ingredient-item:visible').length;
        var totalCount = $('.ingredient-item').length;

        if (searchTerm || categoryId) {
            var resultText = '显示 ' + visibleCount + ' / ' + totalCount + ' 个食材';

            // 如果没有已存在的结果提示，则创建一个
            if ($('#filterResultInfo').length === 0) {
                $('#ingredientList').before('<div id="filterResultInfo" class="alert alert-info py-1 mb-2">' + resultText + '</div>');
            } else {
                $('#filterResultInfo').text(resultText).show();
            }
        } else {
            $('#filterResultInfo').hide();
        }
    }

    // 添加食材到已选列表
    function addIngredient(id, name, unit) {
        var html = `
            <div class="selected-ingredient" data-id="${id}">
                <input type="hidden" name="ingredient_ids[]" value="${id}">
                <div class="row">
                    <div class="col-5">${name}</div>
                    <div class="col-3">
                        <input type="number" name="ingredient_quantities[]" class="form-control form-control-sm" value="1" min="0.1" step="0.1">
                    </div>
                    <div class="col-3">
                        <input type="text" name="ingredient_units[]" class="form-control form-control-sm" value="${unit}">
                    </div>
                    <div class="col-1">
                        <span class="remove-ingredient"><i class="fas fa-times-circle"></i></span>
                    </div>
                </div>
            </div>
        `;
        $('#selectedIngredients').append(html);
    }
</script>
{% endblock %}
