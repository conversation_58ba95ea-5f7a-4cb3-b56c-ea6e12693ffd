{% extends 'base.html' %}

{% block title %}问题记录详情{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .photo-gallery {
        display: flex;
        flex-wrap: wrap;
        margin: -5px;
    }
    .photo-item {
        width: 200px;
        margin: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
    }
    .photo-item img {
        width: 100%;
        height: 150px;
        object-fit: cover;
    }
    .photo-caption {
        padding: 8px;
        background-color: #f8f9fc;
        font-size: 0.8rem;
    }
    .badge-pending {
        background-color: #f6c23e;
        color: #fff;
    }
    .badge-fixing {
        background-color: #4e73df;
        color: #fff;
    }
    .badge-fixed {
        background-color: #1cc88a;
        color: #fff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">问题记录详情</h1>
    
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">问题信息</h6>
            <div>
                <a href="{{ url_for('daily_management.edit_issue', issue_id=issue.id) }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-edit mr-1"></i> 编辑
                </a>
                <a href="{{ url_for('daily_management.issues', log_id=issue.daily_log_id) }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-left mr-1"></i> 返回列表
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 30%">问题类型</th>
                            <td>{{ issue.issue_type }}</td>
                        </tr>
                        <tr>
                            <th>发现时间</th>
                            <td>{{ issue.found_time|format_datetime }}</td>
                        </tr>
                        <tr>
                            <th>责任人</th>
                            <td>{{ issue.responsible_person or '-' }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 30%">状态</th>
                            <td>
                                {% if issue.status == 'pending' %}
                                <span class="badge badge-pending">待处理</span>
                                {% elif issue.status == 'fixing' %}
                                <span class="badge badge-fixing">处理中</span>
                                {% elif issue.status == 'fixed' %}
                                <span class="badge badge-fixed">已修复</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>修复时间</th>
                            <td>{{ issue.fixed_time|format_datetime if issue.fixed_time else '-' }}</td>
                        </tr>
                        <tr>
                            <th>所属日志</th>
                            <td>
                                <a href="{{ url_for('daily_management.edit_log', date_str=issue.daily_log.log_date|format_datetime('%Y-%m-%d')) }}">
                                    {{ issue.daily_log.log_date|format_datetime('%Y-%m-%d') }}
                                </a>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-12">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">问题描述</h6>
                        </div>
                        <div class="card-body">
                            <p>{{ issue.description|nl2br }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">验证结果</h6>
                        </div>
                        <div class="card-body">
                            {% if issue.verification_result %}
                                <p>{{ issue.verification_result|nl2br }}</p>
                            {% else %}
                                <p class="text-muted">无内容</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">照片记录</h6>
                        </div>
                        <div class="card-body">
                            {% if photos %}
                                <div class="photo-gallery">
                                    {% for photo in photos %}
                                        <div class="photo-item">
                                            <a href="{{ photo.file_path }}" target="_blank">
                                                <img src="{{ photo.file_path }}" alt="问题照片">
                                            </a>
                                            <div class="photo-caption">
                                                {{ photo.description or '无描述' }}
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <p class="text-muted">暂无照片</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
