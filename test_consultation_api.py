#!/usr/bin/env python3
"""
测试在线咨询API接口
模拟前端调用，验证API是否正常工作
"""

import os
import sys
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def test_consultation_api():
    """测试在线咨询API接口"""
    app = create_app()
    
    with app.test_client() as client:
        try:
            print("🌐 测试在线咨询API接口...")
            print("=" * 50)
            
            # 测试数据
            test_data = {
                "name": "API测试用户",
                "contact_type": "微信",
                "contact_value": "api_test_123",
                "content": "这是通过API提交的测试咨询内容，用于验证前端调用是否正常工作。"
            }
            
            print("📤 发送POST请求到 /consultation/api/submit...")
            print(f"   请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
            
            # 发送POST请求
            response = client.post(
                '/consultation/api/submit',
                data=json.dumps(test_data),
                content_type='application/json'
            )
            
            print(f"📥 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.get_json()
                print(f"📋 响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                
                if response_data.get('success'):
                    print("✅ API调用成功！")
                    print(f"   咨询ID: {response_data.get('consultation_id')}")
                    print(f"   消息: {response_data.get('message')}")
                    return True
                else:
                    print(f"❌ API调用失败: {response_data.get('message')}")
                    return False
            else:
                print(f"❌ HTTP请求失败，状态码: {response.status_code}")
                print(f"   响应内容: {response.get_data(as_text=True)}")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程中出现异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def test_consultation_api_validation():
    """测试API参数验证"""
    app = create_app()
    
    with app.test_client() as client:
        try:
            print("\n🔍 测试API参数验证...")
            print("=" * 30)
            
            # 测试空数据
            print("📤 测试空数据...")
            response = client.post(
                '/consultation/api/submit',
                data=json.dumps({}),
                content_type='application/json'
            )
            
            if response.status_code == 200:
                response_data = response.get_json()
                if not response_data.get('success'):
                    print(f"✅ 空数据验证正常: {response_data.get('message')}")
                else:
                    print("❌ 空数据验证失败：应该返回错误")
                    return False
            
            # 测试无效姓名
            print("📤 测试无效姓名...")
            invalid_data = {
                "name": "A",  # 太短
                "contact_type": "微信",
                "contact_value": "test123",
                "content": "测试内容"
            }
            
            response = client.post(
                '/consultation/api/submit',
                data=json.dumps(invalid_data),
                content_type='application/json'
            )
            
            if response.status_code == 200:
                response_data = response.get_json()
                if not response_data.get('success'):
                    print(f"✅ 姓名长度验证正常: {response_data.get('message')}")
                else:
                    print("❌ 姓名长度验证失败：应该返回错误")
                    return False
            
            print("✅ 参数验证测试通过！")
            return True
            
        except Exception as e:
            print(f"❌ 验证测试过程中出现异常: {str(e)}")
            return False

if __name__ == '__main__':
    print("🧪 开始测试在线咨询API...")
    
    # 测试基本功能
    success1 = test_consultation_api()
    
    # 测试参数验证
    success2 = test_consultation_api_validation()
    
    if success1 and success2:
        print("\n🎉 所有API测试通过！在线咨询功能完全正常！")
        print("\n📝 修复总结:")
        print("   ✅ 使用原始SQL插入数据，避免ORM时间字段问题")
        print("   ✅ 不手动设置created_at和updated_at，让数据库使用默认值")
        print("   ✅ 使用DATETIME2(1)精度，避免精度值错误")
        print("   ✅ 避免ORM自动刷新，防止时间字段冲突")
        print("   ✅ API接口正常工作，前端可以正常调用")
    else:
        print("\n❌ 部分测试失败，请检查错误信息！")
