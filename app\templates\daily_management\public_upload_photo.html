{% extends 'base_public.html' %}

{% block title %}上传{{ period }}卫生照片{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    body {
        background-color: #f8f9fc;
        font-family: '<PERSON>uni<PERSON>', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    }

    .header-banner {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        padding: 20px 0;
        margin-bottom: 30px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .school-logo {
        max-height: 80px;
        margin-right: 15px;
    }

    .platform-name {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .school-name {
        font-size: 1.2rem;
    }

    .upload-card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        background-color: white;
        border: none;
    }

    .upload-card .card-header {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        font-weight: bold;
        padding: 20px;
        border: none;
    }

    .upload-card .card-body {
        padding: 30px;
    }

    .info-section {
        background-color: #f8f9fc;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 25px;
        border-left: 4px solid #4e73df;
    }

    .camera-zone {
        border: 3px dashed #4e73df;
        border-radius: 15px;
        padding: 40px 20px;
        text-align: center;
        background-color: #f8f9fc;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;
    }

    .camera-zone:hover {
        background-color: #e3e6f0;
        border-color: #2e59d9;
    }

    .camera-zone.dragover {
        background-color: #e3e6f0;
        border-color: #1cc88a;
        transform: scale(1.02);
    }

    .camera-icon {
        font-size: 3rem;
        color: #4e73df;
        margin-bottom: 15px;
    }

    .camera-text {
        font-size: 1.2rem;
        font-weight: bold;
        color: #5a5c69;
        margin-bottom: 10px;
    }

    .camera-hint {
        color: #858796;
        font-size: 0.9rem;
    }

    .photo-preview-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 15px;
        margin-top: 20px;
    }

    .photo-preview-item {
        position: relative;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .photo-preview {
        width: 100%;
        height: 120px;
        object-fit: cover;
        border-radius: 10px;
    }

    .photo-remove {
        position: absolute;
        top: 5px;
        right: 5px;
        background-color: #e74a3b;
        color: white;
        border: none;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .upload-btn {
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 10px;
        font-weight: bold;
        font-size: 1.1rem;
        margin-top: 20px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .upload-btn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    .upload-btn:disabled {
        background: #858796;
        cursor: not-allowed;
    }

    .success-message {
        display: none;
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-top: 20px;
        text-align: center;
        font-weight: bold;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .error-message {
        display: none;
        background-color: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 10px;
        margin-top: 20px;
        border: 1px solid #f5c6cb;
        font-size: 14px;
        position: relative;
    }

    .error-message.show {
        display: block;
        animation: slideIn 0.3s ease-out;
    }

    .error-message i {
        margin-right: 8px;
        color: #dc3545;
    }

    .error-message .close-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        background: none;
        border: none;
        color: #721c24;
        cursor: pointer;
        padding: 0;
        font-size: 16px;
    }

    .error-message .error-details {
        margin-top: 10px;
        font-size: 12px;
        color: #856404;
    }

    .history-section {
        margin-top: 30px;
    }

    .history-title {
        font-size: 1.2rem;
        font-weight: bold;
        color: #5a5c69;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e3e6f0;
    }

    .history-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }

    .history-item {
        position: relative;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .history-photo {
        width: 100%;
        height: 150px;
        object-fit: cover;
    }

    .history-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 8px;
        font-size: 0.8rem;
    }

    @keyframes slideIn {
        from {
            transform: translateY(-10px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .loading-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 9999;
        justify-content: center;
        align-items: center;
    }

    .loading-content {
        background-color: white;
        padding: 30px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    .loading-spinner {
        font-size: 2rem;
        color: #4e73df;
        margin-bottom: 15px;
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background-color: #e3e6f0;
        border-radius: 4px;
        overflow: hidden;
        margin-top: 15px;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #4e73df, #1cc88a);
        width: 0%;
        transition: width 0.3s ease;
    }

    .footer {
        background-color: #f8f9fc;
        padding: 20px 0;
        margin-top: 30px;
        text-align: center;
        color: #5a5c69;
        font-size: 0.9rem;
    }

    /* 移动端优化 */
    @media (max-width: 768px) {
        .upload-card .card-body {
            padding: 20px;
        }

        .camera-zone {
            padding: 30px 15px;
        }

        .camera-icon {
            font-size: 2.5rem;
        }

        .photo-preview-container {
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
        }

        .photo-preview {
            height: 100px;
        }

        .error-message {
            padding: 12px;
            font-size: 13px;
            margin: 15px 0;
        }

        .error-message .error-details {
            font-size: 11px;
        }

        .history-grid {
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        }

        .history-photo {
            height: 120px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="header-banner">
    <div class="container">
        <div class="d-flex align-items-center">
            <img src="/static/img/logo.png" alt="Logo" class="school-logo">
            <div>
                <div class="platform-name">校园餐智慧食堂</div>
                <div class="school-name">{{ school.name }}</div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="upload-card">
                <div class="card-header">
                    <i class="fas fa-camera mr-2"></i> 上传{{ period }}卫生照片
                </div>
                <div class="card-body">
                    <!-- 信息提示区域 -->
                    <div class="info-section">
                        <h5 class="mb-3">
                            <i class="fas fa-info-circle mr-2"></i>
                            {{ period }}卫生检查
                        </h5>
                        <p class="mb-2">
                            <strong>学校：</strong>{{ school.name }}
                        </p>
                        <p class="mb-2">
                            <strong>日期：</strong>{{ log.log_date.strftime('%Y年%m月%d日') }}
                        </p>
                        <p class="mb-2">
                            <strong>说明：</strong>请拍摄食堂{{ period }}卫生的相关照片，包括卫生状况、设备情况等。
                        </p>
                        <p class="mb-0 text-info">
                            <i class="fas fa-info-circle mr-1"></i>
                            <strong>提示：</strong>照片将自动压缩为800×600像素，确保上传速度和存储效率。
                        </p>
                    </div>

                    <form id="uploadForm">
                        <!-- 拍照/选择照片区域 -->
                        <div class="camera-zone" id="cameraZone">
                            <div class="camera-icon">
                                <i class="fas fa-camera"></i>
                            </div>
                            <div class="camera-text">点击选择照片或拍照</div>
                            <div class="camera-hint">支持相册选择、拍照、多张照片、拖拽上传</div>
                            <input type="file" id="photos" accept="image/*" multiple style="display: none;">
                        </div>

                        <!-- 照片预览区域 -->
                        <div class="photo-preview-container" id="photoPreviewContainer"></div>

                        <!-- 描述输入 -->
                        <div class="form-group" style="margin-top: 20px;">
                            <label for="description"><strong>备注说明</strong> (可选)</label>
                            <textarea class="form-control" id="description" rows="3" placeholder="请输入照片说明或发现的问题..."></textarea>
                        </div>

                        <!-- 上传按钮 -->
                        <button type="submit" class="btn upload-btn btn-block" id="uploadBtn" disabled>
                            <i class="fas fa-upload mr-2"></i> 上传照片
                        </button>

                        <!-- 消息提示 -->
                        <div class="success-message" id="successMessage">
                            <i class="fas fa-check-circle mr-2"></i>
                            照片上传成功！感谢您的配合。
                            <div style="margin-top: 10px;">
                                <button type="button" class="btn btn-light" id="continueBtn">
                                    <i class="fas fa-plus mr-1"></i> 继续上传
                                </button>
                            </div>
                        </div>

                        <!-- 错误消息容器 -->
                        <div class="error-message" id="errorMessage">
                            <button type="button" class="close-btn" data-onclick="hideError()" data-event-id="71e1afee">
                                <i class="fas fa-times"></i>
                            </button>
                            <div class="error-content">
                                <i class="fas fa-exclamation-circle"></i>
                                <span id="errorText"></span>
                            </div>
                            <div class="error-details" id="errorDetails"></div>
                        </div>
                    </form>

                    <!-- 历史照片区域 -->
                    {% if history_photos %}
                    <div class="history-section">
                        <div class="history-title">
                            <i class="fas fa-history mr-2"></i> 今日已上传照片
                        </div>
                        <div class="history-grid">
                            {% for photo in history_photos %}
                            <div class="history-item">
                                <img src="{{ photo.file_path }}" alt="历史照片" class="history-photo">
                                <div class="history-info">
                                    {{ photo.upload_time.strftime('%H:%M') }}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载遮罩 -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
        </div>
        <h5>正在上传照片...</h5>
        <p>请稍候，正在处理您的照片</p>
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        <div id="progressText">0%</div>
    </div>
</div>

<div class="footer">
    <div class="container">
        <p>© {{ now.year }} 校园餐智慧食堂 - 版权所有</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    let selectedFiles = [];

    // DOM 元素
    const cameraZone = document.getElementById('cameraZone');
    const photosInput = document.getElementById('photos');
    const previewContainer = document.getElementById('photoPreviewContainer');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadForm = document.getElementById('uploadForm');
    const loadingOverlay = document.getElementById('loadingOverlay');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    const successMessage = document.getElementById('successMessage');
    const errorMessage = document.getElementById('errorMessage');
    const continueBtn = document.getElementById('continueBtn');

    // 点击拍照区域
    cameraZone.addEventListener('click', function() {
        photosInput.click();
    });

    // 拖拽上传
    cameraZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        cameraZone.classList.add('dragover');
    });

    cameraZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        cameraZone.classList.remove('dragover');
    });

    cameraZone.addEventListener('drop', function(e) {
        e.preventDefault();
        cameraZone.classList.remove('dragover');

        const files = Array.from(e.dataTransfer.files).filter(file => file.type.startsWith('image/'));
        if (files.length > 0) {
            handleFileSelection(files);
        }
    });

    // 文件选择
    photosInput.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        if (files.length > 0) {
            handleFileSelection(files);
        }
    });

    // 处理文件选择
    function handleFileSelection(files) {
        // 验证文件
        const validFiles = [];
        const errors = [];

        for (let file of files) {
            if (!file.type.startsWith('image/')) {
                errors.push(`${file.name}: 只能上传图片文件`);
                continue;
            }
            if (file.size > 10 * 1024 * 1024) { // 10MB
                errors.push(`${file.name}: 图片大小不能超过10MB`);
                continue;
            }
            if (file.size === 0) {
                errors.push(`${file.name}: 文件为空`);
                continue;
            }
            validFiles.push(file);
        }

        // 显示错误信息
        if (errors.length > 0) {
            showError(errors.join('\n'));
        }

        if (validFiles.length === 0) return;

        // 检查总文件数量
        if (selectedFiles.length + validFiles.length > 10) {
            showError('最多只能选择10张照片');
            return;
        }

        // 添加到选中文件列表
        selectedFiles = selectedFiles.concat(validFiles);

        // 更新预览
        updatePreview();

        // 启用上传按钮
        uploadBtn.disabled = selectedFiles.length === 0;

        // 显示成功信息
        if (validFiles.length > 0) {
            console.log(`成功选择 ${validFiles.length} 张照片`);
        }
    }

    // 更新照片预览
    function updatePreview() {
        previewContainer.innerHTML = '';

        selectedFiles.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewItem = document.createElement('div');
                previewItem.className = 'photo-preview-item';

                previewItem.innerHTML = `
                    <img src="${e.target.result}" class="photo-preview" alt="预览图片">
                    <button type="button" class="photo-remove" onclick="removePhoto(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                `;

                previewContainer.appendChild(previewItem);
            };
            reader.readAsDataURL(file);
        });
    }

    // 删除照片
    function removePhoto(index) {
        selectedFiles.splice(index, 1);
        updatePreview();
        uploadBtn.disabled = selectedFiles.length === 0;
    }

    // 表单提交
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();

        if (selectedFiles.length === 0) {
            showError('请选择至少一张照片');
            return;
        }

        uploadPhotos();
    });

    // 上传照片
    function uploadPhotos() {
        const description = document.getElementById('description').value;

        // 显示加载遮罩
        loadingOverlay.style.display = 'flex';

        // 创建FormData
        const formData = new FormData();
        formData.append('description', description);

        // 添加所有照片
        selectedFiles.forEach(file => {
            formData.append('photos', file);
        });

        // 模拟进度
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            updateProgress(progress);
        }, 200);

        // 发送请求
        fetch('/api/v2/photos/upload/{{ school.id }}/{{ period }}', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(JSON.stringify({
                        message: data.error,
                        type: data.error_type
                    }));
                });
            }
            return response.json();
        })
        .then(data => {
            clearInterval(progressInterval);
            updateProgress(100);

            setTimeout(() => {
                loadingOverlay.style.display = 'none';

                if (data.success) {
                    showSuccess(data);
                    resetForm();
                    // 刷新页面以更新历史照片
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    showError(data.error, data.error_type);
                }
            }, 500);
        })
        .catch(error => {
            clearInterval(progressInterval);
            loadingOverlay.style.display = 'none';
            
            try {
                const errorData = JSON.parse(error.message);
                showError(errorData.message, errorData.type);
            } catch (e) {
                showError('上传失败，请重试', 'system_error');
            }
        });
    }

    // 更新进度
    function updateProgress(percent) {
        progressFill.style.width = percent + '%';
        progressText.textContent = Math.round(percent) + '%';
    }

    // 显示成功消息
    function showSuccess(data) {
        hideMessages();
        successMessage.style.display = 'block';
        
        // 滚动到成功消息
        successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // 显示错误消息
    function showError(message, errorType) {
        const errorDiv = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        const errorDetails = document.getElementById('errorDetails');
        
        // 设置错误消息
        errorText.textContent = message;
        
        // 根据错误类型设置详细信息
        let details = '';
        switch(errorType) {
            case 'file_too_large':
                details = '请选择小于5MB的图片文件';
                break;
            case 'invalid_format':
                details = '支持的格式：JPG、PNG、GIF';
                break;
            case 'network_error':
                details = '请检查网络连接后重试';
                break;
            case 'system_error':
                details = '系统暂时无法处理请求，请稍后重试';
                break;
            default:
                details = '请重试或联系管理员';
        }
        errorDetails.textContent = details;
        
        // 显示错误消息
        errorDiv.classList.add('show');
        
        // 5秒后自动隐藏
        setTimeout(() => {
            hideError();
        }, 5000);
    }

    function hideError() {
        const errorDiv = document.getElementById('errorMessage');
        errorDiv.classList.remove('show');
    }

    // 隐藏所有消息
    function hideMessages() {
        successMessage.style.display = 'none';
        hideError();
    }

    // 重置表单
    function resetForm() {
        selectedFiles = [];
        previewContainer.innerHTML = '';
        uploadBtn.disabled = true;
        document.getElementById('description').value = '';
        hideError();
    }

    // 继续上传按钮
    continueBtn.addEventListener('click', function() {
        hideMessages();
        resetForm();
    });

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 重置进度
        updateProgress(0);

        // 检查是否支持拍照
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            // 支持拍照，更新提示文字
            document.querySelector('.camera-hint').textContent = '支持拍照、选择照片或拖拽上传';
        }
    });
</script>
{% endblock %} 
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>