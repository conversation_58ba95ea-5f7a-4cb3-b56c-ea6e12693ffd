{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>{{ title }}</h2>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('system.users') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回用户列表
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form method="post" novalidate>
            {{ form.hidden_tag() }}

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.username.label }}
                        {{ form.username(class="form-control") }}
                        {% for error in form.username.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.email.label }}
                        {{ form.email(class="form-control") }}
                        {% for error in form.email.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.real_name.label }}
                        {{ form.real_name(class="form-control") }}
                        {% for error in form.real_name.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.phone.label }}
                        {{ form.phone(class="form-control") }}
                        {% for error in form.phone.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.password.label }}
                        {{ form.password(class="form-control") }}
                        {% for error in form.password.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                        {% if user %}
                        <small class="form-text text-muted">如不修改密码，请留空</small>
                        {% else %}
                        <small class="form-text text-muted">如不设置密码，将使用默认密码：123456</small>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.password2.label }}
                        {{ form.password2(class="form-control") }}
                        {% for error in form.password2.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="area_selector">所属区域</label>
                        <div class="area-selector">
                            <select id="area_level_1" class="form-control mb-2">
                                <option value="">请选择县市区</option>
                                <!-- 县市区选项将通过JavaScript动态加载 -->
                            </select>

                            <select id="area_level_2" class="form-control mb-2" style="display:none;">
                                <option value="">请选择乡镇</option>
                                <!-- 乡镇选项将通过JavaScript动态加载 -->
                            </select>

                            <select id="area_level_3" class="form-control mb-2" style="display:none;">
                                <option value="">请选择学校</option>
                                <!-- 学校选项将通过JavaScript动态加载 -->
                            </select>

                            <select id="area_level_4" class="form-control mb-2" style="display:none;">
                                <option value="">请选择食堂</option>
                                <!-- 食堂选项将通过JavaScript动态加载 -->
                            </select>

                            <!-- 隐藏的实际表单字段 -->
                            {{ form.area_id(type="hidden", id="area_id") }}
                            {% for error in form.area_id.errors %}
                            <small class="text-danger">{{ error }}</small>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.area_level.label }}
                        {{ form.area_level(class="form-control", id="area_level") }}
                        {% for error in form.area_level.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="form-group">
                {{ form.roles.label }}
                {{ form.roles(class="form-control", size=5) }}
                {% for error in form.roles.errors %}
                <small class="text-danger">{{ error }}</small>
                {% endfor %}
                <small class="form-text text-muted">按住Ctrl键可以选择多个角色</small>
            </div>

            <div class="form-group">
                {{ form.status.label }}
                {{ form.status(class="form-control") }}
                {% for error in form.status.errors %}
                <small class="text-danger">{{ error }}</small>
                {% endfor %}
            </div>

            <div class="form-group text-center">
                <a href="{{ url_for('system.users') }}" class="btn btn-secondary">取消</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 加载县市区数据
    loadAreaOptions(1, null);

    // 当选择县市区时
    $('#area_level_1').change(function() {
        var areaId = $(this).val();
        if (areaId) {
            // 设置隐藏字段的值
            $('#area_id').val(areaId);
            $('#area_level').val(1);

            // 加载下级区域（乡镇）
            loadAreaOptions(2, areaId);
            $('#area_level_2').show();

            // 清空并隐藏更下级的选择框
            $('#area_level_3, #area_level_4').hide().val('');
        } else {
            // 清空并隐藏所有下级选择框
            $('#area_level_2, #area_level_3, #area_level_4').hide().val('');
            $('#area_id').val('');
            $('#area_level').val('');
        }
    });

    // 当选择乡镇时
    $('#area_level_2').change(function() {
        var areaId = $(this).val();
        if (areaId) {
            // 设置隐藏字段的值
            $('#area_id').val(areaId);
            $('#area_level').val(2);

            // 加载下级区域（学校）
            loadAreaOptions(3, areaId);
            $('#area_level_3').show();

            // 清空并隐藏更下级的选择框
            $('#area_level_4').hide().val('');
        } else {
            // 清空并隐藏所有下级选择框
            $('#area_level_3, #area_level_4').hide().val('');
            // 恢复上级区域的值
            var parentId = $('#area_level_1').val();
            if (parentId) {
                $('#area_id').val(parentId);
                $('#area_level').val(1);
            } else {
                $('#area_id').val('');
                $('#area_level').val('');
            }
        }
    });

    // 当选择学校时
    $('#area_level_3').change(function() {
        var areaId = $(this).val();
        if (areaId) {
            // 设置隐藏字段的值
            $('#area_id').val(areaId);
            $('#area_level').val(3);

            // 加载下级区域（食堂）
            loadAreaOptions(4, areaId);
            $('#area_level_4').show();
        } else {
            // 清空并隐藏下级选择框
            $('#area_level_4').hide().val('');
            // 恢复上级区域的值
            var parentId = $('#area_level_2').val();
            if (parentId) {
                $('#area_id').val(parentId);
                $('#area_level').val(2);
            } else {
                parentId = $('#area_level_1').val();
                if (parentId) {
                    $('#area_id').val(parentId);
                    $('#area_level').val(1);
                } else {
                    $('#area_id').val('');
                    $('#area_level').val('');
                }
            }
        }
    });

    // 当选择食堂时
    $('#area_level_4').change(function() {
        var areaId = $(this).val();
        if (areaId) {
            // 设置隐藏字段的值
            $('#area_id').val(areaId);
            $('#area_level').val(4);
        } else {
            // 恢复上级区域的值
            var parentId = $('#area_level_3').val();
            if (parentId) {
                $('#area_id').val(parentId);
                $('#area_level').val(3);
            } else {
                parentId = $('#area_level_2').val();
                if (parentId) {
                    $('#area_id').val(parentId);
                    $('#area_level').val(2);
                } else {
                    parentId = $('#area_level_1').val();
                    if (parentId) {
                        $('#area_id').val(parentId);
                        $('#area_level').val(1);
                    } else {
                        $('#area_id').val('');
                        $('#area_level').val('');
                    }
                }
            }
        }
    });

    // 加载区域选项的函数
    function loadAreaOptions(level, parentId) {
        var url = "{{ url_for('area.get_areas_by_level_and_parent') }}";
        var params = {level: level};
        if (parentId) {
            params.parent_id = parentId;
        }

        $.getJSON(url, params, function(data) {
            var select = $('#area_level_' + level);
            // 保留第一个选项（请选择...）
            var firstOption = select.find('option:first');
            select.empty().append(firstOption);

            // 添加新选项
            $.each(data, function(index, area) {
                select.append($('<option></option>')
                    .attr('value', area.id)
                    .text(area.name));
            });

            // 如果是编辑模式，设置默认选中值
            {% if user and user.area %}
            if (level == {{ user.area.level }}) {
                select.val({{ user.area.id }});
                select.trigger('change');
            } else if (level < {{ user.area.level }}) {
                // 如果是上级区域，找到对应的区域ID
                var areaPath = {{ user.area.get_path_ids()|tojson }};
                if (areaPath[level-1]) {
                    select.val(areaPath[level-1]);
                    select.trigger('change');
                }
            }
            {% endif %}
        });
    }
});
</script>
{% endblock %}
