<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端错误修复测试</title>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h1>前端错误修复测试</h1>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>jQuery 测试</h5>
                    </div>
                    <div class="card-body">
                        <button id="jqueryTest" class="btn btn-primary">测试 jQuery</button>
                        <div id="jqueryResult" class="mt-2"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>API 测试</h5>
                    </div>
                    <div class="card-body">
                        <button id="apiTest" class="btn btn-warning">测试 API</button>
                        <div id="apiResult" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>控制台日志</h5>
                    </div>
                    <div class="card-body">
                        <div id="consoleLog" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto;">
                            <!-- 控制台输出 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 重写 console.log 以显示在页面上
        const originalLog = console.log;
        const originalError = console.error;
        const logContainer = document.getElementById('consoleLog');
        
        function addLogEntry(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-danger' : 'text-info';
            logContainer.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLogEntry(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLogEntry(args.join(' '), 'error');
        };

        $(document).ready(function() {
            console.log('✅ jQuery 加载成功！');
            console.log('✅ DOM 准备完成');
            
            // jQuery 测试
            $('#jqueryTest').click(function() {
                console.log('🧪 开始 jQuery 测试...');
                
                try {
                    // 测试 jQuery 基本功能
                    const version = $.fn.jquery;
                    $('#jqueryResult').html(`
                        <div class="alert alert-success">
                            <strong>✅ jQuery 工作正常！</strong><br>
                            版本: ${version}<br>
                            当前时间: ${new Date().toLocaleString()}
                        </div>
                    `);
                    console.log(`✅ jQuery 版本: ${version}`);
                } catch (error) {
                    $('#jqueryResult').html(`
                        <div class="alert alert-danger">
                            <strong>❌ jQuery 错误：</strong><br>
                            ${error.message}
                        </div>
                    `);
                    console.error('❌ jQuery 测试失败:', error);
                }
            });
            
            // API 测试
            $('#apiTest').click(function() {
                console.log('🧪 开始 API 测试...');
                
                // 测试一个简单的 API 调用
                $.ajax({
                    url: '/api/carousel/list',
                    type: 'GET',
                    timeout: 5000,
                    success: function(data) {
                        $('#apiResult').html(`
                            <div class="alert alert-success">
                                <strong>✅ API 调用成功！</strong><br>
                                返回数据类型: ${typeof data}<br>
                                数据长度: ${Array.isArray(data) ? data.length : '非数组'}
                            </div>
                        `);
                        console.log('✅ API 调用成功:', data);
                    },
                    error: function(xhr, status, error) {
                        $('#apiResult').html(`
                            <div class="alert alert-danger">
                                <strong>❌ API 调用失败：</strong><br>
                                状态: ${status}<br>
                                错误: ${error}<br>
                                状态码: ${xhr.status}
                            </div>
                        `);
                        console.error('❌ API 调用失败:', status, error);
                    }
                });
            });
            
            // 检查常见的前端错误
            console.log('🔍 检查常见错误...');
            
            // 检查是否有未定义的变量
            try {
                if (typeof $ === 'undefined') {
                    console.error('❌ jQuery ($) 未定义');
                } else {
                    console.log('✅ jQuery ($) 已定义');
                }
                
                if (typeof bootstrap === 'undefined') {
                    console.error('❌ Bootstrap 未定义');
                } else {
                    console.log('✅ Bootstrap 已定义');
                }
                
                // 检查网络连接
                if (navigator.onLine) {
                    console.log('✅ 网络连接正常');
                } else {
                    console.error('❌ 网络连接异常');
                }
                
            } catch (error) {
                console.error('❌ 检查过程中出现错误:', error);
            }
        });
        
        // 监听全局错误
        window.addEventListener('error', function(event) {
            console.error('❌ 全局错误:', event.error.message, '在', event.filename, '第', event.lineno, '行');
        });
        
        // 监听未处理的 Promise 拒绝
        window.addEventListener('unhandledrejection', function(event) {
            console.error('❌ 未处理的 Promise 拒绝:', event.reason);
        });
    </script>
</body>
</html>
