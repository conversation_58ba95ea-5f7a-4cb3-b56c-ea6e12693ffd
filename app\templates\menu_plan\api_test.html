{% extends 'base.html' %}

{% block title %}API接口测试{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .api-section {
        margin-bottom: 30px;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
    }
    .api-title {
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }
    .api-response {
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        max-height: 400px;
        overflow-y: auto;
        font-family: monospace;
        white-space: pre-wrap;
    }
    .btn-api {
        margin-top: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h1 class="my-4">API接口测试</h1>

    <!-- 菜单保存测试 -->
    <div class="api-section">
        <h3 class="api-title">菜单保存接口测试</h3>

        <form id="menuSaveForm">
            <div class="form-group">
                <label for="menuAreaId">区域ID</label>
                <input type="number" class="form-control" id="menuAreaId" name="area_id" value="{{ current_user.area_id }}">
            </div>

            <div class="form-group">
                <label for="menuWeekStart">周开始日期</label>
                <input type="date" class="form-control" id="menuWeekStart" name="week_start" value="{{ now.strftime('%Y-%m-%d') }}">
            </div>

            <div class="form-group">
                <label for="menuJsonData">菜单数据 (JSON格式)</label>
                <textarea class="form-control" id="menuJsonData" rows="10">{
  "days": {
    "{{ now.strftime('%Y-%m-%d') }}": {
      "早餐": [
        {"id": 1, "name": "米粥", "quantity": 1}
      ],
      "午餐": [
        {"id": 2, "name": "米饭", "quantity": 1}
      ],
      "晚餐": [
        {"id": 3, "name": "面条", "quantity": 1}
      ]
    }
  }
}</textarea>
            </div>

            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="useCSRFMenu" checked>
                <label class="form-check-label" for="useCSRFMenu">
                    使用CSRF令牌
                </label>
            </div>

            <button type="button" class="btn btn-primary btn-api" id="saveMenu">保存菜单</button>
        </form>

        <div class="mt-3">
            <h5>响应结果：</h5>
            <div class="api-response" id="menuSaveResponse">等待请求...</div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <!-- 周菜谱展示接口测试 -->
            <div class="api-section">
                <h3 class="api-title">周菜谱展示接口</h3>

                <form id="weekMenuForm">
                    <div class="form-group">
                        <label for="weekStart">周开始日期</label>
                        <input type="date" class="form-control" id="weekStart" name="week_start">
                    </div>

                    <div class="form-group">
                        <label for="areaId">区域ID</label>
                        <input type="number" class="form-control" id="areaId" name="area_id" placeholder="留空使用默认区域">
                    </div>

                    <button type="button" class="btn btn-primary btn-api" id="getWeekMenu">获取周菜谱</button>
                </form>

                <div class="mt-3">
                    <h5>响应结果：</h5>
                    <div class="api-response" id="weekMenuResponse">等待请求...</div>
                </div>
            </div>

            <!-- 菜品详情接口测试 -->
            <div class="api-section">
                <h3 class="api-title">菜品详情接口</h3>

                <form id="recipeForm">
                    <div class="form-group">
                        <label for="recipeId">菜品ID</label>
                        <input type="number" class="form-control" id="recipeId" name="recipe_id" required>
                    </div>

                    <button type="button" class="btn btn-primary btn-api" id="getRecipe">获取菜品详情</button>
                </form>

                <div class="mt-3">
                    <h5>响应结果：</h5>
                    <div class="api-response" id="recipeResponse">等待请求...</div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <!-- 日菜谱采购参考接口测试 -->
            <div class="api-section">
                <h3 class="api-title">日菜谱采购参考接口</h3>

                <form id="purchaseReferenceForm">
                    <div class="form-group">
                        <label for="referenceDate">日期</label>
                        <input type="date" class="form-control" id="referenceDate" name="date">
                    </div>

                    <div class="form-group">
                        <label for="referenceAreaId">区域ID</label>
                        <input type="number" class="form-control" id="referenceAreaId" name="area_id" placeholder="留空使用默认区域">
                    </div>

                    <button type="button" class="btn btn-primary btn-api" id="getPurchaseReference">获取采购参考</button>
                </form>

                <div class="mt-3">
                    <h5>响应结果：</h5>
                    <div class="api-response" id="purchaseReferenceResponse">等待请求...</div>
                </div>
            </div>

            <!-- 库存信息接口测试 -->
            <div class="api-section">
                <h3 class="api-title">库存信息接口</h3>

                <form id="inventoryForm">
                    <div class="form-group">
                        <label for="inventoryAreaId">区域ID</label>
                        <input type="number" class="form-control" id="inventoryAreaId" name="area_id" placeholder="留空使用默认区域">
                    </div>

                    <div class="form-group">
                        <label for="ingredientIds">食材ID列表（逗号分隔）</label>
                        <input type="text" class="form-control" id="ingredientIds" name="ingredient_ids" placeholder="例如：1,2,3">
                    </div>

                    <button type="button" class="btn btn-primary btn-api" id="getInventory">获取库存信息</button>
                </form>

                <div class="mt-3">
                    <h5>响应结果：</h5>
                    <div class="api-response" id="inventoryResponse">等待请求...</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 设置默认日期为今天
        const today = new Date();
        const dateString = today.toISOString().split('T')[0];
        $('#weekStart').val(dateString);
        $('#referenceDate').val(dateString);
        $('#menuWeekStart').val(dateString);

        // 获取CSRF令牌
        const csrfToken = $('meta[name="csrf-token"]').attr('content');
        console.log("CSRF Token:", csrfToken); // 调试用

        // 周菜谱展示接口测试
        $('#getWeekMenu').click(function() {
            const weekStart = $('#weekStart').val();
            const areaId = $('#areaId').val();

            let url = '/api/menu-display/week';
            let params = [];

            if (weekStart) {
                params.push(`week_start=${weekStart}`);
            }

            if (areaId) {
                params.push(`area_id=${areaId}`);
            }

            if (params.length > 0) {
                url += '?' + params.join('&');
            }

            $('#weekMenuResponse').text('正在请求...');

            $.ajax({
                url: url,
                type: 'GET',
                headers: {
                    'X-CSRFToken': csrfToken
                },
                success: function(response) {
                    $('#weekMenuResponse').text(JSON.stringify(response, null, 2));
                },
                error: function(xhr) {
                    $('#weekMenuResponse').text(`请求失败: ${xhr.status} ${xhr.statusText}\n${xhr.responseText}`);
                }
            });
        });

        // 菜品详情接口测试
        $('#getRecipe').click(function() {
            const recipeId = $('#recipeId').val();

            if (!recipeId) {
                $('#recipeResponse').text('请输入菜品ID');
                return;
            }

            $('#recipeResponse').text('正在请求...');

            $.ajax({
                url: `/api/menu-display/recipe/${recipeId}`,
                type: 'GET',
                headers: {
                    'X-CSRFToken': csrfToken
                },
                success: function(response) {
                    $('#recipeResponse').text(JSON.stringify(response, null, 2));
                },
                error: function(xhr) {
                    $('#recipeResponse').text(`请求失败: ${xhr.status} ${xhr.statusText}\n${xhr.responseText}`);
                }
            });
        });

        // 日菜谱采购参考接口测试
        $('#getPurchaseReference').click(function() {
            const date = $('#referenceDate').val();
            const areaId = $('#referenceAreaId').val();

            let url = '/api/flexible-purchase/reference';
            let params = [];

            if (date) {
                params.push(`date=${date}`);
            }

            if (areaId) {
                params.push(`area_id=${areaId}`);
            }

            if (params.length > 0) {
                url += '?' + params.join('&');
            }

            $('#purchaseReferenceResponse').text('正在请求...');

            $.ajax({
                url: url,
                type: 'GET',
                headers: {
                    'X-CSRFToken': csrfToken
                },
                success: function(response) {
                    $('#purchaseReferenceResponse').text(JSON.stringify(response, null, 2));
                },
                error: function(xhr) {
                    $('#purchaseReferenceResponse').text(`请求失败: ${xhr.status} ${xhr.statusText}\n${xhr.responseText}`);
                }
            });
        });

        // 库存信息接口测试
        $('#getInventory').click(function() {
            const areaId = $('#inventoryAreaId').val();
            const ingredientIds = $('#ingredientIds').val();

            let url = '/api/flexible-purchase/inventory/status';
            let params = [];

            if (areaId) {
                params.push(`area_id=${areaId}`);
            }

            if (ingredientIds) {
                params.push(`ingredient_ids=${ingredientIds}`);
            }

            if (params.length > 0) {
                url += '?' + params.join('&');
            }

            $('#inventoryResponse').text('正在请求...');

            $.ajax({
                url: url,
                type: 'GET',
                headers: {
                    'X-CSRFToken': csrfToken
                },
                success: function(response) {
                    $('#inventoryResponse').text(JSON.stringify(response, null, 2));
                },
                error: function(xhr) {
                    $('#inventoryResponse').text(`请求失败: ${xhr.status} ${xhr.statusText}\n${xhr.responseText}`);
                }
            });
        });

        // 菜单保存接口测试
        $('#saveMenu').click(function() {
            const areaId = parseInt($('#menuAreaId').val());
            const weekStart = $('#menuWeekStart').val();
            let menuData;

            try {
                menuData = JSON.parse($('#menuJsonData').val());
            } catch (e) {
                $('#menuSaveResponse').text(`JSON解析错误: ${e.message}`);
                return;
            }

            const saveData = {
                area_id: areaId,
                week_start: weekStart,
                menu_data: menuData
            };

            console.log('准备保存的数据:', saveData);
            console.log('数据JSON字符串:', JSON.stringify(saveData));

            $('#menuSaveResponse').text('正在请求...');

            // 准备AJAX设置
            const ajaxSettings = {
                url: '/api/menu-plan/week/save',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(saveData),
                success: function(response) {
                    $('#menuSaveResponse').text(JSON.stringify(response, null, 2));
                },
                error: function(xhr, status, error) {
                    console.error('保存请求失败:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText,
                        statusCode: xhr.status
                    });

                    try {
                        const errorObj = JSON.parse(xhr.responseText);
                        $('#menuSaveResponse').text(`请求失败: ${errorObj.message || error || status}\n\n${xhr.responseText}`);
                    } catch(e) {
                        $('#menuSaveResponse').text(`请求失败: ${error || status} (${xhr.status})\n\n${xhr.responseText}`);
                    }
                }
            };

            // 如果选择使用CSRF令牌，添加头部
            if ($('#useCSRFMenu').is(':checked')) {
                ajaxSettings.headers = {
                    'X-CSRFToken': csrfToken
                };
            }

            // 发送请求
            $.ajax(ajaxSettings);
        });
    });
</script>
{% endblock %}
