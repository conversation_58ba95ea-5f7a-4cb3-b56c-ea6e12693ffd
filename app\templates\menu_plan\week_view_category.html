{% extends 'base.html' %}

{% block title %}周菜单安排 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row mb-4">
    <div class="col-md-8">
      <h2>周菜单安排</h2>
      <p class="text-muted">使用系统自带的表单处理功能，无需复杂的JavaScript</p>
    </div>
    <div class="col-md-4 text-right">
      <a href="{{ url_for('menu_plan.print_week_menu', area_id=area_id, week_start=week_start) }}" class="btn btn-info" target="_blank">
        <i class="fas fa-print"></i> 打印菜单
      </a>
    </div>
  </div>

  <div class="row">
    <!-- 筛选条件和食谱选择 -->
    <div class="col-md-3 mb-4">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">筛选条件</h5>
        </div>
        <div class="card-body">
          <form method="get">
            <div class="form-group">
              <label for="area_id">区域</label>
              <select class="form-control" id="area_id" name="area_id">
                {% for area in areas %}
                <option value="{{ area.id }}" {% if area.id == area_id %}selected{% endif %}>
                  {{ area.get_level_name() }} - {{ area.name }}
                </option>
                {% endfor %}
              </select>
            </div>
            <div class="form-group">
              <label for="week_start">周开始日期</label>
              <input type="date" class="form-control" id="week_start" name="week_start" value="{{ week_start }}">
            </div>
            <button type="submit" class="btn btn-primary btn-block">应用筛选</button>
          </form>
        </div>
      </div>

      <!-- 食谱分类和选择 -->
      <form method="post" action="{{ url_for('menu_plan.save_week_menu_category') }}" id="recipeForm">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <input type="hidden" name="area_id" value="{{ area_id }}">
        <input type="hidden" name="week_start" value="{{ week_start }}">

        <!-- 当前选择的日期和餐次 -->
        <input type="hidden" id="current_date" name="current_date" value="">
        <input type="hidden" id="current_meal" name="current_meal" value="">

        <!-- 食谱分类 -->
        <div class="card mt-3">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">食谱分类</h5>
            <div>
              <span id="selected_date_meal" class="text-primary font-weight-bold"></span>
            </div>
          </div>
          <div class="card-body">
            <!-- 搜索框 -->
            <div class="input-group mb-3">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-search"></i></span>
              </div>
              <input type="text" id="recipeSearch" class="form-control" placeholder="搜索食谱...">
            </div>

            <div class="accordion" id="recipeAccordion">
              {% for category, recipes in recipes_by_category.items() %}
              <div class="card">
                <div class="card-header" id="heading{{ loop.index }}">
                  <h2 class="mb-0">
                    <button class="btn btn-link btn-block text-left" type="button" data-toggle="collapse" data-target="#collapse{{ loop.index }}">
                      {{ category }} <span class="badge badge-primary float-right">{{ recipes|length }}</span>
                    </button>
                  </h2>
                </div>
                <div id="collapse{{ loop.index }}" class="collapse" data-parent="#recipeAccordion">
                  <div class="card-body">
                    <div class="list-group">
                      {% for recipe in recipes %}
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input recipe-checkbox"
                               id="recipe_{{ recipe.id }}"
                               value="{{ recipe.id }}"
                               data-name="{{ recipe.name }}">
                        <label class="custom-control-label" for="recipe_{{ recipe.id }}">{{ recipe.name }}</label>
                      </div>
                      {% endfor %}
                    </div>
                  </div>
                </div>
              </div>
              {% endfor %}
            </div>

            <div class="mt-3">
              <button type="button" id="addToMenuBtn" class="btn btn-success btn-block" disabled>
                <i class="fas fa-plus-circle"></i> 添加到菜单
              </button>
            </div>
          </div>
        </div>

        <div class="card mt-3">
          <div class="card-header">
            <h5 class="mb-0">保存菜单</h5>
          </div>
          <div class="card-body">
            <p class="text-muted mb-3">点击保存按钮将所有选择的菜品保存到数据库</p>
            <button type="submit" id="saveBtn" class="btn btn-primary btn-lg btn-block">
              <i class="fas fa-save"></i> 保存菜单
            </button>
          </div>
        </div>
      </form>
    </div>

    <!-- 周菜单表格 -->
    <div class="col-md-9">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">周菜单安排</h5>
        </div>
        <div class="card-body">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th style="width: 15%">日期</th>
                <th style="width: 28%">早餐</th>
                <th style="width: 28%">午餐</th>
                <th style="width: 28%">晚餐</th>
              </tr>
            </thead>
            <tbody>
              {% for date_str, day_data in week_dates.items() %}
              <tr>
                <td>
                  <div class="font-weight-bold">{{ day_data.weekday }}</div>
                  <div>{{ date_str }}</div>
                </td>
                {% for meal_type in ['早餐', '午餐', '晚餐'] %}
                <td>
                  <div id="menu-{{ date_str }}-{{ meal_type }}" class="menu-cell">
                    {% if menuData.get(date_str, {}).get(meal_type) %}
                      {% for recipe in menuData[date_str][meal_type] %}
                        <div class="badge badge-info mb-1 recipe-badge">
                          <span>{{ recipe.name }}</span>
                          <button type="button" class="close ml-1 remove-recipe"
                                  data-date="{{ date_str }}"
                                  data-meal="{{ meal_type }}"
                                  data-id="{{ recipe.id }}">&times;</button>
                          <input type="hidden" name="menu[{{ date_str }}][{{ meal_type }}][]" value="{{ recipe.id }}">
                        </div>
                      {% endfor %}
                    {% else %}
                      <span class="text-muted">未选择</span>
                    {% endif %}
                  </div>
                  <button type="button" class="btn btn-sm btn-outline-primary mt-2 select-cell-btn"
                          data-date="{{ date_str }}"
                          data-meal="{{ meal_type }}">
                    选择
                  </button>
                </td>
                {% endfor %}
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
  .recipe-badge {
    font-size: 0.9em;
    padding: 5px 8px;
    margin-right: 5px;
    display: inline-block;
  }
  .recipe-badge .close {
    font-size: 1em;
    line-height: 0.8;
  }
  .menu-cell {
    min-height: 40px;
  }
  .selected-cell {
    background-color: #e8f4ff;
    border: 1px solid #b8daff;
  }
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
  $(document).ready(function() {
    // 初始化 - 展开第一个食谱分类
    $('#collapse1').addClass('show');

    // 选择单元格
    $('.select-cell-btn').click(function() {
      const date = $(this).data('date');
      const meal = $(this).data('meal');

      // 更新当前选择
      $('#current_date').val(date);
      $('#current_meal').val(meal);
      $('#selected_date_meal').text(date + ' ' + meal);

      // 高亮显示当前选择的单元格
      $('.menu-cell').removeClass('selected-cell');
      $('#menu-' + date + '-' + meal).addClass('selected-cell');

      // 高亮显示当前选择的按钮
      $('.select-cell-btn').removeClass('btn-primary').addClass('btn-outline-primary');
      $(this).removeClass('btn-outline-primary').addClass('btn-primary');

      // 启用添加按钮
      $('#addToMenuBtn').prop('disabled', false);

      // 清除所有复选框
      $('.recipe-checkbox').prop('checked', false);

      // 选中已有的菜品
      const cell = $('#menu-' + date + '-' + meal);
      cell.find('input[type="hidden"]').each(function() {
        const recipeId = $(this).val();
        $('#recipe_' + recipeId).prop('checked', true);
      });

      // 滚动到食谱分类区域
      $('html, body').animate({
        scrollTop: $('#recipeAccordion').offset().top - 100
      }, 300);
    });

    // 添加到菜单
    $('#addToMenuBtn').click(function() {
      const date = $('#current_date').val();
      const meal = $('#current_meal').val();

      if (!date || !meal) {
        alert('请先选择一个日期和餐次');
        return;
      }

      const cell = $('#menu-' + date + '-' + meal);
      cell.empty(); // 清空单元格

      // 获取选中的菜品
      const selectedRecipes = [];
      $('.recipe-checkbox:checked').each(function() {
        const id = $(this).val();
        const name = $(this).data('name');
        selectedRecipes.push({ id, name });
      });

      // 如果没有选中任何菜品，显示"未选择"
      if (selectedRecipes.length === 0) {
        cell.html('<span class="text-muted">未选择</span>');
        return;
      }

      // 添加选中的菜品到单元格
      selectedRecipes.forEach(function(recipe) {
        const badge = $(`
          <div class="badge badge-info mb-1 recipe-badge">
            <span>${recipe.name}</span>
            <button type="button" class="close ml-1 remove-recipe"
                    data-date="${date}"
                    data-meal="${meal}"
                    data-id="${recipe.id}">&times;</button>
            <input type="hidden" name="menu[${date}][${meal}][]" value="${recipe.id}">
          </div>
        `);
        cell.append(badge);
      });

      // 显示成功提示
      const successMsg = $('<div class="alert alert-success mt-2 fade show">菜品已添加到菜单</div>');
      $('#addToMenuBtn').after(successMsg);

      // 3秒后自动隐藏提示
      setTimeout(function() {
        successMsg.alert('close');
      }, 3000);

      // 滚动回表格区域
      $('html, body').animate({
        scrollTop: $('#menu-' + date + '-' + meal).offset().top - 100
      }, 300);
    });

    // 删除菜品
    $(document).on('click', '.remove-recipe', function() {
      const badge = $(this).closest('.recipe-badge');
      const date = $(this).data('date');
      const meal = $(this).data('meal');
      const id = $(this).data('id');

      // 如果当前正在编辑这个单元格，取消选中对应的复选框
      if ($('#current_date').val() === date && $('#current_meal').val() === meal) {
        $('#recipe_' + id).prop('checked', false);
      }

      badge.remove();

      // 如果单元格中没有菜品了，显示"未选择"
      const cell = $('#menu-' + date + '-' + meal);
      if (cell.find('.recipe-badge').length === 0) {
        cell.html('<span class="text-muted">未选择</span>');
      }
    });

    // 快速搜索食谱
    $('#recipeSearch').on('keyup', function() {
      const searchText = $(this).val().toLowerCase();

      if (searchText.length < 2) {
        $('.recipe-checkbox').parent().show();
        return;
      }

      // 遍历所有食谱
      $('.recipe-checkbox').each(function() {
        const recipeName = $(this).data('name').toLowerCase();
        if (recipeName.includes(searchText)) {
          $(this).parent().show();

          // 展开包含匹配食谱的分类
          const collapseId = $(this).closest('.collapse').attr('id');
          $('#' + collapseId).addClass('show');
        } else {
          $(this).parent().hide();
        }
      });
    });

    // 表单提交前确认
    $('#recipeForm').on('submit', function(e) {
      // 检查是否有选择的菜品
      let hasRecipes = false;
      $('.menu-cell').each(function() {
        if ($(this).find('.recipe-badge').length > 0) {
          hasRecipes = true;
          return false; // 跳出循环
        }
      });

      if (!hasRecipes) {
        if (!confirm('您还没有选择任何菜品，确定要保存空菜单吗？')) {
          e.preventDefault();
          return false;
        }
      }

      // 显示加载提示
      $('#saveBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 保存中...');
    });
  });
</script>
{% endblock %}
