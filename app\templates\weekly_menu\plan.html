{% extends 'base.html' %}

{% block title %}周菜单计划 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/weekly_menu_modal.css') }}">
<style>
  /* 加载状态样式 */
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* 保存状态提示 */
  .save-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 10px 20px;
    border-radius: 4px;
    display: none;
    z-index: 9998;
  }

  .save-status.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  .save-status.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  .save-status.info {
    background-color: #cce5ff;
    color: #004085;
    border: 1px solid #b8daff;
  }

  /* 页面特定样式 */
  .table th, .table td {
    vertical-align: middle;
    text-align: center;
  }

  .menu-input {
    cursor: pointer;
    background-color: #fff;
    transition: all 0.3s;
  }

  .menu-input:hover {
    background-color: #f8f9fa;
  }

  .menu-input.selected {
    background-color: #e8f4e8;
    border-color: #28a745;
  }

  .menu-input.readonly {
    cursor: not-allowed;
    background-color: #f8f9fa;
    opacity: 0.8;
  }

  /* 周次选择器样式 */
  .week-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
  }

  .week-item {
    padding: 8px 15px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    cursor: pointer;
    transition: all 0.2s;
    background-color: #f8f9fa;
    position: relative;
  }

  .week-item:hover {
    border-color: #adb5bd;
    background-color: #e9ecef;
  }

  .week-item.active {
    border-color: #28a745;
    background-color: #e8f4e8;
    font-weight: 500;
  }

  .week-item.disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .week-item::after {
    content: attr(data-status);
    position: absolute;
    top: -8px;
    right: -8px;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
  }

  .week-item[data-status="已发布"]::after {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
  }

  .week-item[data-status="计划中"]::after {
    background-color: #fff3cd;
    border-color: #ffeeba;
    color: #856404;
  }

  /* 状态徽章样式 */
  .status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-left: 10px;
    vertical-align: middle;
  }

  .status-badge.editable {
    background-color: #d4edda;
    color: #155724;
  }

  .status-badge.readonly {
    background-color: #f8f9fa;
    color: #6c757d;
  }

  .status-badge.published {
    background-color: #cce5ff;
    color: #004085;
  }

  /* 菜品卡片样式 */
  .recipe-card {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #dee2e6;
    background-color: #fff;
  }

  .recipe-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-color: #adb5bd;
  }

  .recipe-card.selected {
    background-color: #e8f4e8;
    border-color: #28a745;
  }

  /* 已选菜品标签样式 */
  .selected-recipe-tag {
    display: inline-flex;
    align-items: center;
    margin: 0.25rem;
    padding: 0.35rem 0.75rem;
    background-color: #e8f4e8;
    border: 1px solid #28a745;
    border-radius: 20px;
    font-size: 0.875rem;
  }

  .selected-recipe-tag .remove-btn {
    margin-left: 0.5rem;
    cursor: pointer;
    font-size: 1.2rem;
    line-height: 1;
    color: #dc3545;
  }

  .selected-recipe-tag .remove-btn:hover {
    color: #c82333;
  }

  /* 分类导航样式 */
  .nav-tabs {
    border-bottom: 2px solid #dee2e6;
  }

  .nav-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    margin-bottom: -2px;
    color: #495057;
    padding: 0.5rem 1rem;
  }

  .nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #28a745;
  }

  .nav-tabs .nav-link.active {
    color: #28a745;
    border-bottom-color: #28a745;
    font-weight: 500;
  }

  /* 滚动区域样式 */
  .modal-body {
    max-height: calc(100vh - 210px);
    overflow-y: auto;
  }

  .recipe-list {
    padding: 1rem 0;
  }

  /* 自定义菜品输入框样式 */
  .input-group {
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  }

  #customDishInput {
    border-right: none;
  }

  #customDishInput:focus {
    box-shadow: none;
    border-color: #28a745;
  }

  #addCustomDishBtn {
    border-color: #ced4da;
    color: #28a745;
  }

  #addCustomDishBtn:hover {
    background-color: #28a745;
    border-color: #28a745;
    color: #fff;
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <!-- 加载状态遮罩 -->
  <div class="loading-overlay">
    <div class="loading-spinner"></div>
  </div>

  <!-- 保存状态提示 -->
  <div class="save-status"></div>

  <!-- 隐藏字段 -->
  <input type="hidden" id="csrf-token" value="{{ csrf_token() }}">
  <input type="hidden" id="area-id" value="{{ area_id }}">
  <input type="hidden" id="week-start" value="{{ week_start }}">
  <input type="hidden" id="base-url" value="{{ url_for('weekly_menu.plan', _external=True) }}">
  <input type="hidden" id="is-editable" value="{{ 'true' if is_editable else 'false' }}">
  {% if existing_menu %}
  <input type="hidden" id="menu-id" value="{{ existing_menu.id }}">
  <input type="hidden" id="menu-status" value="{{ existing_menu.status }}">
  {% endif %}

  <!-- 提示信息模态框 -->
  <div class="modal fade" id="infoModal" tabindex="-1" role="dialog" aria-labelledby="infoModalTitle" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="infoModalTitle">提示信息</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body" id="infoModalBody">
          <!-- 提示内容将在这里显示 -->
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" data-dismiss="modal">我知道了</button>
        </div>
      </div>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col-md-8">
      <h2>
        {{ area.name }}周菜单计划
        {% if is_editable %}
          <span class="status-badge editable">可编辑</span>
        {% elif existing_menu and existing_menu.status == '已发布' %}
          <span class="status-badge published">已发布</span>
        {% else %}
          <span class="status-badge readonly">只读</span>
        {% endif %}
      </h2>
      <p class="text-muted">{{ week_start }} 至 {{ week_end }}</p>

      <!-- 周次导航 -->
      <div class="week-selector">
        {% for week_info in available_weeks %}
          <div class="week-item {% if week_info.start_date == week_start %}active{% endif %} {% if not week_info.is_editable and not week_info.is_viewable %}disabled{% endif %}"
               data-week="{{ week_info.start_date }}"
               data-editable="{{ week_info.is_editable|lower }}"
               data-status="{{ week_info.status }}">
            {{ week_info.display_text }}
          </div>
        {% endfor %}
      </div>
    </div>
    <div class="col-md-4 text-right">
      {% if is_editable %}
        <button type="button" id="saveMenuBtn" class="btn btn-success mr-2">
          <i class="fas fa-save"></i> 保存菜单
        </button>

        {% if existing_menu %}
          <button type="button" id="publishMenuBtn" class="btn btn-primary mr-2">
            <i class="fas fa-check-circle"></i> 发布菜单
          </button>
          <button type="button" id="deleteMenuBtn" class="btn btn-danger mr-2">
            <i class="fas fa-trash"></i> 删除菜单
          </button>
        {% endif %}
      {% elif existing_menu and existing_menu.status == '已发布' %}
        <button type="button" id="unpublishMenuBtn" class="btn btn-warning mr-2">
          <i class="fas fa-undo"></i> 解除发布
        </button>
        <button type="button" id="printMenuBtn" class="btn btn-info mr-2">
          <i class="fas fa-print"></i> 打印菜单
        </button>
      {% endif %}

      <a href="{{ url_for('weekly_menu.index') }}" class="btn btn-secondary mr-2">
        <i class="fas fa-arrow-left"></i> 返回列表
      </a>

      <!-- 新版周菜单入口 -->
      <a href="{{ url_for('weekly_menu_v2.plan', area_id=area_id, week_start=week_start) }}" class="btn btn-primary">
        <i class="fas fa-rocket"></i> 使用新版周菜单
      </a>
    </div>
  </div>

  <!-- 这里不需要重复的隐藏字段，已经在页面顶部定义了 -->

  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-body">
          {% if not existing_menu and is_editable %}
          <!-- 未创建周菜单时显示的提示和创建按钮 -->
          <div id="createMenuPrompt" class="text-center py-5">
            <div class="alert alert-info mb-4">
              <h4 class="alert-heading">尚未创建周菜单</h4>
              <p>您当前选择的周次还没有创建周菜单。请先创建周菜单，然后再添加菜谱。</p>
            </div>
            <button id="createMenuBtn" class="btn btn-primary btn-lg">
              <i class="fas fa-plus-circle"></i> 创建周菜单
            </button>
          </div>
          {% endif %}

          <form id="menuForm" method="post" {% if not existing_menu and is_editable %}style="display: none;"{% endif %}>
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <input type="hidden" name="menu_data" id="menuData" value="">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th style="width: 15%">日期</th>
                  <th style="width: 28%">早餐</th>
                  <th style="width: 28%">午餐</th>
                  <th style="width: 28%">晚餐</th>
                </tr>
              </thead>
              <tbody>
                {% for date_str, day_data in week_dates.items() %}
                <tr>
                  <td class="date-column">
                    <div class="font-weight-bold">{{ day_data.weekday }}</div>
                    <div>{{ date_str }}</div>
                  </td>
                  {% for meal_type in ['早餐', '午餐', '晚餐'] %}
                  <td>
                    <div class="form-group mb-0">
                      <textarea class="form-control menu-input {% if not is_editable %}readonly{% endif %}"
                               data-date="{{ date_str }}"
                               data-meal="{{ meal_type }}"
                               {% if not is_editable %}readonly{% endif %}>{{ menu_data.get(date_str, {}).get(meal_type, []) | map(attribute='name') | join(', ') }}</textarea>
                    </div>
                  </td>
                  {% endfor %}
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 删除菜单模态框 -->
<div class="modal fade" id="deleteMenuModal" tabindex="-1" role="dialog" aria-labelledby="deleteMenuModalTitle" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white">
        <h5 class="modal-title" id="deleteMenuModalTitle">删除周菜单</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="alert alert-danger">
          <i class="fas fa-exclamation-triangle"></i> 警告：此操作不可恢复！
        </div>
        <p>您确定要删除这个周菜单吗？删除后将无法恢复。</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
        <form id="deleteMenuForm" method="post" action="{{ url_for('weekly_menu.delete_menu', id=existing_menu.id if existing_menu else 0) }}">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
          <button type="submit" class="btn btn-danger">
            <i class="fas fa-trash"></i> 确认删除
          </button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- 解除发布模态框 -->
<div class="modal fade" id="unpublishMenuModal" tabindex="-1" role="dialog" aria-labelledby="unpublishMenuModalTitle" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header bg-warning">
        <h5 class="modal-title" id="unpublishMenuModalTitle">解除发布周菜单</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-triangle"></i> 注意：解除发布后，该菜单将不再显示在已发布菜单列表中。
        </div>
        <p>您确定要解除发布这个周菜单吗？解除发布后，您可以继续编辑该菜单。</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
        <form id="unpublishMenuForm" method="post" action="{{ url_for('weekly_menu.unpublish', id=existing_menu.id if existing_menu else 0) }}">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
          <button type="submit" class="btn btn-warning">
            <i class="fas fa-undo"></i> 确认解除发布
          </button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- 打印预览模态框 -->
<div class="modal fade" id="printPreviewModal" tabindex="-1" role="dialog" aria-labelledby="printPreviewModalTitle" aria-hidden="true">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="printPreviewModalTitle">打印预览</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div id="printPreviewContent" class="p-3">
          <!-- 打印内容将在这里动态生成 -->
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
        <button type="button" class="btn btn-primary" id="printMenuConfirmBtn">
          <i class="fas fa-print"></i> 打印
        </button>
      </div>
    </div>
  </div>
</div>

<!-- 菜单编辑模态框 -->
<div class="modal fade" id="menuModal" tabindex="-1" role="dialog" aria-labelledby="modalTitle" aria-hidden="true" data-backdrop="static" data-keyboard="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modalTitle"></h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <!-- 已选菜品 -->
        <div class="mb-4">
          <h6>已选菜品：</h6>
          <div id="selectedDishes" class="d-flex flex-wrap"></div>
        </div>

        <!-- 自定义菜品输入 -->
        <div class="mb-4">
          <div class="input-group">
            <input type="text" class="form-control" id="customDishInput" placeholder="输入自定义菜品名称">
            <div class="input-group-append">
              <button class="btn btn-outline-secondary" type="button" id="addCustomDishBtn">添加</button>
            </div>
          </div>
        </div>

        <!-- 分类导航 -->
        <ul class="nav nav-tabs mb-3" id="recipeCategories" role="tablist">
          <li class="nav-item">
            <a class="nav-link active" id="all-tab" data-toggle="tab" href="#all" role="tab"
               data-category="all" aria-selected="true">全部</a>
          </li>
          {% for category in recipes_by_category.keys() %}
          <li class="nav-item">
            <a class="nav-link" id="{{ category }}-tab" data-toggle="tab" href="#{{ category }}"
               data-category="{{ category }}" role="tab" aria-selected="false">{{ category }}</a>
          </li>
          {% endfor %}
        </ul>

        <!-- 系统菜品列表 -->
        <div class="tab-content recipe-list" id="recipeCategoriesContent">
          <!-- 全部分类 -->
          <div class="tab-pane fade show active" id="all" role="tabpanel">
            <div class="row">
              {% for category, recipes in recipes_by_category.items() %}
                {% for recipe in recipes %}
                <div class="col-md-4 col-sm-6 mb-3">
                  <div class="recipe-card card h-100"
                       data-category="{{ category }}"
                       data-id="{{ recipe.id }}"
                       onclick="selectDish('{{ recipe.id }}', '{{ recipe.name }}')">
                    {% if recipe.image_path %}
                    <img src="{{ url_for('static', filename=recipe.image_path.replace('\\', '/')) }}"
                         class="card-img-top recipe-img"
                         alt="{{ recipe.name }}">
                    {% endif %}
                    <div class="card-body p-2 text-center">
                      {{ recipe.name }}
                    </div>
                  </div>
                </div>
                {% endfor %}
              {% endfor %}
            </div>
          </div>

          <!-- 各个分类 -->
          {% for category, recipes in recipes_by_category.items() %}
          <div class="tab-pane fade" id="{{ category }}" role="tabpanel">
            <div class="row">
              {% for recipe in recipes %}
              <div class="col-md-4 col-sm-6 mb-3">
                <div class="recipe-card card h-100"
                     data-category="{{ category }}"
                     data-id="{{ recipe.id }}"
                     onclick="selectDish('{{ recipe.id }}', '{{ recipe.name }}')">
                  {% if recipe.image_path %}
                  <img src="{{ url_for('static', filename=recipe.image_path.replace('\\', '/')) }}"
                       class="card-img-top recipe-img"
                       alt="{{ recipe.name }}">
                  {% endif %}
                  <div class="card-body p-2 text-center">
                    {{ recipe.name }}
                  </div>
                </div>
              </div>
              {% endfor %}
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal" id="cancelSelectionBtn">取消</button>
        <button type="button" class="btn btn-primary" id="saveSelectionBtn">确定</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
// 全局变量
let menuModal;
const selectedDishes = new Map();

// 确保在文档加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
  // 初始化模态框
  const modalElement = document.getElementById('menuModal');
  if (!modalElement) {
    console.error('未找到模态框元素');
    return;
  }

  // 初始化 Bootstrap 模态框
  menuModal = new bootstrap.Modal(modalElement, {
    backdrop: 'static',
    keyboard: true
  });

  // 阻止模态框内容点击事件冒泡
  const modalContent = document.querySelector('.modal-content');
  if (modalContent) {
    modalContent.addEventListener('click', function(e) {
      e.stopPropagation();
    });
  }

  // 初始化其他事件监听器
  initializeEventListeners();

  // 初始化菜单数据
  window.menuData = {};

  // 显示提示信息
  const menuStatus = document.getElementById('menu-status');
  const isEditable = document.getElementById('is-editable').value === 'true';

  if (menuStatus && menuStatus.value === '已发布') {
    showInfoModal('菜单已发布', `
      <div class="alert alert-info">
        <p><strong>提示：</strong>当前周菜单已发布，无法修改。</p>
        <p>如果需要修改，请先点击"解除发布"按钮。</p>
        <p>解除发布后，您需要从周一开始重新安排菜单。</p>
      </div>
    `);
  } else if (!isEditable) {
    showInfoModal('菜单只读', `
      <div class="alert alert-warning">
        <p><strong>提示：</strong>当前周菜单为只读状态，无法修改。</p>
        <p>您可以查看上周菜单，但不能修改。</p>
        <p>如果需要规划菜单，请选择本周（未发布）或下周。</p>
      </div>
    `);
  }
});

// 初始化事件监听器
function initializeEventListeners() {
  // 菜单输入框点击事件
  document.querySelectorAll('.menu-input').forEach(input => {
    input.addEventListener('click', function() {
      if (!this.classList.contains('readonly')) {
        // 标记当前选中的输入框
        document.querySelectorAll('.menu-input').forEach(i => i.classList.remove('selected'));
        this.classList.add('selected');
        showModal(this);
      }
    });
  });

  // 周次导航点击事件
  document.querySelectorAll('.week-item').forEach(item => {
    item.addEventListener('click', function() {
      const weekStart = this.dataset.week;
      const isEditable = this.dataset.editable === 'true';

      // 如果当前有未保存的更改，提示用户
      if (hasUnsavedChanges()) {
        if (confirm('当前菜单有未保存的更改，切换周将丢失这些更改。是否继续？')) {
          navigateToWeek(weekStart);
        }
      } else {
        navigateToWeek(weekStart);
      }
    });
  });

  // 自定义菜品输入框回车事件
  const customDishInput = document.getElementById('customDishInput');
  if (customDishInput) {
    customDishInput.addEventListener('keydown', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        addCustomDish();
      }
    });
  }

  // 添加自定义菜品按钮点击事件
  const addCustomDishBtn = document.getElementById('addCustomDishBtn');
  if (addCustomDishBtn) {
    addCustomDishBtn.addEventListener('click', addCustomDish);
  }

  // 保存按钮点击事件
  const saveSelectionBtn = document.getElementById('saveSelectionBtn');
  if (saveSelectionBtn) {
    saveSelectionBtn.addEventListener('click', saveSelection);
  }

  // 取消按钮点击事件
  const cancelSelectionBtn = document.getElementById('cancelSelectionBtn');
  if (cancelSelectionBtn) {
    cancelSelectionBtn.addEventListener('click', function() {
      menuModal.hide();
    });
  }

  // 分类标签点击事件
  document.querySelectorAll('#recipeCategories .nav-link').forEach(tab => {
    tab.addEventListener('click', function(e) {
      e.preventDefault();
      const category = this.dataset.category;
      showRecipesByCategory(category);
    });
  });

  // 表单提交事件
  const menuForm = document.getElementById('menuForm');
  if (menuForm) {
    menuForm.addEventListener('submit', function(e) {
      e.preventDefault();
      document.getElementById('menuData').value = JSON.stringify(window.menuData || {});
      this.submit();
    });
  }

  // 保存菜单按钮点击事件
  const saveMenuBtn = document.getElementById('saveMenuBtn');
  if (saveMenuBtn) {
    saveMenuBtn.addEventListener('click', saveMenu);
  }

  // 发布菜单按钮点击事件
  const publishMenuBtn = document.getElementById('publishMenuBtn');
  if (publishMenuBtn) {
    publishMenuBtn.addEventListener('click', publishMenu);
  }

  // 解除发布按钮点击事件
  const unpublishMenuBtn = document.getElementById('unpublishMenuBtn');
  if (unpublishMenuBtn) {
    unpublishMenuBtn.addEventListener('click', unpublishMenu);
  }

  // 创建周菜单按钮点击事件
  const createMenuBtn = document.getElementById('createMenuBtn');
  if (createMenuBtn) {
    createMenuBtn.addEventListener('click', createMenu);
  }
}

// 显示模态框
function showModal(input) {
  if (!menuModal) {
    console.error('模态框未初始化');

    // 尝试重新初始化模态框
    const modalElement = document.getElementById('menuModal');
    if (modalElement) {
      try {
        menuModal = new bootstrap.Modal(modalElement, {
          backdrop: 'static',
          keyboard: true
        });
        console.log('模态框重新初始化成功');
      } catch (error) {
        console.error('模态框重新初始化失败:', error);
        return;
      }
    } else {
      console.error('未找到模态框元素');
      return;
    }
  }

  const date = input.dataset.date;
  const meal = input.dataset.meal;

  // 更新模态框标题
  const titleElement = document.getElementById('modalTitle');
  if (titleElement) {
    titleElement.textContent = `${date} ${meal}`;
  }

  // 清空已选菜品
  selectedDishes.clear();

  // 确保menuData已初始化
  if (!window.menuData) {
    window.menuData = {};
  }

  // 确保日期和餐次的数据结构存在
  if (!window.menuData[date]) {
    window.menuData[date] = {};
  }

  // 确保餐次的数据结构存在
  if (!window.menuData[date][meal]) {
    window.menuData[date][meal] = [];
  }

  // 加载当前已选菜品
  if (window.menuData[date][meal] && window.menuData[date][meal].length > 0) {
    window.menuData[date][meal].forEach(dish => {
      selectedDishes.set(dish.id, dish);
    });
  }

  // 更新已选菜品显示
  updateSelectedDishesList();

  // 显示模态框
  try {
    menuModal.show();

    // 聚焦到自定义菜品输入框
    setTimeout(() => {
      const customDishInput = document.getElementById('customDishInput');
      if (customDishInput) {
        customDishInput.focus();
      }
    }, 300);
  } catch (error) {
    console.error('显示模态框失败:', error);
    alert('显示菜品选择器失败，请刷新页面后重试。错误信息: ' + error.message);
  }
}

// 更新已选菜品列表
function updateSelectedDishesList() {
  const container = document.getElementById('selectedDishes');
  if (!container) return;

  container.innerHTML = '';

  selectedDishes.forEach(dish => {
    const tag = document.createElement('div');
    tag.className = 'selected-recipe-tag';
    tag.innerHTML = `
      ${dish.name}
      <span class="remove-btn" onclick="removeDish('${dish.id}')">&times;</span>
    `;
    container.appendChild(tag);
  });
}

// 选择菜品
function selectDish(id, name) {
  const card = document.querySelector(`.recipe-card[onclick*="${id}"]`);

  if (selectedDishes.has(id)) {
    removeDish(id);
    card?.classList.remove('selected');
  } else {
    // 检查是否是系统菜品（非自定义）
    const isSystemRecipe = !id.toString().startsWith('custom_');

    // 系统菜品使用数字ID，自定义菜品为null
    const recipeId = isSystemRecipe ? parseInt(id) : null;

    console.log(`选择菜品: ${name}, ID: ${id}, recipe_id: ${recipeId}, 是系统菜品: ${isSystemRecipe}`);

    // 创建包含所有必要字段的菜品对象
    selectedDishes.set(id, {
      id,
      name,
      recipe_id: recipeId
    });

    card?.classList.add('selected');
    updateSelectedDishesList();
  }
}

// 移除菜品
function removeDish(id) {
  selectedDishes.delete(id);
  updateSelectedDishesList();
}

// 添加自定义菜品
function addCustomDish() {
  const input = document.getElementById('customDishInput');
  const dishName = input.value.trim();

  if (!dishName) return;

  const customId = `custom_${Date.now()}`;
  selectedDishes.set(customId, {
    id: customId,
    name: dishName,
    recipe_id: null  // 自定义菜品的recipe_id为null
  });

  updateSelectedDishesList();
  input.value = '';
  input.focus();
}

// 保存选择
function saveSelection() {
  const currentInput = document.querySelector('.menu-input.selected');
  if (!currentInput) return;

  const date = currentInput.dataset.date;
  const meal = currentInput.dataset.meal;
  const dayOfWeek = getDayOfWeek(date);
  const weekly_menu_id = document.getElementById('menu-id')?.value || null;
  const now = new Date().toISOString().slice(0, 19).replace('T', ' '); // 当前时间，格式：YYYY-MM-DD HH:MM:SS

  // 确保数据结构存在
  if (!window.menuData) window.menuData = {};
  if (!window.menuData[date]) window.menuData[date] = {};

  // 保存选择的菜品，确保包含所有数据库字段
  window.menuData[date][meal] = Array.from(selectedDishes.values()).map(dish => {
    // 检查是否是系统菜品（非自定义）
    const isSystemRecipe = dish.id && !dish.id.toString().startsWith('custom_');

    // 确保recipe_id字段正确设置
    let recipeId = dish.recipe_id;
    if (recipeId === undefined) {
      // 如果recipe_id未定义，根据ID类型设置
      recipeId = isSystemRecipe ? parseInt(dish.id) : null;
    }

    console.log(`保存菜品: ${dish.name}, ID: ${dish.id}, recipe_id: ${recipeId}`);

    // 创建完整的数据对象，包含weekly_menu_recipes表的所有字段
    return {
      id: dish.id, // 保留原始ID
      weekly_menu_id: weekly_menu_id, // 周菜单ID（可能为null，将在保存时由后端创建）
      day_of_week: dayOfWeek, // 星期几（1-7）
      meal_type: meal, // 餐次类型
      recipe_id: recipeId, // 使用计算后的recipe_id
      recipe_name: dish.name, // 菜品名称
      created_at: now, // 创建时间
      updated_at: now, // 更新时间
      name: dish.name // 保留name字段用于显示
    };
  });

  // 更新输入框显示
  currentInput.value = Array.from(selectedDishes.values())
    .map(d => d.name)
    .join(', ');

  // 如果是第一次添加菜品，提示用户需要保存菜单
  if (!weekly_menu_id && selectedDishes.size > 0) {
    // 显示提示信息
    showSaveStatus('菜品已添加，请点击"保存菜单"按钮保存到数据库', 'info');
  }

  // 关闭模态框
  menuModal.hide();
}

// 按分类显示食谱
function showRecipesByCategory(category) {
  // 更新标签页状态
  document.querySelectorAll('#recipeCategories .nav-link').forEach(tab => {
    if (tab.dataset.category === category) {
      tab.classList.add('active');
    } else {
      tab.classList.remove('active');
    }
  });

  // 更新内容区域
  document.querySelectorAll('.tab-pane').forEach(pane => {
    if (pane.id === category) {
      pane.classList.add('show', 'active');
    } else {
      pane.classList.remove('show', 'active');
    }
  });
}

// 检查是否有未保存的更改
function hasUnsavedChanges() {
  // 这里可以实现更复杂的逻辑来检测未保存的更改
  // 简单实现：如果用户修改了任何输入框，则认为有未保存的更改
  return document.querySelector('.menu-input.selected') !== null;
}

// 导航到指定周
function navigateToWeek(weekStart) {
  // 使用 URLSearchParams 构建查询参数，更安全
  const baseUrl = document.getElementById('base-url').value;
  const areaId = document.getElementById('area-id').value;

  const url = new URL(baseUrl);
  url.searchParams.set('area_id', areaId);
  url.searchParams.set('week_start', weekStart);
  window.location.href = url.toString();
}

// 保存菜单数据到服务器
function saveMenu() {
  // 显示加载状态
  const loadingOverlay = document.querySelector('.loading-overlay');
  if (loadingOverlay) {
    loadingOverlay.style.display = 'flex';
  }

  // 禁用保存按钮
  const saveBtn = document.getElementById('saveMenuBtn');
  if (saveBtn) {
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
  }

  // 准备要发送的数据
  const area_id_val = document.getElementById('area-id').value;
  const week_start_val = document.getElementById('week-start').value;
  const weekly_menu_id = document.getElementById('menu-id')?.value || null;
  const now = new Date().toISOString().slice(0, 19).replace('T', ' '); // 当前时间，格式：YYYY-MM-DD HH:MM:SS

  // 同步所有输入框内容到menuData，无论是否被点击过
  syncAllInputsToMenuData(weekly_menu_id, now);

  const menuData = window.menuData;

  // 检查数据是否为空
  let hasData = false;
  for (const date in menuData) {
    for (const meal in menuData[date]) {
      if (menuData[date][meal] && menuData[date][meal].length > 0) {
        hasData = true;
        break;
      }
    }
    if (hasData) break;
  }

  if (!hasData) {
    showSaveStatus('没有菜单数据可保存', 'error');
    resetSaveButton();
    return;
  }

  console.log('准备发送的菜单数据:', menuData);

  // 发送AJAX请求
  fetch('/api/weekly-menu/week/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': '{{ csrf_token() }}'
    },
    body: JSON.stringify({
      area_id: area_id_val,
      week_start: week_start_val,
      menu_data: menuData
    })
  })
  .then(response => {
    // 记录响应状态
    console.log('响应状态:', response.status, response.statusText);

    // 尝试解析JSON响应
    return response.json().catch(error => {
      console.error('解析JSON响应失败:', error);
      // 返回一个错误对象
      return {
        success: false,
        message: `服务器返回了非JSON响应 (${response.status}: ${response.statusText})`
      };
    });
  })
  .then(data => {
    console.log('响应数据:', data);
    if (data.success) {
      // 更新菜单ID（如果是新创建的）
      if (data.weekly_menu_id) {
        document.getElementById('menu-id').value = data.weekly_menu_id;
      }

      // 获取周次信息
      const weekNumber = getWeekNumber(week_start_val);

      // 显示成功消息，包含周次信息
      const successMessage = `第${weekNumber}周菜单保存成功`;
      showSaveStatus(successMessage, 'success');

      // 显示详细信息模态框
      showInfoModal('保存成功', `
        <div class="alert alert-success">
          <p><strong>${successMessage}</strong></p>
          <p>菜单已按照您的安排保存到数据库。</p>
          <p>您可以继续编辑菜单，或点击"发布菜单"按钮发布。</p>
          <p>发布后的菜单将不能再修改，请确保菜单内容正确。</p>
        </div>
      `);
    } else {
      showSaveStatus('保存失败: ' + (data.message || '未知错误'), 'error');
      console.error('保存失败:', data);
    }
  })
  .catch(error => {
    showSaveStatus('保存请求失败: ' + error.message, 'error');
    console.error('保存请求失败:', error);
  })
  .finally(() => {
    resetSaveButton();
  });
}

// 获取日期所在的周数
function getWeekNumber(dateStr) {
  const date = new Date(dateStr);
  const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
  const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
  return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
}

// 工具函数：根据日期字符串返回周几（1=周一，7=周日）
function getDayOfWeek(dateStr) {
  const date = new Date(dateStr);
  let day = date.getDay(); // 0=周日, 1=周一, ..., 6=周六
  return day === 0 ? 7 : day;
}

// 同步所有输入框内容到menuData，确保包含所有字段
function syncAllInputsToMenuData(weekly_menu_id, timestamp) {
  // 保存原始的menuData，以便我们可以保留recipe_id信息
  const originalMenuData = window.menuData || {};

  // 初始化为对象格式，符合后端API期望
  window.menuData = {};

  // 遍历所有输入框
  document.querySelectorAll('.menu-input').forEach(input => {
    const date = input.dataset.date;
    const meal = input.dataset.meal;
    const value = input.value.trim();

    // 计算day_of_week（1=周一，7=周日）
    const dayOfWeek = getDayOfWeek(date);

    // 确保日期和餐次的数据结构存在
    if (!window.menuData[date]) {
      window.menuData[date] = {};
    }

    // 即使输入框为空，也创建空数组
    if (!window.menuData[date][meal]) {
      window.menuData[date][meal] = [];
    }

    // 如果有内容，解析并添加菜品
    if (value) {
      // 检查原始数据中是否有这个日期和餐次的数据
      const originalDishes = originalMenuData[date] && originalMenuData[date][meal]
        ? originalMenuData[date][meal]
        : [];

      // 获取输入框中的菜品名称数组
      const inputRecipeNames = value.split(',').map(name => name.trim()).filter(name => name);

      // 如果原始数据中有菜品，并且数量与输入框中的菜品数量相同，则使用原始数据
      if (originalDishes.length > 0 && originalDishes.length === inputRecipeNames.length) {
        // 检查原始数据中的菜品名称是否与输入框中的菜品名称匹配
        const namesMatch = originalDishes.every((dish, index) =>
          dish.recipe_name === inputRecipeNames[index] || dish.name === inputRecipeNames[index]
        );

        if (namesMatch) {
          // 使用原始数据，但确保所有必要字段都存在
          window.menuData[date][meal] = originalDishes.map(dish => {
            return {
              id: dish.id,
              weekly_menu_id: weekly_menu_id,
              day_of_week: dayOfWeek,
              meal_type: meal,
              recipe_id: dish.recipe_id, // 保留原始的recipe_id
              recipe_name: dish.recipe_name || dish.name,
              created_at: timestamp,
              updated_at: timestamp,
              name: dish.name || dish.recipe_name
            };
          });

          console.log(`使用原始数据 - 日期: ${date}, 餐次: ${meal}, 菜品数: ${window.menuData[date][meal].length}`);
          continue; // 跳过后续处理
        }
      }

      // 如果无法使用原始数据，则将输入框内容解析为菜品数组
      console.log(`解析输入框内容 - 日期: ${date}, 餐次: ${meal}, 内容: ${value}`);
      window.menuData[date][meal] = inputRecipeNames.map((recipeName, index) => {
        // 尝试在原始数据中查找匹配的菜品
        const matchingDish = originalDishes.find(dish =>
          dish.recipe_name === recipeName || dish.name === recipeName
        );

        if (matchingDish) {
          // 如果找到匹配的菜品，使用它的recipe_id
          console.log(`找到匹配的菜品: ${recipeName}, recipe_id: ${matchingDish.recipe_id}`);
          return {
            id: matchingDish.id,
            weekly_menu_id: weekly_menu_id,
            day_of_week: dayOfWeek,
            meal_type: meal,
            recipe_id: matchingDish.recipe_id, // 使用匹配菜品的recipe_id
            recipe_name: recipeName,
            created_at: timestamp,
            updated_at: timestamp,
            name: recipeName
          };
        } else {
          // 如果没有找到匹配的菜品，创建一个新的自定义菜品
          console.log(`创建新的自定义菜品: ${recipeName}`);
          return {
            id: `custom_${date}_${meal}_${index}`,
            weekly_menu_id: weekly_menu_id,
            day_of_week: dayOfWeek,
            meal_type: meal,
            recipe_id: null, // 自定义菜品的recipe_id为null
            recipe_name: recipeName,
            created_at: timestamp,
            updated_at: timestamp,
            name: recipeName
          };
        }
      });
    } else {
      // 即使输入框为空，也创建一个空数组
      window.menuData[date][meal] = [];
    }
  });

  console.log('同步后的菜单数据:', window.menuData);
}

// 发布菜单
function publishMenu() {
  // 先保存菜单，然后发布
  if (!window.menuData) {
    // 如果没有菜单数据，提示用户先保存
    showSaveStatus('请先保存菜单后再发布', 'error');
    return;
  }

  // 确认发布
  if (!confirm('发布后的菜单将不能再修改。确定要发布吗？')) {
    return;
  }

  // 显示加载状态
  const loadingOverlay = document.querySelector('.loading-overlay');
  if (loadingOverlay) {
    loadingOverlay.style.display = 'flex';
  }

  // 禁用按钮
  const publishBtn = document.getElementById('publishMenuBtn');
  if (publishBtn) {
    publishBtn.disabled = true;
    publishBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发布中...';
  }

  // 获取菜单ID
  const menuIdElement = document.getElementById('menu-id');
  const menuId = menuIdElement ? menuIdElement.value : null;

  if (!menuId) {
    showSaveStatus('请先保存菜单后再发布', 'error');
    resetPublishButton();
    return;
  }

  // 创建表单并提交
  const form = document.createElement('form');
  form.method = 'POST';
  form.action = `/weekly-menu/${menuId}/publish`;

  // 添加CSRF令牌
  const csrfInput = document.createElement('input');
  csrfInput.type = 'hidden';
  csrfInput.name = 'csrf_token';
  csrfInput.value = document.getElementById('csrf-token').value;
  form.appendChild(csrfInput);

  // 添加到文档并提交
  document.body.appendChild(form);
  form.submit();
}

// 解除发布菜单
function unpublishMenu() {
  // 确认解除发布
  if (!confirm('解除发布后，您需要从周一开始重新安排菜单。确定要解除发布吗？')) {
    return;
  }

  // 显示加载状态
  const loadingOverlay = document.querySelector('.loading-overlay');
  if (loadingOverlay) {
    loadingOverlay.style.display = 'flex';
  }

  // 禁用按钮
  const unpublishBtn = document.getElementById('unpublishMenuBtn');
  if (unpublishBtn) {
    unpublishBtn.disabled = true;
    unpublishBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
  }

  // 获取菜单ID
  const menuIdElement = document.getElementById('menu-id');
  const menuId = menuIdElement ? menuIdElement.value : null;

  if (!menuId) {
    showSaveStatus('无法找到菜单信息', 'error');
    resetUnpublishButton();
    return;
  }

  // 创建表单并提交
  const form = document.createElement('form');
  form.method = 'POST';
  form.action = `/weekly-menu/${menuId}/unpublish`;

  // 添加CSRF令牌
  const csrfInput = document.createElement('input');
  csrfInput.type = 'hidden';
  csrfInput.name = 'csrf_token';
  csrfInput.value = document.getElementById('csrf-token').value;
  form.appendChild(csrfInput);

  // 添加到文档并提交
  document.body.appendChild(form);
  form.submit();
}

// 创建周菜单
function createMenu() {
  // 显示加载状态
  let loadingOverlay = document.querySelector('.loading-overlay');

  // 如果不存在加载状态元素，创建一个
  if (!loadingOverlay) {
    loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'loading-overlay';
    loadingOverlay.innerHTML = `
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">加载中...</span>
      </div>
    `;
    loadingOverlay.style.position = 'fixed';
    loadingOverlay.style.top = '0';
    loadingOverlay.style.left = '0';
    loadingOverlay.style.width = '100%';
    loadingOverlay.style.height = '100%';
    loadingOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    loadingOverlay.style.display = 'flex';
    loadingOverlay.style.justifyContent = 'center';
    loadingOverlay.style.alignItems = 'center';
    loadingOverlay.style.zIndex = '9999';
    document.body.appendChild(loadingOverlay);
  } else {
    loadingOverlay.style.display = 'flex';
  }

  // 禁用创建按钮
  const createBtn = document.getElementById('createMenuBtn');
  if (createBtn) {
    createBtn.disabled = true;
    createBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 创建中...';
  }

  // 准备要发送的数据
  const area_id_val = document.getElementById('area-id').value;
  const week_start_val = document.getElementById('week-start').value;

  // 获取CSRF Token
  let csrfToken = '';
  // 尝试从meta标签获取
  const metaToken = document.querySelector('meta[name="csrf-token"]');
  if (metaToken) {
    csrfToken = metaToken.getAttribute('content');
  }
  // 尝试从隐藏输入框获取
  else {
    const csrfInput = document.querySelector('input[name="csrf_token"]');
    if (csrfInput) {
      csrfToken = csrfInput.value;
    }
  }

  if (!csrfToken) {
    console.error('无法获取CSRF Token');
    showSaveStatus('创建失败：无法获取CSRF Token', 'error');
    return;
  }

  // 发送AJAX请求创建空的周菜单
  fetch('/api/weekly-menu/week/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': csrfToken
    },
    body: JSON.stringify({
      area_id: area_id_val,
      week_start: week_start_val,
      menu_data: {} // 空菜单数据
    })
  })
  .then(response => {
    // 记录响应状态
    console.log('响应状态:', response.status, response.statusText);

    // 尝试解析JSON响应
    return response.json().catch(error => {
      console.error('解析JSON响应失败:', error);
      // 返回一个错误对象
      return {
        success: false,
        message: `服务器返回了非JSON响应 (${response.status}: ${response.statusText})`
      };
    });
  })
  .then(data => {
    console.log('响应数据:', data);
    if (data.success) {
      // 更新菜单ID
      if (data.weekly_menu_id) {
        // 检查是否已存在菜单ID输入框
        let menuIdInput = document.getElementById('menu-id');

        if (!menuIdInput) {
          // 创建隐藏的菜单ID输入框
          menuIdInput = document.createElement('input');
          menuIdInput.type = 'hidden';
          menuIdInput.id = 'menu-id';
          document.body.appendChild(menuIdInput);
        }

        // 设置菜单ID值
        menuIdInput.value = data.weekly_menu_id;

        // 隐藏创建提示，显示菜单表单
        const createPrompt = document.getElementById('createMenuPrompt');
        if (createPrompt) {
          createPrompt.style.display = 'none';
        }

        const menuForm = document.getElementById('menuForm');
        if (menuForm) {
          menuForm.style.display = 'block';
        }

        // 显示成功消息
        showSaveStatus('周菜单创建成功，现在您可以添加菜谱了', 'success');

        // 添加发布菜单按钮（如果不存在）
        if (!document.getElementById('publishMenuBtn')) {
          // 尝试找到操作区域
          let actionArea = document.querySelector('.col-md-4.text-right');

          // 如果找不到指定的操作区域，尝试找到保存按钮的父元素
          if (!actionArea) {
            const saveBtn = document.getElementById('saveMenuBtn');
            if (saveBtn && saveBtn.parentNode) {
              actionArea = saveBtn.parentNode;
            }
          }

          // 如果找到了操作区域，添加发布按钮
          if (actionArea) {
            const publishBtn = document.createElement('button');
            publishBtn.type = 'button';
            publishBtn.id = 'publishMenuBtn';
            publishBtn.className = 'btn btn-primary mr-2';
            publishBtn.innerHTML = '<i class="fas fa-check-circle"></i> 发布菜单';
            publishBtn.addEventListener('click', publishMenu);

            // 插入到保存按钮后面
            const saveBtn = document.getElementById('saveMenuBtn');
            if (saveBtn) {
              saveBtn.parentNode.insertBefore(publishBtn, saveBtn.nextSibling);
            } else {
              actionArea.appendChild(publishBtn);
            }
          }
        }

        // 刷新页面以显示新创建的菜单
        showSaveStatus('周菜单创建成功，页面将在3秒后刷新...', 'success');
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      }
    } else {
      // 构建详细的错误消息
      let errorMessage = '创建周菜单失败';

      if (data.message) {
        errorMessage += ': ' + data.message;
      }

      // 添加可能的解决方案
      errorMessage += '<br><br>可能的解决方案:<br>';
      errorMessage += '1. 检查网络连接<br>';
      errorMessage += '2. 确保您有创建周菜单的权限<br>';
      errorMessage += '3. 刷新页面后重试<br>';
      errorMessage += '4. 联系系统管理员';

      // 显示错误消息
      showInfoModal('创建失败', `<div class="alert alert-danger">${errorMessage}</div>`);
      showSaveStatus('创建周菜单失败', 'error');
      console.error('创建失败:', data);

      // 恢复创建按钮状态
      if (createBtn) {
        createBtn.disabled = false;
        createBtn.innerHTML = '<i class="fas fa-plus-circle"></i> 创建周菜单';
      }
    }
  })
  .catch(error => {
    // 构建详细的错误消息
    let errorMessage = '创建周菜单请求失败';

    if (error.message) {
      errorMessage += ': ' + error.message;
    }

    // 添加可能的解决方案
    errorMessage += '<br><br>可能的解决方案:<br>';
    errorMessage += '1. 检查网络连接<br>';
    errorMessage += '2. 确保服务器正在运行<br>';
    errorMessage += '3. 刷新页面后重试<br>';
    errorMessage += '4. 联系系统管理员';

    // 显示错误消息
    showInfoModal('请求失败', `<div class="alert alert-danger">${errorMessage}</div>`);
    showSaveStatus('创建周菜单请求失败', 'error');
    console.error('创建请求失败:', error);

    // 恢复创建按钮状态
    if (createBtn) {
      createBtn.disabled = false;
      createBtn.innerHTML = '<i class="fas fa-plus-circle"></i> 创建周菜单';
    }
  })
  .finally(() => {
    // 隐藏加载状态
    if (loadingOverlay) {
      loadingOverlay.style.display = 'none';
    }
  });
}

// 显示保存状态
function showSaveStatus(message, type) {
  const statusElement = document.querySelector('.save-status');
  if (!statusElement) return;

  statusElement.textContent = message;

  // 设置类名
  statusElement.className = 'save-status';

  // 根据类型添加不同的样式类
  switch (type) {
    case 'success':
      statusElement.classList.add('success');
      break;
    case 'error':
      statusElement.classList.add('error');
      break;
    case 'info':
      statusElement.classList.add('info');
      break;
    default:
      statusElement.classList.add(type);
  }

  statusElement.style.display = 'block';

  // 3秒后隐藏
  setTimeout(() => {
    statusElement.style.display = 'none';
  }, 3000);
}

// 显示提示信息模态框
function showInfoModal(title, message) {
  // 检查模态框是否存在
  let modal = document.getElementById('infoModal');

  // 如果模态框不存在，创建一个
  if (!modal) {
    modal = document.createElement('div');
    modal.id = 'infoModal';
    modal.className = 'modal fade';
    modal.setAttribute('tabindex', '-1');
    modal.setAttribute('role', 'dialog');
    modal.setAttribute('aria-labelledby', 'infoModalTitle');
    modal.setAttribute('aria-hidden', 'true');

    modal.innerHTML = `
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="infoModalTitle"></h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="infoModalBody">
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  // 设置模态框内容
  const modalTitle = document.getElementById('infoModalTitle');
  const modalBody = document.getElementById('infoModalBody');

  if (modalTitle) {
    modalTitle.textContent = title || '提示信息';
  }

  if (modalBody) {
    modalBody.innerHTML = message;
  }

  // 显示模态框
  try {
    // 尝试使用Bootstrap 5的方式
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
  } catch (error) {
    try {
      // 尝试使用jQuery的方式
      $(modal).modal('show');
    } catch (jqError) {
      console.error('无法显示模态框:', error, jqError);
      alert(title + '\n\n' + message.replace(/<[^>]*>/g, ''));
    }
  }
}

// 重置保存按钮状态
function resetSaveButton() {
  // 隐藏加载状态
  const loadingOverlay = document.querySelector('.loading-overlay');
  if (loadingOverlay) {
    loadingOverlay.style.display = 'none';
  }

  // 恢复保存按钮
  const saveBtn = document.getElementById('saveMenuBtn');
  if (saveBtn) {
    saveBtn.disabled = false;
    saveBtn.innerHTML = '<i class="fas fa-save"></i> 保存菜单';
  }
}

// 重置发布按钮状态
function resetPublishButton() {
  // 隐藏加载状态
  const loadingOverlay = document.querySelector('.loading-overlay');
  if (loadingOverlay) {
    loadingOverlay.style.display = 'none';
  }

  // 恢复发布按钮
  const publishBtn = document.getElementById('publishMenuBtn');
  if (publishBtn) {
    publishBtn.disabled = false;
    publishBtn.innerHTML = '<i class="fas fa-check-circle"></i> 发布菜单';
  }
}

// 重置解除发布按钮状态
function resetUnpublishButton() {
  // 隐藏加载状态
  const loadingOverlay = document.querySelector('.loading-overlay');
  if (loadingOverlay) {
    loadingOverlay.style.display = 'none';
  }

  // 恢复解除发布按钮
  const unpublishBtn = document.getElementById('unpublishMenuBtn');
  if (unpublishBtn) {
    unpublishBtn.disabled = false;
    unpublishBtn.innerHTML = '<i class="fas fa-undo"></i> 解除发布';
  }
}
</script>
{% endblock %}






